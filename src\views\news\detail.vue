<script setup lang="ts">
defineOptions({ name: "NewsDetail" });

import { ref } from "vue";
import { useRoute } from "vue-router";
import { formatDateToEventString } from "@/utils/core/tools";
import { getNews } from "@/api/news";
import RichTextRenderer from "@/components/RichTextRenderer.vue";
import {
  getNewsTypeIcon,
  getNewsTypeText,
  type NewsItem
} from "./newsUtils";

const route = useRoute();
const data = ref({
  title: "",
  author: "",
  date: "",
  authorImage: "",
  content: "",
});

const init = async () => {
  const { id } = route.params;
  const res = await getNews({});
  const list: NewsItem[] = res?.data || res || [];
  const curMessage = list.find((item: NewsItem) => item.id == Number(id));
  if (curMessage) {
    data.value = {
      title: curMessage.title,
      // author: curMessage.author || "",
      author: getNewsTypeText(curMessage.news_type),
      date: formatDateToEventString(curMessage.updated_at || curMessage.created_at),
      // authorImage: curMessage.image ? getServerSideImageUrl(curMessage.image) : "",
      authorImage: getNewsTypeIcon(curMessage.news_type),
      content: curMessage.news_content || "",
    };
  }
};
</script>

<template>
  <ZPage :request="init">
    <div class="news-detail">
      <div class="header">
        <h1 class="title">{{ data.title }}</h1>
        <div class="meta">
          <div class="author">
            <img class="img" :src="`${data.authorImage} `" />
            <span class="name">{{ data.author }}</span>
          </div>
          <span class="date">{{ data.date }}</span>
        </div>
      </div>
      <div class="content">
        <RichTextRenderer :content="data.content"></RichTextRenderer>
      </div>
    </div>
  </ZPage>
</template>

<style scoped lang="scss">
.news-detail {
  background-color: #fff;
  font-family: Arial, sans-serif;
  color: #333;
  height: 100%;
  box-sizing: border-box;

  .header {
    padding-top: 16px;
    margin-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;

    .title {
      line-height: 1.4;
      align-self: stretch;
      margin-bottom: 12px;
      color: #222;

      /* tab文字和新闻标题 */
      font-family: Inter;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }

    .meta {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #888;

      .author {
        margin-right: 8px;
        display: flex;
        align-items: center;

        .img {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          margin-right: 6px;
        }

        .name {
          color: #999;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: #999;
          font-family: Inter;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }

      .date {
        color: #999;
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .content {
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 16px;
    background-color: #fff;
    color: #999;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
