<template>
  <div class="turnstile-widget">
    <div 
      :id="containerId" 
      class="turnstile-container"
      :class="{ 'loading': isLoading, 'error': hasError }"
    ></div>
    
    <!-- Loading state -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <span>Loading verification...</span>
    </div>
    
    <!-- Error state -->
    <div v-if="hasError" class="error-overlay">
      <div class="error-icon">⚠️</div>
      <span>{{ errorMessage }}</span>
      <button @click="retry" class="retry-btn">Retry</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { CloudflareMgr, CF_TURNSTILE_TYPE, type TurnstileResult } from '@/utils/CloudflareMgr';

// Props
interface Props {
  /** Site key for Turnstile */
  siteKey?: string;
  /** Theme */
  theme?: 'light' | 'dark' | 'auto';
  /** Size */
  size?: 'normal' | 'compact';
  /** Appearance */
  appearance?: 'always' | 'execute' | 'interaction-only';
  /** Language */
  language?: string;
  /** CF Type for verification */
  cfType?: CF_TURNSTILE_TYPE;
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'auto',
  size: 'normal',
  appearance: 'always',
  language: 'en',
  cfType: CF_TURNSTILE_TYPE.NONE
});

// Emits
const emit = defineEmits<{
  success: [token: string];
  error: [error: string];
  expired: [];
  reset: [];
}>();

// State
const containerId = ref(`turnstile-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
const isLoading = ref(false);
const hasError = ref(false);
const errorMessage = ref('');
const widgetId = ref<string | null>(null);

// Cloudflare manager instance
const cfManager = CloudflareMgr.instance;

// Initialize Turnstile
const initTurnstile = async () => {
  if (widgetId.value) {
    // Clean up existing widget
    cfManager.remove(widgetId.value);
    widgetId.value = null;
  }

  isLoading.value = true;
  hasError.value = false;
  errorMessage.value = '';

  try {
    // Wait for next tick to ensure DOM is ready
    await nextTick();
    
    // Render Turnstile widget
    const id = await cfManager.renderTurnstile(
      containerId.value,
      props.cfType,
      handleVerificationResult,
      {
        siteKey: props.siteKey,
        theme: props.theme,
        size: props.size,
        appearance: props.appearance,
        language: props.language
      }
    );

    if (id) {
      widgetId.value = id;
      console.log('✅ Turnstile widget initialized:', id);
    } else {
      throw new Error('Failed to create Turnstile widget');
    }
  } catch (error) {
    console.error('❌ Failed to initialize Turnstile:', error);
    hasError.value = true;
    errorMessage.value = error instanceof Error ? error.message : 'Failed to load verification';
    emit('error', errorMessage.value);
  } finally {
    isLoading.value = false;
  }
};

// Handle verification result
const handleVerificationResult = (result: TurnstileResult | false) => {
  if (result === false) {
    hasError.value = true;
    errorMessage.value = 'Verification failed';
    emit('error', errorMessage.value);
    return;
  }

  if (result.success && result.token) {
    console.log('✅ Turnstile verification successful');
    hasError.value = false;
    errorMessage.value = '';
    emit('success', result.token);
  } else if (result.error === 'expired') {
    console.warn('⚠️ Turnstile verification expired');
    emit('expired');
  } else {
    console.error('❌ Turnstile verification failed:', result.error);
    hasError.value = true;
    errorMessage.value = result.error || 'Verification failed';
    emit('error', errorMessage.value);
  }
};

// Retry verification
const retry = () => {
  initTurnstile();
};

// Reset widget
const reset = () => {
  if (widgetId.value) {
    cfManager.reset(widgetId.value);
    hasError.value = false;
    errorMessage.value = '';
    emit('reset');
  }
};

// Watch for prop changes and reinitialize
watch(
  () => [props.siteKey, props.theme, props.size, props.appearance, props.language],
  () => {
    if (widgetId.value) {
      initTurnstile();
    }
  },
  { deep: true }
);

// Lifecycle
onMounted(() => {
  initTurnstile();
});

onUnmounted(() => {
  if (widgetId.value) {
    cfManager.remove(widgetId.value);
  }
});

// Expose methods for parent component
defineExpose({
  reset,
  retry
});
</script>

<style scoped>
.turnstile-widget {
  position: relative;
  display: inline-block;
  min-height: 65px; /* Minimum height for normal size */
}

.turnstile-container {
  position: relative;
  z-index: 1;
}

.turnstile-container.loading {
  opacity: 0.5;
  pointer-events: none;
}

.turnstile-container.error {
  opacity: 0.3;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  z-index: 2;
  gap: 8px;
  padding: 16px;
  text-align: center;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e3e3e3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 24px;
}

.retry-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #0056b3;
}

.loading-overlay span,
.error-overlay span {
  font-size: 14px;
  color: #666;
}

/* Compact size adjustments */
.turnstile-widget:has(.turnstile-container[data-size="compact"]) {
  min-height: 45px;
}
</style>
