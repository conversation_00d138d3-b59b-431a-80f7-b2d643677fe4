import autoprefixer from "autoprefixer";
import pxtovw from "postcss-px-to-viewport";
import mobileForever from "postcss-mobile-forever";

export default {
  plugins: [
    autoprefixer(),
    pxtovw({
      // exclude: [/^(?!.*node_modules\/vant)/],
      viewportWidth: 375, // 设计稿的视口宽度，可传递函数动态生成视图宽度
      unitPrecision: 3, // 单位转换后保留的精度（很多时候无法整除）
      valueBlackList: ["1px solid"], // 下面配置表示属性值包含 '1px solid' 的内容不会转换
      mobileUnit: "vw", // 指定需要转换成的视口单位，建议使用 vw
      maxDisplayWidth: 480, // 桌面端最大展示宽度
      rootContainingBlockSelectorList: ["van-popup--bottom"], // 指定包含块是根包含块的选择器，这种选择器的定位通常是 `fixed`，但是选择器内没有 `position: fixed`
    }),
    // https://www.npmjs.com/package/postcss-mobile-forever
    mobileForever({
      maxDisplayWidth: 480,
      containerClass: "#app",
      appContainingBlock: "auto",
    }),
  ],
};
