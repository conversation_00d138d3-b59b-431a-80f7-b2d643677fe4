<template>
  <van-action-sheet
    class="z-action-sheet"
    v-model:show="internalShow"
    :title="title"
    :overlay="overlay"
    :safe-area-inset-bottom="true"
    :closeable="false"
    :close-on-click-overlay="closeOnClickOverlay"
    teleport="body"
  >
    <div :class="{ 'custom-content': true, 'padding-bottom': hasFooter }">
      <slot></slot>
    </div>
    <div class="close" @click="handleClose" v-if="showClose">
      <ZIcon type="icon-guanbi" color="#999"></ZIcon>
    </div>
    <!-- extra 插槽独立于 hasFooter 条件，始终可用 -->
    <div class="footer-extra" v-if="$slots.extra">
      <slot name="extra"></slot>
    </div>
    <template v-if="hasFooter">
      <div class="custom-footer">
        <slot name="footer">
          <ZButton
            type="default"
            @click="handleCancel"
            v-if="showCancelButton"
            :disabled="cancelDisabled"
            >{{ cancelText }}</ZButton
          >
          <ZButton
            type="primary"
            @click="handleConfirm"
            v-if="showConfirmButton"
            :disabled="confirmDisabled"
            :loading="confirmLoading"
            >{{ confirmText }}</ZButton
          >
        </slot>
      </div>
    </template>
  </van-action-sheet>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from "vue";

// 定义 props 类型
const props = defineProps({
  // 控制显示/隐藏
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 标题
  title: {
    type: String,
    default: "",
  },
  // 是否显示关闭图标
  showClose: {
    type: Boolean,
    default: true,
  },
  // 点击遮罩层是否关闭
  closeOnClickOverlay: {
    type: Boolean,
    default: false,
  },
  // 是否显示遮罩层
  overlay: {
    type: Boolean,
    default: true,
  },
  // 显示确认按钮
  showConfirmButton: {
    type: Boolean,
    default: true,
  },
  // 显示取消按钮
  showCancelButton: {
    type: Boolean,
    default: true,
  },
  // 取消按钮文案
  cancelText: {
    type: String,
    default: "Cancel",
  },
  // 确认按钮文案
  confirmText: {
    type: String,
    default: "Confirm",
  },
  // 取消按钮 disabled
  cancelDisabled: {
    type: Boolean,
    default: false,
  },
  // 确认按钮 disabled
  confirmDisabled: {
    type: Boolean,
    default: false,
  },
  // 确认回调
  onConfirm: {
    type: Function,
    default: null,
  },
  // 取消回调
  onCancel: {
    type: Function,
    default: null,
  },
  // 顶部关闭回调
  onClose: {
    type: Function,
    default: null,
  },
  // 点取消按钮关闭
  closeOnClickCancelBtn: {
    type: Boolean,
    default: true,
  },
});

// 定义 emits 事件
const emit = defineEmits(["update:modelValue"]);

const hasFooter = computed(() => props.showCancelButton || props.showConfirmButton);

// 确认按钮加载状态
const confirmLoading = ref(false);

const internalShow = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  },
});

const handleConfirm = async () => {
  if (confirmLoading.value) return; // 防止重复点击

  try {
    confirmLoading.value = true;

    if (props.onConfirm) {
      // 执行确认回调
      await props.onConfirm();
    }
  } catch (error) {
    console.error("Dialog confirm error:", error);
  } finally {
    confirmLoading.value = false;
  }
};

const handleClose = async () => {
  try {
    await props.onClose?.();
  } finally {
    emit("update:modelValue", false);
  }
};

const handleCancel = async () => {
  try {
    await props.onCancel?.();
  } finally {
    if (props.closeOnClickCancelBtn) {
      emit("update:modelValue", false);
    }
  }
};
</script>

<style scoped lang="scss">
.z-action-sheet {
  position: relative !important;

  .close {
    position: absolute;
    right: 20px;
    top: 12px;
    z-index: 99999;
    cursor: pointer;
  }
}

.custom-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 10px 20px 20px;
  background-color: #fff;
  z-index: 10;
}

.footer-extra {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 10px 20px 20px;
  background-color: #fff;
  z-index: 10;
}

.custom-content {
  position: relative;
  padding: 0 16px;
  padding-bottom: 10px;
  box-sizing: border-box;
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  // max-height: calc(70vh - 140px);

  /* 应用美化滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }
}

:global(.van-action-sheet__content) {
  // max-height: 70vh;
  display: flex;
  flex-direction: column;
}

:global(.van-action-sheet__header) {
  text-align: left;
  padding: 20px;
  line-height: 1;
  color: #222;

  /* 弹窗标题 */
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

:global(.van-action-sheet__description:after) {
  border: 0;
}
</style>
