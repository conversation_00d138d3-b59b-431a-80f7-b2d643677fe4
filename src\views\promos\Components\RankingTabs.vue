<template>
  <div class="ranking-tabs" :style="tabsStyle">
    <button
      v-for="tab in tabs"
      :key="tab.value"
      :class="['ranking-tab', { active: modelValue === tab.value }]"
      :style="getTabStyle(tab.value)"
      @click="$emit('update:modelValue', tab.value)"
    >
      {{ tab.label }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from "vue";
import type { ThemeConfig } from "../configs";

export interface TabItem {
  label: string;
  value: string;
}

// Define component name for better debugging
defineOptions({
  name: "RankingTabs",
});

interface Props {
  modelValue: string;
  tabs?: TabItem[];
  theme?: ThemeConfig;
  customStyle?: Record<string, string>;
}

const props = withDefaults(defineProps<Props>(), {
  tabs: () => [
    { label: "Today", value: "1" },
    { label: "Yesterday", value: "2" },
  ],
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
}>();

// 计算样式
const tabsStyle = computed(() => ({
  background: props.theme?.colors.tabBackground || "#814eff",
  ...props.customStyle,
}));

const getTabStyle = (tabValue: string) => {
  const isActive = props.modelValue === tabValue;
  if (!isActive) return {};

  return {
    background: props.theme?.colors.tabActive || "linear-gradient(90deg, #fef9e2 0%, #ffe8a7 100%)",
    color: props.theme?.colors.tabActiveText || "#f7b500",
  };
};
</script>

<style lang="scss" scoped>
.ranking-tabs {
  display: flex;
  width: 60%;
  justify-content: center;
  margin: 20px auto;
  border-radius: 22px;
  overflow: hidden;
  font-family: "Inter";
}

.ranking-tab {
  width: 50%;
  color: #fff;
  border: none;
  padding: 6px 10px;
  font-size: 16px;
  border-radius: 22px;
  background: transparent;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }

  &.active {
    font-weight: 700;
  }
}
</style>
