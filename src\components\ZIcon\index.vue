<template>
  <i :class="iconClasses" :style="customStyle" @click="handleClick" />
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, } from 'vue';

// 定义 props 类型
const props = defineProps({
  // 图标类型，支持 iconfont 的类名,如 'icon-search'
  type: {
    type: String,
    required: true,
    default: ''
  },
  // 图标大小，支持数字(px)或字符串(如'1.5em')
  size: {
    type: [Number, String],
    default: 20
  },
  // 图标颜色
  color: {
    type: String,
    default: '#fff'
  },
  // 自定义类名
  className: {
    type: [String, Array, Object],
    default: ''
  },
  // 自定义样式对象
  style: {
    type: Object,
    default: () => ({})
  }
});

// 定义 emits 事件
const emits = defineEmits(['click']);

// 计算图标类名
const iconClasses = computed(() => {
  const classes = [];
  // 添加基础类名（根据你的 iconfont 库调整）
  classes.push('iconfont');
  // 添加图标类型类名
  if (props.type) {
    // 不加前缀了，直接复制过来用就好
    // classes.push(`icon-${props.type}`);
    classes.push(`${props.type}`);
  }
  // 添加自定义类名
  if (props.className) {
    if (Array.isArray(props.className)) {
      classes.push(...props.className);
    } else if (typeof props.className === 'object') {
      Object.entries(props.className).forEach(([key, value]) => {
        if (value) classes.push(key);
      });
    } else {
      classes.push(props.className);
    }
  }
  return classes;
});

// 计算自定义样式
const customStyle = computed(() => {
  const style = { ...props.style };

  // 设置图标大小
  if (props.size) {
    style.fontSize = typeof props.size === 'number' ? `${props.size}px` : props.size;
  }

  // 设置图标颜色
  if (props.color) {
    style.color = props.color
  }

  return style;
});

// 处理点击事件
const handleClick = (event) => {
  emits('click', event);
};


</script>

<style scoped></style>
