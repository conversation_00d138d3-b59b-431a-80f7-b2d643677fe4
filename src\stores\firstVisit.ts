import { defineStore } from "pinia";
import { useGlobalStore } from "./global";

const STORAGE_KEY_PREFIX = "visited-routes";

// 获取用户唯一标识符
const getUserIdentifier = (): string => {
  const globalStore = useGlobalStore();
  // 优先使用用户ID，如果没有则使用token的一部分作为标识
  const userId = globalStore.userInfo?.id;
  if (userId) {
    return `user_${userId}`;
  }

  // 如果没有用户ID，使用token的前8位作为标识（避免存储完整token）
  const token = globalStore.token;
  if (token && token.length > 8) {
    return `token_${token.substring(0, 8)}`;
  }

  // 如果都没有，使用默认标识
  return "anonymous";
};

// 获取带用户标识的存储键
const getStorageKey = (): string => {
  const userIdentifier = getUserIdentifier();
  return `${STORAGE_KEY_PREFIX}_${userIdentifier}`;
};

// 从 localStorage 加载已访问的路由
const loadVisitedRoutes = (): Record<string, boolean> => {
  try {
    const storageKey = getStorageKey();
    const stored = localStorage.getItem(storageKey);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn("Failed to load visited routes from localStorage:", error);
    return {};
  }
};

// 保存已访问的路由到 localStorage
const saveVisitedRoutes = (visitedRoutes: Record<string, boolean>): void => {
  try {
    const storageKey = getStorageKey();
    localStorage.setItem(storageKey, JSON.stringify(visitedRoutes));
  } catch (error) {
    console.warn("Failed to save visited routes to localStorage:", error);
  }
};

export const useFirstVisitStore = defineStore("firstVisit", {
  state: () => ({
    visitedRoutes: loadVisitedRoutes(),
    currentUserIdentifier: getUserIdentifier(),
  }),
  actions: {
    isFirstVisit(routePath: string): boolean {
      // 检查用户是否切换，如果切换则重新加载数据
      this.checkUserSwitch();
      return !this.visitedRoutes[routePath];
    },
    markAsVisited(routePath: string): void {
      // 检查用户是否切换，如果切换则重新加载数据
      this.checkUserSwitch();
      this.visitedRoutes[routePath] = true;
      saveVisitedRoutes(this.visitedRoutes);
    },
    clearVisitedRoutes(): void {
      this.visitedRoutes = {};
      saveVisitedRoutes(this.visitedRoutes);
    },
    /**
     * 检查用户是否切换，如果切换则重新加载对应用户的数据
     */
    checkUserSwitch(): void {
      const newUserIdentifier = getUserIdentifier();
      if (this.currentUserIdentifier !== newUserIdentifier) {
        this.currentUserIdentifier = newUserIdentifier;
        this.visitedRoutes = loadVisitedRoutes();
      }
    },
    /**
     * 手动刷新用户数据（用于登录/登出时调用）
     */
    refreshUserData(): void {
      this.currentUserIdentifier = getUserIdentifier();
      this.visitedRoutes = loadVisitedRoutes();
    },
  },
});
