<script setup lang="ts">
import { onBeforeUnmount, computed, onMounted } from "vue";
import { useGlobalStore } from "@/stores/global";
import { storeToRefs } from "pinia";
import { useDepositStore } from "@/stores/deposit";
import { useWithdrawStore } from "@/stores/withdraw";
import WalletCard from "@/views/account/components/WalletCard.vue";
import LineItem from "@/views/account/components/LineItem.vue";
import { maskString } from "@/utils/core/tools";
import { setUserInfo } from "@/api/user";
import { useClipboard } from "@/hooks/useClipboard";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import enTranslations from "@/utils/I18n/en.json";
import { getUserAvatar } from "@/utils/core/tools";
import { useKycMgrStore } from "@/stores/kycMgr";

const router = useRouter();

const globalStore = useGlobalStore();
const depositStore = useDepositStore();
const withdrawStore = useWithdrawStore();
const kycMgrStore = useKycMgrStore();
const showChangeNickNameDialog = ref(false);
const dialogInput = ref("");
const { copy } = useClipboard();

const { userInfo } = storeToRefs(globalStore);

const { kycBonusInfo, showKycCompletedRewardTip } = storeToRefs(kycMgrStore);

// 创建响应式的头像计算属性
const getAvatar = computed(() => {
  const avatar = userInfo.value?.avatar || "";
  const avatarUrl = getUserAvatar(avatar);
  return avatarUrl;
});

const handleEditUsername = () => {
  showChangeNickNameDialog.value = true;
};

const handleLineClick = (route: string) => {
  router.push(route);
};

const handleEditAvatar = () => {
  router.push("/account/set-avatar");
};

const handleConfirm = async () => {
  const regex = new RegExp(/^[a-zA-Z0-9\s]*$/);
  if (dialogInput.value === "") {
    showToast(enTranslations["tipword95"]);
    return;
  }
  if (dialogInput.value.length < 2) {
    showToast(enTranslations["tipword96"]);
    return;
  }
  if (!regex.test(dialogInput.value)) {
    showToast(enTranslations["tipword97"]);
    return;
  }
  const response = await setUserInfo({
    nickname: dialogInput.value,
  });
  const { code, msg } = response;
  if (code === 200) {
    showToast("Done");
    globalStore.updateUserInfo({ nickname: dialogInput.value });
    showChangeNickNameDialog.value = false;
    dialogInput.value = "";
  } else {
    if (code) {
      showToast(enTranslations[`php_code_${code}`] || `Error code: ${code}`);
    } else {
      showToast(msg);
    }
  }
};

// 页面离开时关闭所有弹窗
const closeAllDialogs = () => {
  // 关闭充值弹窗
  if (depositStore.show) {
    depositStore.show = false;
  }

  // 关闭提现相关弹窗
  if (withdrawStore.showWithdrawDialog) {
    withdrawStore.showWithdrawDialog = false;
  }
  if (withdrawStore.showAccountListDialog) {
    withdrawStore.showAccountListDialog = false;
  }
  if (withdrawStore.showMoneyConfirmDialog) {
    withdrawStore.showMoneyConfirmDialog = false;
  }
  if (withdrawStore.showRiskWithdrawalDialog) {
    withdrawStore.showRiskWithdrawalDialog = false;
  }
  if (withdrawStore.showPaymentPassworLimtDialog) {
    withdrawStore.showPaymentPassworLimtDialog = false;
  }
};

onMounted(() => {
  kycMgrStore.fetchKycBonus();
});

// 页面卸载前关闭弹窗
onBeforeUnmount(() => {
  closeAllDialogs();
});
</script>

<template>
  <ZPage :showNarBar="false" backgroundColor="transparent">
    <div class="account-page">
      <div class="account-header">
        <div class="user-profile">
          <img :src="getAvatar" @click="handleEditAvatar" class="avatar" alt="Avatar" />
          <div class="user-info">
            <div class="user-details">
              <div class="user-name" @click="handleEditUsername">
                {{ globalStore.userInfo.nickname || " " }}
              </div>
              <div class="user-id">
                <span>ID:</span>
                <span>{{ maskString(globalStore.userInfo.id + "") }}</span>
                <ZIcon
                  @click="copy(globalStore.userInfo.id?.toString() || '')"
                  type="icon-fuzhi1"
                  color="#666"
                  :size="12"
                ></ZIcon>
              </div>
            </div>
            <ZIcon @click="handleEditUsername" type="icon-bianji" color="#333"></ZIcon>
          </div>
        </div>
      </div>
      <div class="wallet-section">
        <WalletCard />
      </div>
      <div class="menu-section">
        <div class="menu-list">
          <LineItem
            text="Privacy and Security"
            @click="handleLineClick('/account/security-center')"
          >
            <template #icon>
              <ZIcon type="icon-Shield" :size="28"></ZIcon>
            </template>
            <template #rightContent v-if="showKycCompletedRewardTip">
              <span class="security-center-kycStatus"
                >Get ₱{{ kycBonusInfo.kyc_completed_reward }} Now!</span
              >
            </template>
          </LineItem>
          <LineItem
            v-if="!globalStore.isMiniChannel"
            text="Withdraw Accounts"
            @click="handleLineClick('/account/withdraw-account')"
          >
            <template #icon>
              <ZIcon type="icon-qianbao" :size="28"></ZIcon>
            </template>
          </LineItem>
          <LineItem text="Bet Order" @click="handleLineClick('/account/bet-order')">
            <template #icon>
              <ZIcon type="icon-orders_fill" :size="28"></ZIcon>
            </template>
          </LineItem>
          <LineItem text="Transaction" @click="handleLineClick('/account/transactions/Deposit')">
            <template #icon>
              <ZIcon type="icon-huhuan" :size="28"></ZIcon>
            </template>
          </LineItem>
          <LineItem text="Terms of use" @click="handleLineClick('/protocal/terms-of-use')">
            <template #icon>
              <ZIcon type="icon-xinxi-quan" :size="28"></ZIcon>
            </template>
          </LineItem>
        </div>
      </div>
      <TabBarGap></TabBarGap>
      <ZActionSheet
        v-model="showChangeNickNameDialog"
        title="Nickname"
        confirmText="Save"
        :showCancelButton="false"
        :onConfirm="handleConfirm"
        :closeOnClickOverlay="true"
        teleport="body"
      >
        <div class="nickname-dialog">
          <div class="title">Enter a nickname</div>
          <input
            type="text"
            maxlength="12"
            class="nickname-input"
            v-model="dialogInput"
            placeholder="Enter Your New Name"
          />
        </div>
      </ZActionSheet>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
// 昵称弹窗内容
.nickname-dialog {
  .title {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 10px;
    border-radius: 24px;
  }

  .nickname-input {
    color: #222;
    font-family: D-DIN;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    border: 1px solid #e5e5e5;
    padding: 12px;
    width: 100%;
    border-radius: 24px;

    &::placeholder {
      color: #c0c0c0;
      /* 输入框内默认文字 */
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}

// 账户页面主容器
.account-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: linear-gradient(to bottom, #ffffff, #f4f8fb 60%);

  > div {
    box-sizing: border-box;
    padding: 0 16px;
  }
}

// 账户头部区域
.account-header {
  .user-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 20px;

    // 头像样式
    .avatar {
      display: inline-block;
      width: 60px;
      height: 60px;
      cursor: pointer;
      background: #eee;
      border-radius: 50%;
    }

    // 用户信息区域
    .user-info {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 6px;
      margin-right: 20px;
      color: #333;
      .icon-fuzhi1,
      .icon-bianji {
        cursor: pointer;
      }
    }

    // 用户详情
    .user-details {
      flex: 1;
    }

    // 用户名样式
    .user-name {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 6px;
      color: #222;
      font-family: Inter;
      font-size: 22px;
      font-weight: 700;
      cursor: pointer;

      span {
        max-width: 144px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .icon-fuzhi1 {
        cursor: pointer;
      }
    }

    // 用户ID样式
    .user-id {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #666;

      span {
        font-family: Inter;
        font-size: 14px;
        font-weight: 400;
      }
    }
  }
}

// 菜单区域
.menu-section {
  flex: 1;
  overflow: auto;

  .menu-list {
    background: #fff;
    border-radius: 12px;
    margin-bottom: 20px;
  }
}

.security-center-kycStatus {
  font-family: "Inter";
  font-weight: 500;
  font-size: 12px;
  letter-spacing: 0px;
  text-align: center;
  color: #ac1140;
  margin-left: 10px;
  white-space: nowrap;
}
</style>
