<script setup lang="ts">
import { formatPhoneNumber } from "@/utils/core/tools";
import { useKycMgrStore } from "@/stores/kycMgr";
const kycMgrStore = useKycMgrStore();

const { kycData } = storeToRefs(kycMgrStore);

const init = async () => {
  await kycMgrStore.fetchKycData();
};
</script>
<template>
  <ZPage :request="init">
    <div class="content">
      <div class="form">
        <div class="form-item">
          <label for="first_name">Full legal first and middle name</label>
          <div class="input">{{ kycData.first_middle_name }}</div>
        </div>
        <div class="form-item">
          <label for="middle_name">Full legal last name</label>
          <div class="input">{{ kycData.last_name }}</div>
        </div>
        <div class="form-item">
          <label for="middle_name">Phone number</label>
          <div class="input">{{ formatPhoneNumber(kycData.phone) }}</div>
        </div>
        <div class="form-item">
          <label>Date of Birth</label>
          <div class="dob-wrapper">
            <div class="input">{{ kycData.day }}</div>
            <div class="input">{{ kycData.month }}</div>
            <div class="input">{{ kycData.year }}</div>
          </div>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
.content {
  padding: 16px;
}

.form {
  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #ff936f;
      margin-top: 4px;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .input {
      --van-field-border: none;
      /* 移除 Vant Field 默认底部边框 */
      --van-field-padding: 8px 0;
      /* 调整内边距，适配独占一行 */
      background-color: #f7f8fa;
      /* 背景色示例，可根据设计调整 */
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #222;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      .input {
        flex: 1;
        width: 33%;
      }
    }
  }
}
</style>
