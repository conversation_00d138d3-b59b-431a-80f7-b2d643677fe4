import { RouteLocationNormalizedLoaded } from "vue-router";

// 需要页面组件定义其name，如defineOptions({ name: 'Promos' });
// 可缓存页面 name 列表
// 不太建议什么页面都使用keepAliveNames，因为会内存溢出
export const keepAliveNames = [
  "Home",
  "PromosAndTournament",
  "News",
  "Categories",
  "CasinoCate",
  "Transactions",
  "BetOrders",
  "SetAvatar",
  "RewardWallet",
  // "NewsDetail",
  // "MessageDetail",
];

// 详情页 name 列表（每个 id 独立缓存）
export const detailNames = ["NewsDetail", "MessageDetail"];
// promos 详情
const detailNames2 = [
  // "Promo0",
  // "Promo1",
  // "Promo2",
  // "Promo3",
  // "Promo4",
  // "Promo5",
  // "Promo5Tip",
  // "Promo7",
  // "Promo7Tip",
  // "Promo8",
  // "Promo9",
  // "Promo9Tip",
  // "Promo10",
  // "Promo11",
  // "Promo12",
  // "Promo14",
  // "Promo15",
  // "Promo16",
  // "PromoView",
];

// 生成智能的路由 key
export function getRouteKey(route: RouteLocationNormalizedLoaded) {
  const routeName = route.name as string;
  if (detailNames2.includes(routeName)) {
    return route.fullPath;
  }
  if (keepAliveNames.includes(routeName)) {
    // 详情页需要带 id
    if (detailNames.includes(routeName)) {
      const id = route.params.id || route.query.id;
      return id ? `${routeName}-${id}` : routeName;
    }
    // 普通可缓存页面
    return routeName;
  }
  // 其它页面
  return routeName || route.path;
}
