# EnvelopeButton 组件

## 概述

`EnvelopeButton` 是一个专门为红包（奖励）功能设计的交互按钮组件。它具有动画视觉元素，并与自动弹窗管理系统集成，用于显示红包相关的对话框。

## 文件位置

```
src/components/Single-purpose/EnvelopeButton.vue
```

## 功能特性

- **视觉设计**: 红包主题按钮，带分层图像
- **动画效果**: 脉冲缩放动画，增强视觉吸引力
- **状态管理**: 与 Pinia 自动弹窗状态管理集成
- **触摸优化**: 针对移动设备触摸交互优化
- **数据验证**: 确保在显示弹窗前红包数据已加载

## 使用方法

### 基础实现

```vue
<template>
  <EnvelopeButton />
</template>

<script setup>
import EnvelopeButton from "@/components/Single-purpose/EnvelopeButton.vue";
</script>
```

### 集成示例

```vue
<template>
  <div class="bonus-section">
    <h3>每日奖励</h3>
    <EnvelopeButton />
  </div>
</template>

<script setup>
import EnvelopeButton from "@/components/Single-purpose/EnvelopeButton.vue";
</script>

<style scoped>
.bonus-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}
</style>
```

## 属性 (Props)

该组件不接受任何属性。所有功能都是自包含的。

## 事件 (Events)

该组件不发出任何自定义事件。所有交互都在内部处理。

## 依赖项

### 状态管理依赖

- `useAutoPopMgrStore` - 管理红包弹窗状态
- `POP_FORM` - 弹窗来源跟踪枚举

### 资源依赖

- `@/assets/images/redEnvelope/BG.png` - 背景图像
- `@/assets/images/redEnvelope/WZ.png` - 文字覆盖图像
- `@/assets/images/redEnvelope/an.png` - 按钮元素图像

## 行为特性

### 点击交互

1. **数据验证**: 检查状态管理中是否存在红包数据
2. **数据加载**: 如果数据不可用则获取红包数据
3. **弹窗显示**: 显示红包提示对话框
4. **来源跟踪**: 记录交互来源为 `POP_FORM.CLICK`

### 动画详情

- **缩放动画 1**: 文字覆盖从 1.15x 到 1x 缩放脉冲
- **缩放动画 2**: 按钮元素从 1x 到 1.15x 缩放脉冲
- **持续时间**: 1.2 秒无限循环 ease-in-out
- **偏移**: 动画偏移以增加视觉变化

## 样式设计

### 容器样式

- **尺寸**: 80px 宽度，自动高度
- **定位**: 相对定位用于分层元素
- **交互**: 指针光标，触摸优化
- **高亮**: 移动端透明点击高亮

### 层级结构

1. **背景**: 基础红包图像
2. **文字覆盖**: 位置 top: 22px, left: 23px (35px 宽度)
3. **按钮元素**: 位置 top: 30px, left: 30px (20px 宽度)

## 技术说明

### 性能考虑

- 使用 CSS 变换进行动画（GPU 加速）
- 针对移动设备优化的图像资源
- 交互时最少的 JavaScript 执行

### 移动端优化

- 触摸操作优化，提供更好的触摸响应
- 禁用 Webkit 点击高亮
- 禁用用户选择以防止文本选择

## 集成要求

### 状态管理设置

确保自动弹窗管理状态管理正确配置：

```javascript
// stores/autoPopMgr.js
export const useAutoPopMgrStore = defineStore("autoPopMgr", {
  state: () => ({
    envelopeInfo: {},
    showEnvelopeTip: false,
    sourceEnvelope: null,
  }),
  actions: {
    async getEnvelopeData() {
      // 获取红包数据的实现
    },
  },
});
```

### 资源要求

确保所有必需的图像资源在指定路径中可用：

- 红包背景图像
- 文字覆盖图像
- 按钮元素图像

## 浏览器兼容性

- **现代浏览器**: 完全支持 CSS 动画和变换
- **移动端 Safari**: 针对 iOS 触摸交互优化
- **Android Chrome**: 针对 Android 触摸交互优化
- **旧版支持**: 对旧版浏览器的优雅降级

## 故障排除

### 常见问题

1. **图像无法加载**

   - 验证资源路径是否正确
   - 检查图像是否存在于 assets 目录中

2. **动画不工作**

   - 确保支持 CSS 动画
   - 检查是否有冲突的 CSS 规则

3. **状态管理集成问题**

   - 验证 Pinia 状态管理是否正确初始化
   - 检查状态管理方法实现

4. **移动端触摸问题**
   - 确保正确应用了 touch-action CSS
   - 检查是否有冲突的触摸事件处理器

## 使用建议

### 最佳实践

- 确保红包数据在组件使用前已准备就绪
- 在生产环境中优化图像资源大小
- 测试不同设备上的触摸交互体验

### 注意事项

- 组件依赖特定的图像资源，确保路径正确
- 需要配合弹窗组件使用以显示红包详情
- 建议在游戏或活动页面中使用
