<template>
  <PublicActivityBonusTip
    :show="showRegisterBonusTip"
    :tipsText="registerTitle"
    :bonusAmount="formattedBonusAmount"
    buttonText="Done"
    TipsAlign="center"
    ref="publicActivityBonusTipRef"
    @click="confirmClick"
    @start-coin-animation="handleCoinAnimation"
  />
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { AWARD_UPDATE_TYPE, AWARD_NAME } from "@/utils/config/GlobalConstant";
import { useGlobalStore } from "@/stores/global";
import PublicActivityBonusTip from "./PublicActivityBonusTip.vue";

const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showRegisterBonusTip, activityBonusType } = storeToRefs(autoPopMgrStore);
const { registerAward } = storeToRefs(globalStore);

// 注册奖励相关变量
const registerTitle = ref("");
const registerCoin = ref("");

const emit = defineEmits(["start-coin-animation"]);
const publicActivityBonusTipRef = ref<InstanceType<typeof PublicActivityBonusTip> | null>(null);

// 处理金币动画事件
const handleCoinAnimation = (element: HTMLElement | null) => {
  emit("start-coin-animation", element);
};

// 格式化金额，如果 registerCoin 为数字则以本地格式显示，否则显示 "--"
const formattedBonusAmount = computed(() => {
  const num = Number(registerCoin.value?.replace("-", ""));
  return num >= 0 ? num.toLocaleString() : "--";
});

// 关闭弹窗，清理后调用余额刷新和销毁当前弹窗
const confirmClick = async () => {
  showRegisterBonusTip.value = false;
  emit("start-coin-animation", publicActivityBonusTipRef.value?.confirmBtnRef);
  // 防止重复弹框
  globalStore.updateRegisterAward({ type: 0 });
  // Balance 组件内部会更新余额
  setTimeout(() => {
    AutoPopMgr.destroyCurrentPopup();
  }, 2000);
};

// 总初始化函数，根据奖励数据类型进行区分处理
const initBonusData = async () => {
  let updateType = activityBonusType.value;
  // 如果是注册奖励，根据不同 updateType 填写对应标题及金额
  const amount = registerAward.value.amount;
  registerTitle.value =
    updateType === AWARD_UPDATE_TYPE.REGISTER_USER
      ? AWARD_NAME.REGISTER_USER
      : AWARD_NAME.BING_IPHONE_USER;
  registerCoin.value = amount + "";
};

// 监听弹窗显示
watch(
  [() => autoPopMgrStore.showRegisterBonusTip, () => autoPopMgrStore.activityBonusType],
  ([show]) => {
    if (show) {
      initBonusData();
    }
  }
);

defineExpose({
  confirmBtnRef: computed(() => publicActivityBonusTipRef.value?.confirmBtnRef),
});
</script>
