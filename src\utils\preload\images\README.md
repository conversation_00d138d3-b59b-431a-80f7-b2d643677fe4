# 图片预加载工具使用指南

## 📁 新的文件结构

```
src/utils/preload/
├── core/
│   └── imagePreloader.ts          # 核心预加载引擎
├── images/                        # 🆕 新的图片预加载器目录
│   ├── index.ts                   # 统一入口文件
│   ├── CoinImagePreloader.ts      # 金币图片预加载器
│   ├── PopupImagePreloader.ts     # 弹窗图片预加载器
│   └── GameImagePreloader.ts      # 游戏图片预加载器
├── coinImageCache.ts              # ⚠️ 已废弃，保留向后兼容
├── PopupImagePreloader.ts         # ⚠️ 已废弃，保留向后兼容
├── GameImagePreloader.ts          # ⚠️ 已废弃，保留向后兼容
└── README.md                      # 使用指南
```

## 🚀 推荐使用方式

### 统一入口（推荐）

```typescript
// 使用统一入口，获得所有预加载功能
import {
  preloadAllCriticalImages,
  preloadGameRelatedImages,
  coinImagePreloader,
  gameImagePreloader,
  popupImagePreloader,
} from "@/utils/preload/images";
```

## 概述

我们提供了一套完整的图片预加载解决方案，包括：

1. **通用图片预加载工具** (`core/imagePreloader.ts`) - 核心预加载引擎
2. **金币图片预加载器** (`images/CoinImagePreloader.ts`) - 专门用于金币动画图片
3. **弹窗图片预加载器** (`images/PopupImagePreloader.ts`) - 专门用于弹窗图片
4. **游戏图片预加载器** (`images/GameImagePreloader.ts`) - 专门用于游戏相关图片
5. **统一管理器** (`images/index.ts`) - 统一管理所有预加载功能

## 核心功能

### 1. 通用图片预加载工具

```typescript
import {
  imagePreloader,
  preloadImage,
  preloadImages,
  preloadCSSBackgrounds,
  isImageCached,
} from "@/utils/core/imagePreloader";

// 预加载单个图片
await preloadImage("https://example.com/image.jpg");

// 预加载图片列表
await preloadImages(["https://example.com/image1.jpg", "https://example.com/image2.jpg"], {
  timeout: 5000,
  concurrent: true,
  concurrency: 3,
  onProgress: (loaded, total) => {
    console.log(`进度: ${loaded}/${total}`);
  },
  onError: (error, src) => {
    console.warn(`加载失败: ${src}`, error);
  },
});

// 预加载 CSS 背景图片
await preloadCSSBackgrounds(["https://example.com/bg1.jpg", "https://example.com/bg2.jpg"]);

// 检查图片是否已缓存
if (isImageCached("https://example.com/image.jpg")) {
  console.log("图片已缓存");
}
```

### 2. 金币图片预加载器

```typescript
import { coinImagePreloader, preloadCoinImages } from "@/utils/preload/images";

// 预加载所有金币图片
await preloadCoinImages({
  onProgress: (loaded, total) => {
    console.log(`金币图片预加载进度: ${loaded}/${total}`);
  },
});

// 检查预加载状态
const status = coinImagePreloader.getStatus();
console.log(`金币图片状态:`, status);

// 检查特定金币图片是否已缓存
if (coinImagePreloader.isImagePreloaded(coinImagePreloader.getImagePaths()[0])) {
  console.log("第一张金币图片已缓存");
}
```

### 3. 弹窗图片预加载器

```typescript
import { popupImagePreloader } from "@/utils/preload/images";

// 预加载所有弹窗图片
await popupImagePreloader.preloadAll((loaded, total) => {
  console.log(`弹窗图片预加载进度: ${loaded}/${total}`);
});

// 强制预加载特定图片
const success = await popupImagePreloader.forcePreloadImage("/path/to/image.jpg");

// 检查图片是否已缓存
if (popupImagePreloader.isImageCached("/path/to/image.jpg")) {
  console.log("弹窗图片已缓存");
}
```

### 3. 游戏图片预加载器

```typescript
import {
  gameImagePreloader,
  preloadGameImages,
  preloadProviderLogos,
  preloadBanners,
} from "@/utils/preload/GameImagePreloader";

// 预加载游戏图片
await preloadGameImages(gameList, {
  onProgress: (loaded, total) => {
    console.log(`游戏图片: ${loaded}/${total}`);
  },
});

// 预加载厂商 Logo
await preloadProviderLogos(providerList);

// 预加载轮播图
await preloadBanners(bannerList);

// 检查游戏图片缓存状态
console.log(`已缓存图片数量: ${gameImagePreloader.getCachedImageCount()}`);
```

## 在 Vue 组件中使用

### 基础使用

```vue
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { preloadImages } from "@/utils/core/imagePreloader";

const isLoading = ref(false);
const progress = ref(0);

const images = ["/images/game1.jpg", "/images/game2.jpg", "/images/game3.jpg"];

onMounted(async () => {
  isLoading.value = true;

  try {
    await preloadImages(images, {
      onProgress: (loaded, total) => {
        progress.value = Math.round((loaded / total) * 100);
      },
    });

    console.log("所有图片预加载完成");
  } catch (error) {
    console.error("图片预加载失败:", error);
  } finally {
    isLoading.value = false;
  }
});
</script>

<template>
  <div v-if="isLoading">
    <div>加载中... {{ progress }}%</div>
  </div>
  <div v-else>
    <!-- 图片内容 -->
  </div>
</template>
```

### 在 Store 中使用

```typescript
// stores/imageStore.ts
import { defineStore } from "pinia";
import { gameImagePreloader } from "@/utils/preload/GameImagePreloader";

export const useImageStore = defineStore("image", {
  state: () => ({
    isPreloading: false,
    preloadProgress: 0,
  }),

  actions: {
    async preloadGameData(gameList: any[]) {
      this.isPreloading = true;
      this.preloadProgress = 0;

      try {
        await gameImagePreloader.preloadGameImages(gameList, {
          onProgress: (loaded, total) => {
            this.preloadProgress = Math.round((loaded / total) * 100);
          },
        });
      } finally {
        this.isPreloading = false;
      }
    },

    isImageCached(src: string): boolean {
      return gameImagePreloader.isGameImageCached(src);
    },
  },
});
```

## 高级配置

### 自定义预加载选项

```typescript
const options = {
  // 超时时间（毫秒）
  timeout: 8000,

  // 是否并发加载
  concurrent: true,

  // 并发数量限制
  concurrency: 5,

  // 进度回调
  onProgress: (loaded: number, total: number, currentImage?: string) => {
    console.log(`进度: ${loaded}/${total}`, currentImage);
  },

  // 错误回调
  onError: (error: Error, src: string) => {
    console.warn(`加载失败: ${src}`, error);
  },
};

await preloadImages(imageList, options);
```

### 支持的图片格式

- **普通图片**: JPG, PNG, GIF, WebP
- **SVG 图片**: 使用 fetch 方式预加载
- **CSS 背景图片**: 创建临时样式表预加载

## 性能建议

1. **合理设置并发数**: 根据网络状况调整 `concurrency` 参数
2. **优先级预加载**: 重要图片先预加载，次要图片后预加载
3. **缓存检查**: 使用 `isImageCached()` 避免重复预加载
4. **错误处理**: 设置合适的 `timeout` 和 `onError` 回调
5. **进度反馈**: 使用 `onProgress` 提供用户反馈

## 注意事项

- 预加载会消耗用户流量，请谨慎使用
- 大图片建议设置较长的超时时间
- SVG 文件使用特殊的预加载方式
- 预加载失败不会影响正常的图片显示

## 🔄 迁移指南

### 从旧版本迁移

#### 1. 金币图片缓存迁移

**旧版本 (coinImageCache.ts)**:

```typescript
import { coinImageCache } from "@/utils/preload/coinImageCache";

// 预加载金币图片
await coinImageCache.preloadImages();

// 检查是否已预加载
if (coinImageCache.isImagePreloaded(imagePath)) {
  // ...
}
```

**新版本 (推荐)**:

```typescript
import { coinImagePreloader, preloadCoinImages } from "@/utils/preload/images";

// 预加载金币图片
await preloadCoinImages({
  onProgress: (loaded, total) => {
    console.log(`进度: ${loaded}/${total}`);
  },
});

// 检查是否已预加载
if (coinImagePreloader.isImagePreloaded(imagePath)) {
  // ...
}

// 获取详细状态
const status = coinImagePreloader.getStatus();
```

#### 2. 弹窗图片预加载迁移

**旧版本**:

```typescript
import { popupImagePreloader } from "@/utils/preload/PopupImagePreloader";
```

**新版本**:

```typescript
import { popupImagePreloader } from "@/utils/preload/images";
```

#### 3. 游戏图片预加载迁移

**旧版本**:

```typescript
import { gameImagePreloader } from "@/utils/preload/GameImagePreloader";
```

**新版本**:

```typescript
import { gameImagePreloader } from "@/utils/preload/images";
```

### 统一管理器使用

**新功能 - 统一预加载管理**:

```typescript
import {
  preloadAllCriticalImages,
  preloadGameRelatedImages,
  imagePreloadManager,
} from "@/utils/preload/images";

// 预加载所有关键图片（金币 + 弹窗）
await preloadAllCriticalImages((stage, loaded, total) => {
  console.log(`${stage}: ${loaded}/${total}`);
});

// 预加载游戏相关图片
await preloadGameRelatedImages(
  {
    games: gameList,
    providers: providerList,
    banners: bannerList,
    categories: categoryList,
  },
  (stage, loaded, total) => {
    console.log(`${stage}: ${loaded}/${total}`);
  }
);

// 获取所有预加载状态
const allStatus = imagePreloadManager.getAllPreloadStatus();
console.log("预加载状态:", allStatus);
```

### 向后兼容性

- ✅ 旧的导入路径仍然可用
- ✅ 旧的 API 接口保持不变
- ✅ 现有代码无需立即修改
- 🔄 建议逐步迁移到新的统一接口

### 迁移优势

1. **统一管理**: 所有图片预加载功能集中管理
2. **更好的性能**: 基于优化的核心预加载引擎
3. **更丰富的功能**: 支持进度回调、错误处理、状态查询
4. **更好的类型支持**: 完整的 TypeScript 类型定义
5. **更清晰的结构**: 按功能分类的文件组织
