// 图片资源导入
import promotions from "@/assets/images/news/promotions.png";
import about from "@/assets/images/news/about.png";
import activity from "@/assets/images/news/activity.png";
import topic from "@/assets/images/news/topic.png";

// 新闻类型对应的图标映射
export const newsTypeIcon: Record<number, string> = {
  1: promotions,
  2: activity,
  3: about,
  4: topic,
};

// 新闻类型对应的文本映射
export const newsTypeText: Record<number, string> = {
  1: "Promotions",
  2: "Events",
  3: "About US",
  4: "Seasonal Topics",
};

/**
 * 获取新闻类型图标
 * @param newsType 新闻类型编号
 * @returns 图标路径
 */
export const getNewsTypeIcon = (newsType: number): string => {
  return newsTypeIcon[newsType] || promotions; // 默认返回 promotions 图标
};

/**
 * 获取新闻类型文本
 * @param newsType 新闻类型编号
 * @returns 类型文本
 */
export const getNewsTypeText = (newsType: number): string => {
  return newsTypeText[newsType] || "Unknown"; // 默认返回 "Unknown"
};

/**
 * 获取新闻类型信息（图标和文本）
 * @param newsType 新闻类型编号
 * @returns 包含图标和文本的对象
 */
export const getNewsTypeInfo = (newsType: number) => {
  return {
    icon: getNewsTypeIcon(newsType),
    text: getNewsTypeText(newsType),
  };
};

// 新闻类型定义
export interface NewsItem {
  id: number;
  type: "announcement" | "promotion" | "system";
  title: string;
  abstract: string;
  created_at: string;
  updated_at?: string;
  image?: string;
  status?: number;
  news_type: number;
  unread?: boolean;
  content_type?: number;
  url?: string;
  author?: string;
  news_content?: string;
  sort?: number;
}

/**
 * 处理新闻未读状态
 * @param list 新闻列表
 * @param getReadStatus 获取已读状态的函数
 * @param removeReadStatus 移除已读状态的函数
 * @returns 处理后的新闻列表
 */
export const handleNewsUnreadStatus = (
  list: NewsItem[],
  getReadStatus: (id: number) => boolean,
  removeReadStatus: (id: number) => void
): NewsItem[] => {
  return list.map((item) => {
    if (item.status == 1) {
      item.unread = true;
      if (getReadStatus(item.id)) {
        item.unread = false;
      }
    } else {
      removeReadStatus(item.id);
    }
    return item;
  });
};

/**
 * 新闻列表排序函数
 * @param array 新闻数组
 * @returns 排序后的数组
 */
export const sortNewsArray = (array: NewsItem[]): NewsItem[] => {
  return array.sort((a, b) => {
    if (parseInt(String(a.sort || 0)) < parseInt(String(b.sort || 0))) return 1;
    if (parseInt(String(a.sort || 0)) > parseInt(String(b.sort || 0))) return -1;
    if (
      new Date(a.updated_at || a.created_at).getTime() <
      new Date(b.updated_at || b.created_at).getTime()
    )
      return 1;
    if (
      new Date(a.updated_at || a.created_at).getTime() >
      new Date(b.updated_at || b.created_at).getTime()
    )
      return -1;
    return 0;
  });
};

/**
 * 验证并修复 URL 格式
 * @param url 原始 URL
 * @returns 修复后的 URL 或 null（如果无效）
 */
export const validateAndFixUrl = (url: string): string | null => {
  if (!url || typeof url !== "string") {
    return null;
  }

  // 检查是否已经是完整的 URL
  if (/^https?:\/\//.test(url)) {
    return url;
  }

  // 检查是否是有效的域名格式
  if (/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}/.test(url)) {
    return "https://" + url;
  }

  return null;
};
