<template>
  <ZFrameAnimation ref="frameAnimation" animationName="coin" :frameUrls="frameUrls" :frameSizes="frameSizes" :fps="12"
    :centerAlign="true" :fixedSize="true" containerSizeMode="max" objectFit="contain" :preloadFrames="0"
    :autoPlay="false" />
</template>
<script setup>
import { ref, onMounted, defineExpose } from 'vue';
import ZFrameAnimation from '@/components/ZFrameAnimation/index.vue';
import { coinImagePreloader } from '@/utils/preload/images';

// 组件引用
const frameAnimation = ref(null);

// 使用新的预加载器获取图片数据
const frameUrls = coinImagePreloader.getImagePaths();
const frameSizes = coinImagePreloader.getImageSizes();

// 组件挂载时确保图片已预加载
onMounted(async () => {
  try {
    await coinImagePreloader.preloadImages();
  } catch (error) {
    console.warn('金币图片预加载失败:', error);
  }
});

// 开始动画
const startAnimation = () => {
  frameAnimation.value?.startAnimation();
};

// 停止动画
const stopAnimation = () => {
  frameAnimation.value?.stopAnimation();
};

// 重置动画
const resetAnimation = () => {
  frameAnimation.value?.resetAnimation();
};

// 暴露方法给父组件
defineExpose({
  startAnimation,
  stopAnimation,
  resetAnimation
});

</script>

<style scoped></style>
