# useVerifyPreconditions 使用指南

这个 composable 用于统一处理手机号和支付密码的验证逻辑，避免在每个组件中重复编写相同的代码。

## 功能特性

- 统一的前置条件检查逻辑
- 自动显示验证弹窗
- 支持验证成功后的回调函数
- 简化组件代码，提高可维护性

## 基本用法

### 1. 导入 composable

```typescript
import { useVerifyPreconditions } from '@/composables/useVerifyPreconditions'
```

### 2. 在组件中使用

```vue
<template>
  <div>
    <!-- 你的组件内容 -->
    <ZButton @click="handleWithdraw">提现</ZButton>
    
    <!-- 验证弹窗组件 -->
    <VerifyDialogPreconditions 
      v-model:showDialog="showVerifyDialogPreconditions"
      :succCallBack="handlePreconditionsConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { useVerifyPreconditions } from '@/composables/useVerifyPreconditions'
import VerifyDialogPreconditions from '@/components/ZVerifyDialog/VerifyDialogPreconditions.vue'

// 使用 composable
const { 
  showVerifyDialogPreconditions, 
  verifyPreconditions, 
  handlePreconditionsConfirm 
} = useVerifyPreconditions()

// 处理提现按钮点击
const handleWithdraw = () => {
  verifyPreconditions(() => {
    // 验证成功后执行的逻辑
    console.log('开始提现流程')
    // 这里可以调用提现相关的方法
  })
}
</script>
```

## API 说明

### 返回值

| 属性 | 类型 | 说明 |
|------|------|------|
| `showVerifyDialogPreconditions` | `Ref<boolean>` | 控制验证弹窗显示状态 |
| `checkPreconditions` | `() => boolean` | 检查是否满足前置条件 |
| `verifyPreconditions` | `(callback?: () => void) => boolean` | 验证前置条件，不满足时显示弹窗 |
| `handlePreconditionsConfirm` | `() => void` | 验证完成后的处理函数 |
| `getMissingPreconditions` | `() => string[]` | 获取缺失的前置条件列表 |

### 方法详解

#### `verifyPreconditions(callback?: () => void): boolean`

验证前置条件的主要方法。

- **参数**: `callback` - 验证成功后执行的回调函数
- **返回值**: `boolean` - 是否立即满足条件
  - `true`: 条件已满足，立即执行回调
  - `false`: 条件不满足，显示验证弹窗

#### `checkPreconditions(): boolean`

仅检查是否满足前置条件，不显示弹窗。

- **返回值**: `boolean` - 是否满足所有前置条件

#### `getMissingPreconditions(): string[]`

获取当前用户缺失的前置条件。

- **返回值**: `string[]` - 缺失条件的数组，可能包含 `'phone'` 和 `'withdrawPassword'`

## 迁移指南

### 原有代码模式

```typescript
// 原有的重复代码
const showVerifyDialogPreconditions = ref(false)

const showWithdrawDialog = () => {
  const userInfo = globalStore.userInfo
  if (!userInfo.phone) {
    showVerifyDialogPreconditions.value = true
    return
  } else if (!userInfo.withdraw_password) {
    showVerifyDialogPreconditions.value = true
    return
  }
  withdrawStore.showWithdrawDialog = true
}

const handlePreconditionsConfirm = () => {
  showVerifyDialogPreconditions.value = false
  nextTick(() => {
    withdrawStore.showWithdrawDialog = true
  })
}
```

### 使用 composable 后

```typescript
// 简化后的代码
const { 
  showVerifyDialogPreconditions, 
  verifyPreconditions, 
  handlePreconditionsConfirm 
} = useVerifyPreconditions()

const showWithdrawDialog = () => {
  verifyPreconditions(() => {
    withdrawStore.showWithdrawDialog = true
  })
}
```

## 注意事项

1. 确保在模板中正确绑定 `VerifyDialogPreconditions` 组件
2. `handlePreconditionsConfirm` 必须传递给 `VerifyDialogPreconditions` 的 `succCallBack` 属性
3. 这个 composable 依赖于 `useGlobalStore`，确保全局状态正确初始化

## 完整示例

参考 `src/views/account/components/WalletCard.vue` 文件查看完整的使用示例。
