<template>
  <van-cell class="items-block">
    <div class="items-title">{{ dateKey }}</div>
    <div class="items-content">
      <div v-for="item in items" :key="item.id">
        <LineSetting
          valueStatus="normal"
          :text="item.game_name"
          :value="item.provider_name"
          :showArrow="false"
          :rightText="item.rightText"
          :rightStyle="item.rightStyle"
          @click="handleItemClick(item)"
        >
          <template #icon>
            <img class="item-img" v-lazy="getGameImage(item)" alt="" />
          </template>
        </LineSetting>
      </div>
    </div>
  </van-cell>
</template>

<script setup lang="ts">
import LineSetting from "@/views/account/components/LineSetting.vue";
import PlaceholderBase64 from "@/assets/constants/nsLogoBase64";
import type { BetListItemProps, BetItem } from "../config";
import { TAB_STYLES } from "../config";

interface Emits {
  (e: "item-click", item: BetItem): void;
}

const props = defineProps<BetListItemProps>();
const emit = defineEmits<Emits>();

const getGameImage = (item: BetItem) => {
  return props.gamesList[item.game_id]?.images || PlaceholderBase64;
};

// Event handlers
const handleItemClick = (item: BetItem) => {
  const enrichedItem = {
    ...item,
    images: getGameImage(item),
  };
  emit("item-click", enrichedItem);
};
</script>

<style lang="scss" scoped>
.items-block {
  background: transparent;
}

.item-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.items-content {
  padding: 16px 12px 0;
  background-color: #fff;
  text-align: left;
  border-radius: 10px;

  &:deep(.line-item) {
    padding: 0 0 14px 0;
  }
}

.items-title {
  color: #6a7a88;
  font-family: Inter;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-bottom: 0.5px solid #eee;
  line-height: 48px;
  padding: 0 12px;
  text-align: left;
}
</style>
