<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount, computed } from "vue";
import { jumpBanner } from "@/utils/JumpPromo";
import homeBannerBase64 from "@/assets/constants/homeBannerBase64";
import { getServerSideImageUrl } from "@/utils/core/tools";
import { useBannerStore } from "@/stores/banner";
import type { BannerItem } from "@/types/store";

const props = defineProps<{
  bannerType: string; // 加载的banner图类型
}>();

// 使用 banner store
const bannerStore = useBannerStore();

// 组件内部状态
const swipeRef = ref<any>(null); // vant Swipe 组件实例
const currentIndex = ref(0); // 当前轮播项索引
const currentDuration = ref(3000); // 当前轮播项的持续时间（单位：毫秒），默认 3000 毫秒

// 计算属性：根据类型获取轮播图
const banners = computed((): BannerItem[] => {
  return bannerStore.getBannersByType(props.bannerType);
});

// 计算属性：加载状态
const isLoading = computed((): boolean => {
  return bannerStore.isLoading || banners.value.length === 0;
});

// 标记是否正在滑动（从按下到抬起的整个过程）
const isSliding = ref(false);
// 标记是否完成了一次有效滑动（已切换轮播项）
const hasSlid = ref(false);

// 自动播放定时器
let autoplayTimer: ReturnType<typeof setTimeout> | null = null;

// 启动自动播放（调用 next()）
const startAutoplay = () => {
  // 清除已有定时器
  if (autoplayTimer) {
    clearTimeout(autoplayTimer);
  }
  autoplayTimer = setTimeout(() => {
    // 调用 vant Swipe 的 next() 方法切换下一项
    swipeRef.value?.next();
    // 重新启动自动播放
    startAutoplay();
  }, currentDuration.value);
};

// 停止自动播放
const stopAutoplay = () => {
  if (autoplayTimer) {
    clearTimeout(autoplayTimer);
    autoplayTimer = null;
  }
};

// 监听 currentDuration 的变化，重启自动播放
watch(currentDuration, () => {
  stopAutoplay();
  startAutoplay();
});

// 轮播变化时的处理函数
const onChange = (index: number) => {
  currentIndex.value = index;
  const banner = banners.value[index];
  if (banner && banner.duration) {
    currentDuration.value = banner.duration * 1000; // 转换为毫秒
  }
  // 核心：滑动切换后，标记为有效滑动
  hasSlid.value = true;
};

// 获取轮播数据
const fetchBanners = async () => {
  try {
    await bannerStore.fetchBanners();

    nextTick(() => {
      if (banners.value.length > 0) {
        const firstBanner = banners.value[0];
        currentDuration.value = (firstBanner.duration || 3) * 1000; // 转换为毫秒，默认3秒
        // 启动自动播放
        startAutoplay();
      }
    });
  } catch (error) {
    console.error("获取轮播数据失败:", error);
  }
};

// 根据所在页面判断取值来源
const loadByBannerType = (banner: BannerItem): string => {
  return bannerStore.getBannerImageUrl(banner, props.bannerType);
};

// 生命周期：组件挂载时获取数据并启动自动播放
onMounted(() => {
  fetchBanners();
});

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  stopAutoplay();
});

const handleJump = (banner) => {
  // 若处于滑动过程中，或完成了有效滑动，均忽略点击
  if (isSliding.value || hasSlid.value) {
    console.log("忽略滑动触发的点击事件");
    // 延迟重置有效滑动标记（避免影响下次点击）
    setTimeout(() => {
      hasSlid.value = false;
    }, 100);
    return;
  }
  // 非滑动状态，正常跳转
  jumpBanner(banner);
};
</script>

<template>
  <div>
    <!-- 加载中显示骨架图 -->
    <div v-if="isLoading || banners.length < 1" class="banner">
      <img :src="homeBannerBase64" class="banner-skeleton" />
    </div>

    <!-- 有数据时显示轮播 -->
    <van-swipe
      v-else-if="banners.length > 0"
      class="carousel"
      :autoplay="0"
      indicator-color="white"
      ref="swipeRef"
      :loop="true"
      @change="onChange"
      @touchstart="
        isSliding = true;
        hasSlid = false;
      "
      @touchend="isSliding = false"
    >
      <div v-for="(banner, index) in banners" :key="index" :data-duration="banner.duration">
        <van-swipe-item class="carousel-container" @click="() => handleJump(banner)">
          <ZImage
            :src="getServerSideImageUrl(loadByBannerType(banner))"
            class="skeleton"
            fit="fit"
            type="banner"
          >
          </ZImage>
        </van-swipe-item>
      </div>
    </van-swipe>

    <!-- 无数据时不显示任何内容 -->
  </div>
</template>

<style scoped lang="scss">
.banner-skeleton {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  height: 94px;
}

.carousel {
  width: 100%;
  position: relative;
  border-radius: 16px;
  overflow: hidden;

  .carousel-container {
    display: flex;
    transition: transform 0.5s ease;

    .skeleton {
      width: 100%;
      height: 94px;
      object-fit: cover;
      flex-shrink: 0;
      overflow: hidden;
    }
  }
}
</style>
