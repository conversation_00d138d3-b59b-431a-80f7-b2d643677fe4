/**
 * 认证状态 Composable
 * 简化版本 - 直接使用 globalStore，因为路由组件只有在登录完成后才渲染
 */

import { computed } from "vue";
import { useGlobalStore } from "@/stores/global";
import { getToken } from "@/utils/auth";

export function useAuth() {
  const globalStore = useGlobalStore();

  // 计算属性：用户信息
  const userInfo = computed(() => globalStore.userInfo);

  // 计算属性：是否为登录用户
  const isLoggedIn = computed(() => !!globalStore.token && !!globalStore.userInfo);

  // 计算属性：当前 token
  const currentToken = computed(() => globalStore.token);

  // 计算属性：认证状态
  const isAuthenticated = computed(() => !!globalStore.token);

  // 获取最新的 token
  const getLatestToken = (): string => {
    return globalStore.token || getToken();
  };

  // 检查认证状态
  const checkAuthState = (): boolean => {
    return !!globalStore.token;
  };

  return {
    // 响应式状态
    isAuthenticated,
    currentToken,
    userInfo,
    isLoggedIn,

    // 方法
    getLatestToken,
    checkAuthState,
  };
}

/**
 * 在页面组件中使用认证状态的示例
 *
 * ```typescript
 * import { useAuth } from '@/composables/useAuth';
 *
 * export default {
 *   setup() {
 *     const { isAuthenticated, currentToken, getLatestToken, waitForAuth } = useAuth();
 *
 *     // 监听认证状态变化
 *     watch(isAuthenticated, (newVal) => {
 *       if (newVal) {
 *         console.log('用户已登录，token:', currentToken.value);
 *         // 执行需要认证的操作
 *       } else {
 *         console.log('用户未登录');
 *         // 处理未登录状态
 *       }
 *     });
 *
 *     // 在需要时获取最新 token
 *     const handleApiCall = async () => {
 *       const token = getLatestToken();
 *       if (token) {
 *         // 使用 token 调用 API
 *         await callApiWithToken(token);
 *       } else {
 *         // 等待认证完成
 *         const hasAuth = await waitForAuth();
 *         if (hasAuth) {
 *           const newToken = getLatestToken();
 *           await callApiWithToken(newToken);
 *         }
 *       }
 *     };
 *
 *     return {
 *       isAuthenticated,
 *       currentToken,
 *       handleApiCall,
 *     };
 *   }
 * };
 * ```
 */
