# PhoneNumberInput 组件

一个专为菲律宾手机号设计的输入组件，提供完整的验证、格式化和用户体验功能。

## 功能特性

- ✅ 固定显示 `+63` 菲律宾国家代码
- ✅ 自动限制输入长度为10位数字
- ✅ 菲律宾手机号格式验证
- ✅ 实时或失焦验证
- ✅ 本地存储支持
- ✅ 清除按钮
- ✅ 错误提示
- ✅ 响应式设计
- ✅ 完整的事件支持

## 基本用法

```vue
<template>
  <PhoneNumberInput v-model="phone" />
</template>

<script setup>
import { ref } from 'vue';
import PhoneNumberInput from '@/components/PhoneNumberInput/index.vue';

const phone = ref('');
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string` | `""` | 双向绑定的手机号值 |
| `label` | `string` | `""` | 输入框标签 |
| `placeholder` | `string` | `"Phone Number"` | 占位符文本 |
| `maxLength` | `number` | `10` | 最大输入长度 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `showClearButton` | `boolean` | `true` | 是否显示清除按钮 |
| `validateOnInput` | `boolean` | `false` | 是否在输入时实时验证 |
| `validateOnBlur` | `boolean` | `true` | 是否在失焦时验证 |
| `autoSave` | `boolean` | `false` | 是否自动保存到本地存储 |
| `storageKey` | `string` | `"phone"` | 本地存储的键名 |
| `required` | `boolean` | `false` | 是否必填 |
| `errorMessages` | `object` | 见下方 | 自定义错误信息 |

### 默认错误信息

```typescript
errorMessages: {
  required: "Phone number is required",
  invalid: "Please enter a valid 10-digit mobile phone number"
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `value: string` | 值变化时触发 |
| `validityChange` | `isValid: boolean` | 有效性变化时触发 |
| `input` | `value: string` | 输入时触发 |
| `blur` | `value: string` | 失焦时触发 |
| `focus` | `value: string` | 聚焦时触发 |

## 方法

通过 `ref` 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `validate()` | - | - | 手动触发验证 |
| `clear()` | - | - | 清空输入内容 |
| `focus()` | - | - | 聚焦到输入框 |
| `isValid()` | - | `boolean` | 获取当前有效性 |

## 使用示例

### 1. 基础使用

```vue
<template>
  <PhoneNumberInput 
    v-model="phone"
    label="Your phone number (09xx xxxx xxx)"
    placeholder="Enter phone number"
  />
</template>
```

### 2. 带验证的使用

```vue
<template>
  <PhoneNumberInput 
    v-model="phone"
    :required="true"
    :validateOnInput="true"
    @validityChange="onValidityChange"
    :errorMessages="{
      required: '请输入手机号',
      invalid: '手机号格式不正确'
    }"
  />
  
  <button :disabled="!isPhoneValid" @click="submit">
    提交
  </button>
</template>

<script setup>
import { ref } from 'vue';

const phone = ref('');
const isPhoneValid = ref(false);

const onValidityChange = (isValid) => {
  isPhoneValid.value = isValid;
};

const submit = () => {
  console.log('Phone:', phone.value);
};
</script>
```

### 3. 自动保存功能

```vue
<template>
  <PhoneNumberInput 
    v-model="phone"
    :autoSave="true"
    storageKey="user_phone"
  />
</template>
```

### 4. 使用组件方法

```vue
<template>
  <PhoneNumberInput 
    ref="phoneInputRef"
    v-model="phone"
  />
  
  <button @click="validatePhone">验证</button>
  <button @click="clearPhone">清空</button>
  <button @click="focusPhone">聚焦</button>
</template>

<script setup>
import { ref } from 'vue';

const phoneInputRef = ref();
const phone = ref('');

const validatePhone = () => {
  phoneInputRef.value?.validate();
};

const clearPhone = () => {
  phoneInputRef.value?.clear();
};

const focusPhone = () => {
  phoneInputRef.value?.focus();
};
</script>
```

### 5. 在弹窗中使用

```vue
<template>
  <div class="dialog-content">
    <PhoneNumberInput 
      v-model="phone"
      label="Your phone number (09xx xxxx xxx)"
      :required="true"
      :validateOnBlur="true"
      @validityChange="handleValidityChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const phone = ref('');

const handleValidityChange = (isValid) => {
  // 处理验证结果
  console.log('Phone is valid:', isValid);
};
</script>
```

## 样式定制

组件使用 CSS 变量，可以通过覆盖样式进行定制：

```scss
.phone-number-input {
  // 自定义边框颜色
  .phone-input-container {
    border-color: #your-color;
    
    &:focus-within {
      border-color: #your-focus-color;
    }
  }
  
  // 自定义错误颜色
  .error-message {
    color: #your-error-color;
  }
}
```
