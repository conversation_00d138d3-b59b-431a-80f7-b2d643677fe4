# ZPage 组件使用指南

## 概述

`ZPage` 是一个通用的页面容器组件，集成了导航栏、加载状态、异常处理等功能。现在已经内置了 `ExceptionRefresh` 组件，可以统一处理页面接口异常。

## 功能特性

- ✅ **统一的页面结构**：导航栏 + 内容区域
- ✅ **自动加载管理**：内置 loading 状态和 ZLoading 组件
- ✅ **异常处理集成**：内置 ExceptionRefresh 组件
- ✅ **智能错误过滤**：自动区分网络错误和业务错误
- ✅ **自定义配置**：支持自定义异常文案和行为
- ✅ **响应式设计**：适配移动端和桌面端

## Props 配置

### 基础配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| showNarBar | 是否显示导航栏 | boolean | true |
| narBarStyle | 导航栏样式 | CSSProperties | {} |
| showNarBack | 是否显示返回按钮 | boolean | true |
| backgroundColor | 页面背景色 | string | "#fff" |
| title | 页面标题 | string | "" |
| request | 页面数据请求函数 | Function | - |
| onBack | 自定义返回处理 | Function | - |

### 异常处理配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| enableExceptionHandling | 是否启用异常处理 | boolean | true |
| exceptionTitle | 异常页面标题 | string | "页面加载失败" |
| exceptionMessage | 异常页面消息 | string | "无法加载页面内容，请检查网络连接后重试" |
| exceptionRefreshText | 刷新按钮文本 | string | "刷新" |
| exceptionCooldownTime | 刷新冷却时间（秒） | number | 5 |
| errorFilter | 自定义错误过滤器 | Function | - |

## 使用示例

### 1. 基础用法

```vue
<template>
  <ZPage title="首页" :request="loadData">
    <div class="home-content">
      <Banner :banners="banners" />
      <GameList :games="games" />
    </div>
  </ZPage>
</template>

<script setup>
import { ref } from 'vue';
import ZPage from '@/components/ZPage/index.vue';

const banners = ref([]);
const games = ref([]);

const loadData = async () => {
  const [bannersRes, gamesRes] = await Promise.all([
    api.getBanners(),
    api.getGames()
  ]);
  
  banners.value = bannersRes.data;
  games.value = gamesRes.data;
};
</script>
```

### 2. 自定义异常处理

```vue
<template>
  <ZPage 
    title="游戏列表"
    :request="loadGames"
    exception-title="游戏加载失败"
    exception-message="无法加载游戏列表，请重试"
    exception-refresh-text="重新加载"
    :exception-cooldown-time="10"
    :error-filter="customErrorFilter"
  >
    <GameGrid :games="gameList" />
  </ZPage>
</template>

<script setup>
import { ref } from 'vue';

const gameList = ref([]);

const loadGames = async () => {
  const response = await api.getGames();
  gameList.value = response.data;
};

// 自定义错误过滤器
const customErrorFilter = (error) => {
  // 只有网络错误和服务器错误才显示异常页面
  return error?.status >= 500 || 
         error?.code === 'NETWORK_ERROR' ||
         error?.message?.includes('timeout');
};
</script>
```

### 3. 禁用异常处理

```vue
<template>
  <ZPage 
    title="设置页面"
    :request="loadSettings"
    :enable-exception-handling="false"
  >
    <SettingsForm :settings="settings" />
  </ZPage>
</template>

<script setup>
import { ref } from 'vue';
import { showToast } from 'vant';

const settings = ref({});

const loadSettings = async () => {
  try {
    const response = await api.getSettings();
    settings.value = response.data;
  } catch (error) {
    // 手动处理错误
    showToast('设置加载失败');
  }
};
</script>
```

### 4. 手动控制页面状态

```vue
<template>
  <ZPage 
    ref="pageRef"
    title="数据页面"
    :request="loadData"
  >
    <div class="data-content">
      <button @click="handleManualRefresh">手动刷新</button>
      <button @click="handleSetError">模拟错误</button>
      <button @click="handleReset">重置状态</button>
    </div>
  </ZPage>
</template>

<script setup>
import { ref } from 'vue';

const pageRef = ref();

const loadData = async () => {
  // 数据加载逻辑
};

// 手动刷新
const handleManualRefresh = () => {
  pageRef.value?.refresh();
};

// 手动设置错误
const handleSetError = () => {
  pageRef.value?.setError(new Error('手动设置的错误'));
};

// 重置状态
const handleReset = () => {
  pageRef.value?.reset();
};
</script>
```

### 5. 复杂页面示例

```vue
<template>
  <ZPage 
    title="仪表板"
    :request="loadDashboard"
    exception-title="仪表板加载失败"
    exception-message="无法加载仪表板数据，请检查网络连接"
    :error-filter="dashboardErrorFilter"
  >
    <div class="dashboard">
      <UserProfile :user="userData" />
      <WalletCard :wallet="walletData" />
      <RecentOrders :orders="ordersData" />
    </div>
  </ZPage>
</template>

<script setup>
import { ref } from 'vue';

const userData = ref(null);
const walletData = ref(null);
const ordersData = ref(null);

const loadDashboard = async () => {
  // 并行加载多个接口
  const results = await Promise.allSettled([
    api.getUserInfo(),
    api.getWalletInfo(),
    api.getRecentOrders()
  ]);
  
  // 检查关键接口是否失败
  const [userResult, walletResult, ordersResult] = results;
  
  if (userResult.status === 'rejected' || walletResult.status === 'rejected') {
    // 关键接口失败，抛出错误
    throw new Error('Critical data loading failed');
  }
  
  // 设置数据
  userData.value = userResult.value?.data;
  walletData.value = walletResult.value?.data;
  ordersData.value = ordersResult.status === 'fulfilled' ? ordersResult.value?.data : [];
};

// 仪表板错误过滤器
const dashboardErrorFilter = (error) => {
  // 仪表板页面对错误更敏感，更多错误类型显示异常页面
  return error?.status >= 400 || 
         error?.code === 'NETWORK_ERROR' ||
         error?.message?.includes('timeout') ||
         error?.message?.includes('failed');
};
</script>
```

## 暴露的方法

ZPage 组件暴露了以下方法供父组件调用：

```typescript
interface ZPageExpose {
  // 手动刷新数据
  refresh: () => Promise<void>;
  
  // 手动设置错误状态
  setError: (error: any) => void;
  
  // 重置状态
  reset: () => void;
  
  // 获取当前状态
  getState: () => {
    loading: boolean;
    hasError: boolean;
    errorInfo: any;
  };
}
```

## 错误过滤器

### 默认错误过滤器

```typescript
const defaultErrorFilter = (error: any) => {
  return error?.code === 'NETWORK_ERROR' || 
         error?.code === 'TIMEOUT' || 
         error?.message?.includes('timeout') ||
         error?.message?.includes('network') ||
         error?.status >= 500;
};
```

### 自定义错误过滤器示例

```typescript
// 严格模式：只有网络错误才显示异常页面
const strictErrorFilter = (error) => {
  return error?.code === 'NETWORK_ERROR' || error?.code === 'TIMEOUT';
};

// 宽松模式：更多错误类型显示异常页面
const looseErrorFilter = (error) => {
  return error?.status >= 400 || 
         error?.code === 'NETWORK_ERROR' ||
         error?.message?.includes('error');
};

// 业务相关过滤器
const businessErrorFilter = (error) => {
  const networkErrors = ['NETWORK_ERROR', 'TIMEOUT', 'CONNECTION_REFUSED'];
  const serverErrors = [500, 502, 503, 504];
  
  return networkErrors.includes(error?.code) || 
         serverErrors.includes(error?.status);
};
```

## 最佳实践

### 1. 页面数据加载

```typescript
// ✅ 推荐：使用 Promise.all 并行加载
const loadPageData = async () => {
  const [data1, data2, data3] = await Promise.all([
    api.getData1(),
    api.getData2(),
    api.getData3()
  ]);
  
  // 处理数据
};

// ❌ 不推荐：串行加载
const loadPageData = async () => {
  const data1 = await api.getData1();
  const data2 = await api.getData2();
  const data3 = await api.getData3();
};
```

### 2. 错误处理策略

```typescript
// ✅ 推荐：区分关键和非关键接口
const loadData = async () => {
  const results = await Promise.allSettled([
    api.getCriticalData(),  // 关键数据
    api.getOptionalData()   // 可选数据
  ]);
  
  const [criticalResult, optionalResult] = results;
  
  // 关键数据失败时抛出错误
  if (criticalResult.status === 'rejected') {
    throw criticalResult.reason;
  }
  
  // 可选数据失败时只记录日志
  if (optionalResult.status === 'rejected') {
    console.warn('Optional data failed:', optionalResult.reason);
  }
};
```

### 3. 自定义异常文案

```vue
<!-- ✅ 推荐：根据页面类型自定义文案 -->
<ZPage 
  title="游戏列表"
  exception-title="游戏加载失败"
  exception-message="无法加载游戏列表，请检查网络连接后重试"
  exception-refresh-text="重新加载游戏"
>
```

## 注意事项

1. **request 函数**：必须是 async 函数，抛出的错误会被自动捕获
2. **错误过滤器**：返回 true 表示显示异常页面，false 表示不显示
3. **异常处理**：可以通过 `enableExceptionHandling={false}` 完全禁用
4. **状态管理**：组件内部会自动管理 loading 和 error 状态
5. **内存泄漏**：组件卸载时会自动清理相关状态和事件监听

通过使用增强后的 ZPage 组件，你可以轻松地为所有页面添加统一的异常处理功能，提升用户体验和开发效率！
