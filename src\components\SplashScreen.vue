<template>
  <div class="splash-screen">
    <div class="brand-wrapper">
      <RedNuStar class="nustar"></RedNuStar>
      <p class="slogan">Play Bold Win Luxury</p>
    </div>
    <!-- 环形进度条 -->
    <CircleProgress v-if="showProgress" v-model="progress" bottom-text="Loading..." :animated="true"
      class="splash-progress" />
    <div class="version">{{ `v${ALL_APP_SOURCE_CONFIG.app_version}.${globalStore.channel}` }}</div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import RedNuStar from '@/assets/RedNuStar.svg'
import CircleProgress from '@/components/CircleProgress.vue'
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import { useGlobalStore } from "@/stores/global";

const globalStore = useGlobalStore();

const props = defineProps({
  externalProgress: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['loaded'])

const progress = ref(0)
const showProgress = ref(true)
const minDisplayTime = 3000 // 2秒

let startTime = 0
let timer: number | null = null

onMounted(() => {
  startTime = Date.now()
})

// 平滑递增进度
const animateTo = (target: number) => {
  if (timer) clearInterval(timer)
  timer = window.setInterval(() => {
    if (progress.value < target) {
      progress.value += 1
    }
    if (progress.value >= target) {
      clearInterval(timer!)
      timer = null
    }
  }, 8) // 速度可调
}

// 监听外部进度变化，递增 progress
watch(
  () => props.externalProgress,
  (newProgress) => {
    if (newProgress > progress.value) {
      animateTo(Math.min(newProgress, 100))
    }
  },
  { immediate: true }
)

// 监听内部 progress，只有到100才关闭
watch(
  progress,
  (val) => {
    if (val >= 100) {
      const elapsedTime = Date.now() - startTime
      const remainingTime = Math.max(0, minDisplayTime - elapsedTime)
      setTimeout(() => {
        showProgress.value = false
        emit('loaded')
      }, remainingTime)
    }
  }
)
</script>

<style scoped lang="scss">
/* 启动页整体样式，占满视口，居中内容 */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .version {
    position: fixed;
    bottom: 16px;
    right: 12px;
    font-size: 12px;
    color: #ddd;
  }
}

/* 标语样式 */
.slogan {
  font-size: 14px;
  color: #9b2242;
  margin: 0;
}

/* 启动页进度条样式 */
.splash-progress {
  margin-top: 3rem;
}
</style>
