<script setup lang="ts">
import { useRouter } from "vue-router";

const router = useRouter();

const goHome = () => {
  router.push("/");
};
</script>

<template>
  <div class="not-found">
    <div class="content">
      <h1>404</h1>
      <p>Sorry, the page you requested does not exist</p>
      <button class="back-btn" @click="goHome">Back to Home</button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  background-color: #f8f8f8;

  .content {
    text-align: center;

    h1 {
      font-size: 64px;
      color: var(--gold-color);
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 32px;
    }

    .back-btn {
      padding: 12px 32px;
      background-color: var(--gold-color);
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>
