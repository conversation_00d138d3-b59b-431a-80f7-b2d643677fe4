/**
 * ZImage 组件相关类型定义
 */

/**
 * 占位图类型
 */
export type PlaceholderType = "game" | "banner" | "promos" | "video";

/**
 * 图片填充模式
 */
export type ImageFit = "contain" | "cover" | "fill" | "none" | "scale-down";

/**
 * ZImage 组件 Props
 */
export interface ZImageProps {
  /** 图片地址 */
  src: string;
  /** 占位图类型 */
  type?: PlaceholderType;
  /** 自定义占位图（优先级最高） */
  placeholderImg?: string;
  /** 是否开启懒加载 */
  lazyLoad?: boolean;
  /** 图片填充模式 */
  fit?: ImageFit;
}

/**
 * 占位图配置
 */
export interface PlaceholderConfig {
  /** 类型名称 */
  type: PlaceholderType;
  /** 显示名称 */
  name: string;
  /** 描述 */
  description: string;
  /** 适用场景 */
  useCase: string[];
  /** Base64 数据 */
  data: string;
}

/**
 * 预定义的占位图配置
 */
export const PLACEHOLDER_CONFIGS: Record<PlaceholderType, Omit<PlaceholderConfig, "data">> = {
  game: {
    type: "game",
    name: "游戏占位图",
    description: "适用于游戏相关的图片占位",
    useCase: ["游戏列表", "游戏图标", "游戏截图"],
  },
  banner: {
    type: "banner",
    name: "横幅占位图",
    description: "适用于横向比例的图片占位",
    useCase: ["轮播图", "活动横幅", "推广图片"],
  },
  gray: {
    type: "gray",
    name: "灰色占位图",
    description: "简洁的灰色背景占位图",
    useCase: ["用户头像", "通用图标", "简洁场景"],
  },
  custom: {
    type: "custom",
    name: "自定义占位图",
    description: "使用自定义的占位图",
    useCase: ["特殊需求", "品牌定制", "个性化场景"],
  },
};

/**
 * 获取占位图配置
 */
export function getPlaceholderConfig(type: PlaceholderType): Omit<PlaceholderConfig, "data"> {
  return PLACEHOLDER_CONFIGS[type];
}

/**
 * 验证占位图类型
 */
export function isValidPlaceholderType(type: string): type is PlaceholderType {
  return Object.keys(PLACEHOLDER_CONFIGS).includes(type);
}

/**
 * 获取所有可用的占位图类型
 */
export function getAllPlaceholderTypes(): PlaceholderType[] {
  return Object.keys(PLACEHOLDER_CONFIGS) as PlaceholderType[];
}
