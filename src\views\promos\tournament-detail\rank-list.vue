<template>
  <div class="tournament-rankList">
    <!-- Tabs -->
    <div class="tabs">
      <button
        v-for="tab in tabConfig"
        :key="tab.value"
        :class="{ active: activeTab === tab.value }"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Top 3 Podium -->
    <div class="podium">
      <PodiumItem
        v-for="(item, index) in podiumOrder"
        :key="`podium-${index}`"
        :rank-data="topThree[item.index]"
        :rank-position="item.position"
        :coin-size="item.coinSize"
        :is-current-user="isCurrentUser(topThree[item.index])"
        @click="handleRank"
      />
    </div>

    <!-- Rank Table -->
    <div class="rank-table">
      <van-row class="table-header">
        <van-col v-for="header in tableHeaders" :key="header.key" :span="header.span">
          {{ header.label }}
        </van-col>
      </van-row>

      <div class="table-content" :style="{ 'padding-bottom': myRank.user_id ? '118px' : '0' }">
        <!-- Loading State -->
        <div v-if="loading" class="loading-container">
          <van-loading size="24px" color="#6c5ce7">Loading...</van-loading>
        </div>

        <!-- Rank List -->
        <RankTableRow
          v-else
          v-for="(item, index) in prevRankList"
          :key="`${item.user_id}-${item.rank}`"
          :rank-data="item"
          :index="index"
          :is-current-user="isCurrentUser(item)"
          @click="handleRank"
        />

        <!-- Empty State -->
        <div v-if="!loading && prevRankList.length === 0" class="empty-state">
          <p>{{ emptyStateText }}</p>
        </div>
      </div>
    </div>

    <!-- My Rank Footer -->
    <MyRankFooter
      v-if="myRank.user_id"
      :my-rank="myRank"
      @rank-click="handleRank"
      @bet-now="handleBetNow"
    />
  </div>

  <!-- 使用弹窗组件 -->
  <GraphicPopup
    v-if="showModal"
    :visible="showModal"
    :userInfo="player"
    :activityId="`${curId}`"
    :isHistory="activeTab === 'previous'"
    @close="showModal = false"
    @go-to-game="handleGoToGame"
  />
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({ name: "TournamentRankList" });

import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import defaultAvatar from "@/assets/images/tournament/avatar.png";
import {
  maskString,
  getQueryByUrl,
  isEmpty,
  getUserAvatar,
  formatNumberToThousands,
  preloadImages,
} from "@/utils/core/tools";
import GraphicPopup from "./Components/GraphicPopup.vue";
import PodiumItem from "./Components/PodiumItem.vue";
import RankTableRow from "./Components/RankTableRow.vue";
import MyRankFooter from "./Components/MyRankFooter.vue";
import { getLeaderboardList } from "@/api/promos";
import { TopRank, DetailInfo, RankItem } from "../configs";
import { useGameStore } from "@/stores/game";
import ModalTabBg from "@/assets/images/tournament/activeTab-bg.png";

// 组件配置
const tabConfig = [
  { value: "previous", label: "Previous Round" },
  { value: "current", label: "This Round" },
] as const;

const podiumOrder = [
  { index: 1, position: "second", coinSize: { prize: 16, bet: 14 } },
  { index: 0, position: "first", coinSize: { prize: 16, bet: 20 } },
  { index: 2, position: "third", coinSize: { prize: 16, bet: 14 } },
] as const;

const tableHeaders = [
  { key: "rank", label: "Rank", span: 3 },
  { key: "userId", label: "User ID", span: 6 },
  { key: "bet", label: "Bet", span: 7 },
  { key: "bonus", label: "Bonus", span: 6 },
  { key: "action", label: "", span: 2 },
] as const;

// 文本配置
const emptyStateText = "No ranking data available";

// Props 定义
interface Props {
  historyRankList?: rankItem[];
  currentRankList?: rankItem[];
  loading?: boolean;
  detailData?: detailInfo;
}

const props = withDefaults(defineProps<Props>(), {
  historyRankList: () => [],
  currentRankList: () => [],
  loading: false,
  detailData: () => ({
    id: "",
    status: 0,
    matches_rule: 0,
    title: "",
    title_image: "",
    detail_image_urls: [],
    history_detail_image_urls: [],
    period_type: 0,
    start_time: 0,
    end_time: 0,
    registration_rule: 0,
    registration_requirement: "",
    my_registration_value: "",
    game_ids: [],
    vendors: [],
    brands: [],
    game_types: [],
    total_price: 0,
    my_rank: {
      rank: 0,
      avatar: "",
      nickname: "",
      score: "",
      bonus: "",
      user_id: "",
    },
    my_historyRank: {
      rank: 0,
      avatar: "",
      nickname: "",
      score: "",
      bonus: "",
      user_id: "",
    },
    top_rankings: [],
  }),
});

const gameStore = useGameStore();

// 外部传入数据
const query = getQueryByUrl(location.search);
// 当前展示的锦标赛详情
const curId = ref<number>(Number(query.id) || -1);
const router = useRouter();
const popUpData = ref({}); // 排行榜列表内容
const activeTab = ref<"previous" | "current">("previous");

watch(
  () => props.detailData.status,
  (data) => {
    activeTab.value = data === 2 ? "current" : "previous";
  },
  { immediate: true }
);
const showModal = ref(false);
const player = ref<rankItem | {}>({} as rankItem); // 打开对应用户信息的弹窗

// 辅助函数
const isCurrentUser = (item?: rankItem) => {
  return item?.user_id && myRank.value.user_id === item.user_id;
};

const formatRankData = (list: rankItem[]) => {
  if (!list || !Array.isArray(list)) return [];
  return list.map((item) => ({
    ...item,
    score: formatNumberToThousands(Number(item.score), false),
    bonus: formatNumberToThousands(Number(item.bonus), false),
  }));
};

// 计算属性 当前展示的排行榜内容
const curRankList = computed(() => {
  const list = activeTab.value === "previous" ? props.historyRankList : props.currentRankList || [];
  return formatRankData(list);
});

const topThree = computed(() => curRankList.value.slice(0, 3));

const prevRankList = computed(() => curRankList.value.slice(3));

const myRank = computed(() =>
  activeTab.value === "previous"
    ? props.detailData.my_historyRank || {}
    : props.detailData.my_rank || {}
);

// 事件处理
const handleBetNow = () => {
  gameStore.handleBetNowNavigation({
    vendors: props.detailData.vendors.map(String),
    game_types: props.detailData.game_types.map(String),
  });
};

const showHelp = () => {
  // 显示帮助信息
  console.log("Show help information");
  // 这里可以添加显示帮助弹窗的逻辑
};

/**
 * 点击玩家打开玩家详情数据弹框
 */
const handleRank = (item?: rankItem) => {
  if (isEmpty(item) || !item) return;
  showModal.value = true;
  player.value = item;
};
onMounted(() => {
  preloadImages([ModalTabBg]);
});
</script>

<style lang="scss" scoped>
.tournament-rankList {
  font-family: "D-DIN";
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 16px;

  .back-icon,
  .help-icon {
    font-size: 24px;
    color: white;
    cursor: pointer;
  }

  .title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
  }
}

.tabs {
  display: flex;
  width: 278px;
  margin: 0 auto;
  background: url("@/assets/images/tournament/tabs-bg.png") no-repeat;
  background-size: 100% 100%;
  height: 40px;
  font-size: 16px;
  font-weight: 600;
  font-family: "Inter";

  button {
    width: 157px;
    border: none;
    background: transparent;
    color: #fff;
    font-family: "Inter";
    letter-spacing: 0%;

    &:last-child {
      flex: 1;
    }

    &.active {
      background: url("@/assets/images/tournament/tab-active.png") no-repeat;
      background-size: 100% 100%;
    }
  }
}

.podium {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 0px 12px;
  gap: 2px;
  margin-top: 10px;
  max-height: 198px;
  height: 180px;
}

.rank-table {
  margin: 0 12px;
  overflow: hidden;
  color: #fff;

  .table-header {
    text-align: center;
    padding: 14px 0 8px;
    font-family: "Inter";
    font-weight: 500;
    font-size: 13px;
    line-height: 100%;
    letter-spacing: 0%;
    text-align: center;
  }

  .table-content {
    max-height: calc(100vh - 328px);
    overflow-y: auto;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    color: #6c5ce7;
  }
}

.empty-state {
  margin: 40px auto;
  text-align: center;
}
</style>
