<template>
  <ZPage title="奖励钱包返回逻辑测试">
    <div class="test-container">
      <h2>奖励钱包返回逻辑测试</h2>

      <div class="test-section">
        <h3>当前状态</h3>
        <p>用户ID: {{ userId }}</p>
        <p>已访问奖励钱包: {{ hasVisited ? "是" : "否" }}</p>
        <p>上次奖金记录: {{ lastContentStr }}</p>
      </div>

      <div class="test-section">
        <h3>测试操作</h3>
        <button @click="resetStatus" class="test-btn">重置访问状态</button>
        <button @click="markVisited" class="test-btn">标记已访问</button>
        <button @click="testSaveContent" class="test-btn">测试保存内容</button>
        <button @click="checkRedirect" class="test-btn">检查是否应该跳转</button>
        <button @click="goToHome" class="test-btn">返回首页</button>
        <button @click="goToBonusWallet" class="test-btn">前往奖励钱包</button>
      </div>

      <div class="test-section">
        <h3>测试结果</h3>
        <div class="result-box">
          <pre>{{ testResult }}</pre>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useGlobalStore } from "@/stores/global";
import { bonusWalletManager } from "@/utils/managers/BonusWalletManager";
import { getLocalStorage } from "@/utils/core/Storage";

const router = useRouter();
const globalStore = useGlobalStore();

const userId = ref("");
const hasVisited = ref(false);
const lastContentStr = ref("");
const testResult = ref("");

const updateStatus = () => {
  userId.value = globalStore.userInfo?.user_id || "unknown";

  // 检查访问状态
  const visitedKey = `bonus_wallet_visited_${userId.value}`;
  hasVisited.value = getLocalStorage(visitedKey) === "true";

  // 检查上次内容记录
  const contentKey = `bonus_wallet_last_content_${userId.value}`;
  const content = getLocalStorage(contentKey);
  lastContentStr.value = content ? JSON.stringify(JSON.parse(content), null, 2) : "无记录";
};

const resetStatus = () => {
  bonusWalletManager.resetUserVisitStatus();
  updateStatus();
  testResult.value = "已重置用户访问状态";
};

const markVisited = () => {
  bonusWalletManager.markUserVisitedBonusWallet();
  updateStatus();
  testResult.value = "已标记用户访问状态";
};

const checkRedirect = async () => {
  try {
    const shouldRedirect = await bonusWalletManager.shouldPerformFirstTimeRedirect();
    testResult.value = `检查结果: ${shouldRedirect ? "应该跳转" : "不应该跳转"}`;
    updateStatus();
  } catch (error) {
    testResult.value = `检查失败: ${error.message}`;
    console.error("检查跳转失败:", error);
  }
};

const testSaveContent = () => {
  try {
    // 测试保存奖金内容
    bonusWalletManager.saveCurrentBonusContent(
      [{ id: 1, bonus: 100 }], // 模拟奖金列表
      1, // 可领取数量
      userId.value
    );
    testResult.value = "测试保存奖金内容成功";
    updateStatus();
  } catch (error) {
    testResult.value = `保存测试失败: ${error.message}`;
    console.error("保存测试失败:", error);
  }
};

const goToHome = () => {
  router.push("/home");
};

const goToBonusWallet = () => {
  router.push("/bonus-wallet");
};

onMounted(() => {
  updateStatus();
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-btn {
  margin: 5px;
  padding: 10px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background: #0056b3;
}

.result-box {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  min-height: 100px;
}

h2,
h3 {
  color: #333;
}

p {
  margin: 8px 0;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
