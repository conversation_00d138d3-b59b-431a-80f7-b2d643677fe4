<script lang="ts" setup>
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useWithdrawStore } from "@/stores/withdraw";
import ProgressBar from "./ProgressBar.vue";
// @ts-ignore
import MathUtils from "@/utils/core/math";
import { amountFormatThousands } from "@/utils/core/tools";
import { useGameStore } from "@/stores/game";
const withdrawStore = useWithdrawStore();
const gameStore = useGameStore();
const { showRiskWithdrawalDialog, riskWithdrawalInfo } = storeToRefs(withdrawStore);

// 使用 computed 实时计算目标值与有效投注额的差值
const diffValue = computed(() => {
  // 注意 riskWithdrawalInfo.value 可能为空，所以加默认值保护
  const target = riskWithdrawalInfo.value?.target_amount || 0;
  const validBet = riskWithdrawalInfo.value?.user_total_valid_bet || 0;
  return MathUtils.subtract(target, validBet);
});

const goBet = async () => {
  await gameStore.clickBetNow(() => (withdrawStore.showRiskWithdrawalDialog = false));
};
</script>
<template>
  <ZPopOverlay :show="showRiskWithdrawalDialog">
    <div class="wrap">
      <div class="close" @click="showRiskWithdrawalDialog = false">
        <ZIcon type="icon-guanbi2" color="#fff" :size="30" />
      </div>
      <div class="top">
        <img class="img" src="@/assets/images/account/dialog_risk_top.png" />
      </div>
      <div class="content">
        <div class="title">Before Withdrawal</div>
        <div class="module">
          <div class="desc">
            You need to complete
            <span class="money">{{ amountFormatThousands(diffValue) }} ₱</span> bet turnover
          </div>
          <div class="rate-wrap">
            <div class="rate-name">Valid Bet</div>
            <div class="rate-outer">
              <ProgressBar
                :current="riskWithdrawalInfo?.user_total_valid_bet"
                :total="riskWithdrawalInfo?.target_amount"
              />
            </div>
          </div>
        </div>
        <GradientButton
          class="btn"
          background-gradient="linear-gradient(180deg, #FF1E35 20.59%, #FF916C 94.85%)"
          border-gradient="#FFDFBF"
          :showLight="true"
          @click="goBet"
          >Go Bet</GradientButton
        >
      </div>
    </div>
  </ZPopOverlay>
</template>

<style lang="scss" scoped>
.wrap {
  width: 320px;
  height: 270px;
  padding: 20px;
  background: linear-gradient(359deg, #fff 39.98%, #fff7dd 96.1%), #fff;
  border-radius: 16px;
  position: relative;
  z-index: 9;
  background-color: #fff;

  .top {
    position: absolute;
    top: -75px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;

    .img {
      height: auto;
      width: 100%;
    }
  }

  .close {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
  }

  .content {
    padding-top: 40px;

    .btn {
      margin-top: 20px;
      border-radius: 100px;
      width: 100%;
    }

    .title {
      color: #000;
      text-align: center;
      font-family: Inter;
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: 100%;
      /* 18px */
    }

    .module {
      background: #f4f4f4;
      padding: 16px 12px;
      border-radius: 12px;
      margin-top: 12px;

      .desc {
        color: #000;
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 100%;

        .money {
          color: #0eb100;
          font-family: DIN;
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 100%;
        }

        /* 12px */
      }

      .rate-wrap {
        margin-top: 12px;
        display: flex;

        .rate-outer {
          flex: 1;
        }

        .rate-name {
          text-align: left;
          width: 70px;
          color: #465664;
          font-family: Inter;
          font-size: 13px;
          font-style: normal;
          font-weight: 400;
          line-height: 100%;
          /* 13px */
        }
      }
    }
  }
}
</style>
