import http from "@/utils/http";

// 获取消息列表
export const getMessageList = (data) => {
  return http.post("/common/api/sys/inbox", data, {
    type: "formData",
  });
};

// 已读
export const postReadMessage = (data) => {
  return http.post("/common/api/sys/read/inbox", data, {
    type: "formData",
  });
};

// 删除
export const postDelMessage = (data) => {
  return http.post("/common/api/sys/del/inbox", data, {
    type: "formData",
  });
};
