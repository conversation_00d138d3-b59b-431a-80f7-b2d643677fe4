<template>
  <ZActionSheet v-model="showPWAInstallTip" :onClose="handleClose">
    <div class="pwa-install-guide">
      <div class="content">
         <img class="nustar" src="/icon-192.png"></img>
        <div class="description">
         {{pwaInfo.info}}
        </div>
      </div>
    </div>
    <template #footer>
      <ZButton type="default" @click="handleDownloadApp" class="theme-primary btn" v-if="pwaInfo.desktop_shortcut_key!=1">
         <img src="@/assets/images/home/<USER>" class="enjoyMoreBenefits"></img>
        <ZIcon type="icon-app" color="#fff" :size="24"></ZIcon>
        <span class="btn-text">APP</span>
      </ZButton>
      <ZButton type="primary" @click="installPWA" class="theme-default btn" v-if="pwaInfo.desktop_shortcut_key!=1">
        <ZIcon type="icon-web" color="#ac1140" :size="26"></ZIcon>
        <span class="btn-text">WEB-APP</span>
      </ZButton>
      <ZButton type="primary" @click="installPWA" class="theme-primary btn" v-if="pwaInfo.desktop_shortcut_key==1" >
        <img src="@/assets/images/home/<USER>" class="enjoyMoreBenefits"></img>
        <ZIcon type="icon-web" color="#fff" :size="26"></ZIcon>
        <span class="btn-text">WEB-APP</span>
      </ZButton>
    </template>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { recordPWAPromptShown } from "@/utils/pwa";
import { getForceUpdateConfig } from "@/api/activity";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import { showToast } from "vant";

/**
 * PWA 安装引导组件
 * 处理 PWA 安装提示和用户交互
 */

const autoPopMgrStore = useAutoPopMgrStore();
const { showPWAInstallTip,pwaInfo } = storeToRefs(autoPopMgrStore);
const appInfo = ref<any>({});

/**
 * 处理 PWA 安装
 * 触发浏览器的安装提示
 */
const installPWA = async (): Promise<void> => {
  window.installPWA?.(handleFail);
}

const handleDownloadApp = async() => {
  if (appInfo.value.download_url) {
     await MobileWindowManager.launchExternalGame(
          async () => appInfo.value.download_url,
          (error) => {
            console.error("Failed to open GCash URL:", error);
            showToast("Failed to open , please try again");
          }
        );
  }
}

const handleFail = () => {
  handleClose()
}

/**
 * 处理关闭按钮点击
 */
const handleClose = (): void => {
 autoPopMgrStore.showPWAInstallTip = false;
 AutoPopMgr.destroyCurrentPopup();
};


const fetchForceUpdateConfig = async() => {
  const res = await getForceUpdateConfig()
  appInfo.value = res
}

watch(
  () => showPWAInstallTip.value,
  (newVal) => {
    if (newVal) {
      fetchForceUpdateConfig()
      // 从 pwaInfo 中获取每日最大弹窗次数，如果没有配置则使用默认值 3
      const maxDailyPrompts = pwaInfo.value?.PWA_open_times || 3;
      recordPWAPromptShown(maxDailyPrompts)
    }
  },
  { immediate: true }
);

</script>

<style scoped lang="scss">
.not-available {
  padding-top: 40px;
  font-family: Inter;
  font-size: 16px;
}
.footer{
  position: relative;
}
.enjoyMoreBenefits{
  position: absolute;
  right:0;
  top:-40px;
  width: 190px;
  height: 44px;
  margin-bottom: -10px;
  z-index:2;
}
.btn {
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px; /* 150% */
  position: relative;

  :deep(.van-button__text) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
  &.theme-default {
    color: #ac1140;
    background-color: #fff;
    border: 2px solid #ac1140;
  }
  &.theme-primary {
    color: #fff;
    background-color: #ac1140;
  }
}

.pwa-install-guide {
  .content {
    display: flex;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 16px;
    gap: 12px;
    .nustar {
      width: 60px;
    }
    .description {
      flex: 1;
      color: #222;
      text-align: left;
      font-family: Inter;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
</style>
