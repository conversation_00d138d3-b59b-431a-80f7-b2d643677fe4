/**
 * 游戏分类页面相关类型定义
 */

import type { Ref, ComputedRef } from "vue";

// 游戏接口
export interface Game {
  id: string | number;
  game_id?: string | number;
  name?: string;
  is_like?: string | number;
  big_images_set?: number;
  table_group?: number;
  company_id?: string | number;
  [key: string]: any;
}

// 游戏分类接口
export interface GameCategory {
  id: string | number;
  name: string;
  game_type?: string;
  allGames?: Game[];
  pagedGames?: Game[];
  hasMore?: boolean;
  [key: string]: any;
}

// Casino 子分类接口
export interface CasinoTab {
  id: number;
  name: string;
  allGames: Game[];
  games: Game[];
  hasMore: boolean;
}

// 搜索和筛选状态接口
export interface FilterState {
  searchValue: string;
  selectedCategories: string[];
}

// 共享筛选状态管理接口
export interface SharedFiltersComposable {
  filterState: Ref<FilterState>;
  hasFilters: ComputedRef<boolean>;
  initFromQuery: () => void;
  syncToQuery: () => void;
  setSearchValue: (value: string) => void;
  setSelectedCategories: (categories: string[]) => void;
  clearFilters: () => void;
  resetFilters: () => void;
}

// 分页状态接口
export interface PaginationState {
  pageSize: number;
  pageMap: Record<string | number, number>;
}

// 厂商详细信息接口
export interface ProviderDetail {
  id: string;
  provider: string;
}

// 组件 Props 接口
export interface CustomNavbarProps {
  confirm: (val: string[], providerDetails: ProviderDetail[]) => void;
  hasFilters?: boolean;
  searchValue?: string;
  selectedProviders?: string[];
  providerDetails?: ProviderDetail[];
}

export interface SwitchCateProps {
  categoryId: string | number;
  selectedCategories: string[];
  searchValue: string;
}

// 组件 Emits 接口
export interface CustomNavbarEmits {
  (e: "search", value: string): void;
  (e: "confirm-filters", providers: string[], details: ProviderDetail[]): void;
  (e: "clear-filters"): void;
}

export interface SwitchCateEmits {
  (e: "clearFilters"): void;
}

// API 响应接口
export interface GameApiResponse {
  data?: Game[];
  [key: string]: any;
}

// 缓存接口
export interface GameCache {
  [key: string]: Game[];
}

// 滚动状态接口
export interface ScrollState {
  lastScrollTopMap: Record<string | number, number>;
  isDragging: boolean;
  isHorizontalScroll: boolean;
  isDirectionLocked: boolean;
}
