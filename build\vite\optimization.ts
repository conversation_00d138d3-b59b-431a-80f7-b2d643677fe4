/**
 * Vite 构建优化配置
 */

import type { BuildOptions } from "vite";
import { getSimpleBuildConfig, getSimpleOptimizeDeps, getSimpleCSSConfig } from "./simple-build";

/**
 * 获取构建优化配置
 */
export function getBuildOptimization(isDrop: boolean): BuildOptions {
  // 直接使用最简单的构建配置，避免复杂的代码分割
  return getSimpleBuildConfig(isDrop);
}

/**
 * 获取依赖预构建优化配置（简单版本）
 */
export function getOptimizeDeps() {
  return getSimpleOptimizeDeps();
}

/**
 * 获取 CSS 优化配置
 */
export function getCSSOptimization() {
  return getSimpleCSSConfig();
}

/**
 * 获取服务器优化配置
 */
export function getServerOptimization() {
  return {
    // 预热常用文件
    warmup: {
      clientFiles: ["./src/main.ts", "./src/App.vue", "./src/router/index.ts"],
    },
    // 文件系统缓存
    fs: {
      // 允许访问的文件
      allow: [".."],
    },
  };
}

/**
 * 性能监控配置
 */
export function getPerformanceConfig() {
  return {
    // 性能提示
    hints: "warning" as const,
    // 最大入口点大小 (bytes)
    maxEntrypointSize: 1024 * 1024, // 1MB
    // 最大资源大小 (bytes)
    maxAssetSize: 1024 * 1024 * 2, // 2MB
  };
}
