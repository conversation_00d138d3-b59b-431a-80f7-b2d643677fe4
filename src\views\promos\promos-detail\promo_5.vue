<template>
  <RankingLayout config-id="promo5" @help-click="handleHelpClick" @tab-change="handleTabChange" />
</template>

<script setup>
import { useRouter } from "vue-router";
import RankingLayout from "../Components/RankingLayout.vue";
const router = useRouter();

// 事件处理
const handleHelpClick = () => {
  router.push("/promos/promo5_tip");
};

const handleTabChange = (tab) => {
  console.log("Tab changed to:", tab);
};
</script>

<!-- Styles are now handled by the RankingLayout component -->
<style lang="scss" scoped></style>
