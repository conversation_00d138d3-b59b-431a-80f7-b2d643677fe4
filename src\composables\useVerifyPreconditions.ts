import { ref, nextTick } from "vue";
import { useGlobalStore } from "@/stores/global";

/**
 * 验证前置条件的 composable
 * 统一处理手机号和支付密码的验证逻辑
 */
export function useVerifyPreconditions() {
  const globalStore = useGlobalStore();
  const showVerifyDialogPreconditions = ref(false);
  const pendingSuccessCallback = ref<(() => void) | null>(null);

  /**
   * 检查用户是否满足前置条件（手机号和支付密码）
   * @returns {boolean} 是否满足条件
   */
  const checkPreconditions = (): boolean => {
    const userInfo = globalStore.userInfo;

    // 检查手机号
    if (!userInfo.phone) {
      return false;
    }

    // 检查支付密码
    // if (!userInfo.withdraw_password) {
    //   return false;
    // }

    // 检查登录密码
    if (!userInfo.login_password) {
      return false;
    }

    return true;
  };

  /**
   * 验证前置条件，如果不满足则显示验证弹窗
   * @param successCallback 验证成功后的回调函数
   * @returns {boolean} 是否立即满足条件（true表示立即执行成功回调，false表示需要等待用户完成验证）
   */
  const verifyPreconditions = (successCallback?: () => void): boolean => {
    if (checkPreconditions()) {
      // 条件已满足，立即执行回调
      successCallback?.();
      return true;
    } else {
      // 条件不满足，显示验证弹窗
      showVerifyDialogPreconditions.value = true;

      // 如果有成功回调，保存起来等待验证完成后执行
      if (successCallback) {
        pendingSuccessCallback.value = successCallback;
      }

      return false;
    }
  };

  /**
   * 前置条件验证完成后的处理函数
   * 在 VerifyDialogPreconditions 组件的 succCallBack 中调用
   */
  const handlePreconditionsConfirm = () => {
    showVerifyDialogPreconditions.value = false;

    nextTick(() => {
      // 执行之前保存的成功回调
      if (pendingSuccessCallback.value) {
        pendingSuccessCallback.value();
        pendingSuccessCallback.value = null;
      }
    });
  };

  /**
   * 获取当前用户缺失的前置条件
   * @returns {string[]} 缺失的条件列表
   */
  const getMissingPreconditions = (): string[] => {
    const userInfo = globalStore.userInfo;
    const missing: string[] = [];

    if (!userInfo.phone) {
      missing.push("phone");
    }

    if (!userInfo.login_password) {
      missing.push("loginPassword");
    }

    return missing;
  };

  return {
    // 响应式数据
    showVerifyDialogPreconditions,

    // 方法
    checkPreconditions,
    verifyPreconditions,
    handlePreconditionsConfirm,
    getMissingPreconditions,
  };
}
