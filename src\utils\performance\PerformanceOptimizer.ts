/**
 * 性能优化管理器
 * 统一管理各种性能优化策略
 */

import { routePreloader } from "./RoutePreloader";
import { componentPreloader } from "./ComponentPreloader";
import { resourcePreloader } from "./ResourcePreloader";

interface OptimizationConfig {
  enableRoutePreload: boolean;
  enableComponentPreload: boolean;
  enableResourcePreload: boolean;
  enablePredictivePreload: boolean;
  enableIdlePreload: boolean;
}

class PerformanceOptimizer {
  private config: OptimizationConfig = {
    enableRoutePreload: true,
    enableComponentPreload: true, // 组件预加载已优化，避免冲突
    enableResourcePreload: true,
    enablePredictivePreload: true,
    enableIdlePreload: true,
  };

  private initialized = false;

  /**
   * 初始化性能优化
   */
  async initialize(config?: Partial<OptimizationConfig>): Promise<void> {
    if (this.initialized) return;

    this.config = { ...this.config, ...config };
    console.log("🚀 开始初始化性能优化...");

    // 立即执行的高优先级预加载
    await this.preloadCriticalResources();

    // 延迟执行的预加载
    this.schedulePreloading();

    this.initialized = true;
    console.log("✅ 性能优化初始化完成");
  }

  /**
   * 预加载关键资源
   */
  private async preloadCriticalResources(): Promise<void> {
    const promises: Promise<void>[] = [];

    if (this.config.enableComponentPreload) {
      promises.push(componentPreloader.preloadCoreComponents());
    }

    if (this.config.enableResourcePreload) {
      promises.push(resourcePreloader.preloadCriticalImages());
    }

    if (this.config.enableRoutePreload) {
      promises.push(routePreloader.preloadCoreRoutes());
    }

    await Promise.all(promises);
  }

  /**
   * 安排预加载任务
   */
  private schedulePreloading(): void {
    // 1秒后开始预加载游戏相关资源
    setTimeout(() => {
      if (this.config.enableComponentPreload) {
        componentPreloader.preloadGameComponents();
      }
      if (this.config.enableResourcePreload) {
        resourcePreloader.preloadGameImages();
      }
    }, 1000);

    // 3秒后预加载弹窗组件
    setTimeout(() => {
      if (this.config.enableComponentPreload) {
        componentPreloader.preloadDialogComponents();
      }
    }, 3000);

    // 空闲时预加载低优先级资源
    if (this.config.enableIdlePreload) {
      this.scheduleIdlePreloading();
    }
  }

  /**
   * 安排空闲时预加载
   */
  private scheduleIdlePreloading(): void {
    if ("requestIdleCallback" in window) {
      requestIdleCallback(() => {
        this.preloadLowPriorityResources();
      });
    } else {
      setTimeout(() => {
        this.preloadLowPriorityResources();
      }, 5000);
    }
  }

  /**
   * 预加载低优先级资源
   */
  private preloadLowPriorityResources(): void {
    // 预加载其他页面路由
    const lowPriorityRoutes = [
      "/account/transactions",
      "/account/profile",
      "/account/security",
      "/promo-webview",
      "/game-webview",
    ];

    routePreloader.preloadRoutes({
      routes: lowPriorityRoutes,
      priority: "low",
    });
  }

  /**
   * 基于当前路由进行预测性预加载
   */
  onRouteChange(currentRoute: string): void {
    if (!this.config.enablePredictivePreload) return;

    // 路由预测预加载
    routePreloader.predictivePreload(currentRoute);

    // 组件预测预加载
    componentPreloader.preloadByPage(this.getPageName(currentRoute));
  }

  /**
   * 从路由路径获取页面名称
   */
  private getPageName(routePath: string): string {
    const pathMap: Record<string, string> = {
      "/home": "home",
      "/promos": "promos",
      "/news": "news",
      "/account": "account",
      "/game-categories": "games",
    };

    return pathMap[routePath] || "unknown";
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): {
    routes: { loaded: string[]; loading: string[] };
    components: { loaded: string[]; loading: string[] };
    resources: { loaded: string[]; loading: string[] };
  } {
    return {
      routes: routePreloader.getPreloadStatus(),
      components: componentPreloader.getPreloadStatus(),
      resources: resourcePreloader.getPreloadStatus(),
    };
  }

  /**
   * 手动触发预加载
   */
  manualPreload(type: "routes" | "components" | "resources" | "all"): void {
    switch (type) {
      case "routes":
        routePreloader.preloadCoreRoutes();
        break;
      case "components":
        componentPreloader.preloadCoreComponents();
        break;
      case "resources":
        resourcePreloader.preloadCriticalImages();
        break;
      case "all":
        this.preloadCriticalResources();
        break;
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): OptimizationConfig {
    return { ...this.config };
  }
}

// 导出单例
export const performanceOptimizer = new PerformanceOptimizer();
