<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { getMarquee } from "@/api/games";

// 定义通知数据
const marquee = ref([]);

// 获取通知数据的接口请求
const fetchMarqueeData = async () => {
  const res = await getMarquee();
  if (res) {
    marquee.value = res;
  }
};

// 计算动画持续时间
const getAnimationDuration = (content: string) => {
  const baseSpeed = 0.08; // 每个字符需要 0.2 秒
  const contentLength = content.replace(/<[^>]+>/g, "").length;
  return Math.max(contentLength * baseSpeed, 3); // 最少 3 秒
};

// 计算总动画时间
const totalDuration = computed(() => {
  return marquee.value.reduce((total, item) => {
    return total + getAnimationDuration(item.content);
  }, 0);
});

// 在组件挂载时请求数据
onMounted(() => {
  fetchMarqueeData();
});
</script>

<template>
  <div class="notification-bar">
    <ZIcon type="icon-laba" color="#666" :size="14" class="icon-laba"></ZIcon>
    <div class="marquee">
      <div class="marquee-content" :style="{ '--total-duration': totalDuration + 's' }">
        <div
          v-for="item in marquee"
          :key="item.id"
          class="marquee-item"
          :style="{
            '--item-duration': getAnimationDuration(item.content) + 's',
          }"
          v-html="item.content"
        ></div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.notification-bar {
  background-color: rgba(244, 247, 253, 1);
  display: flex;
  align-items: center;
  // margin: 0 16px;
  line-height: 32px;
  padding: 0 10px;
  color: rgba(34, 34, 34, 1);
  border-radius: 8px;
  overflow: hidden;

  .icon-laba {
    margin-right: 8px;
  }

  .marquee {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    display: flex;
    flex-wrap: nowrap;

    .marquee-content {
      display: inline-flex;
      animation: marquee var(--total-duration) linear infinite;
      animation-delay: 0s; // 立即开始动画
      display: flex;
      flex-wrap: nowrap;
      font-size: 14px;

      .marquee-item {
        display: flex;
        flex-wrap: nowrap;
        padding-right: 50px;
        min-width: 100%; // 确保每个项目至少占满容器宽度
        transform: translateX(90vw); // 初始位置从0开始
      }
    }
  }
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-100%);
  }
}
</style>
