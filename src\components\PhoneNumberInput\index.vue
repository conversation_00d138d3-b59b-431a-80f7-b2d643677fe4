<template>
  <div class="phone-number-input">
    <label v-if="label" :for="inputId" class="phone-label">{{ label }}</label>
    <div class="phone-input-container">
      <span class="country-code">+63</span>
      <input
        :id="inputId"
        v-model="inputValue"
        type="number"
        inputmode="numeric"
        :placeholder="placeholder"
        :maxlength="maxLength"
        :disabled="disabled"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        class="phone-input"
      />
      <ZIcon
        v-if="showClearButton && inputValue"
        type="icon-close"
        size="16"
        color="#999"
        @click="clearInput"
        class="clear-icon"
      />
    </div>
    <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { isPhilippinePhoneNumber } from "@/utils/core/tools";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";

interface Props {
  modelValue?: string;
  label?: string;
  placeholder?: string;
  maxLength?: number;
  disabled?: boolean;
  showClearButton?: boolean;
  validateOnInput?: boolean;
  validateOnBlur?: boolean;
  autoSave?: boolean;
  storageKey?: string;
  required?: boolean;
  errorMessages?: {
    required: string;
    invalid: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  label: "",
  placeholder: "Enter your phone number",
  maxLength: 10,
  disabled: false,
  showClearButton: true,
  validateOnInput: false,
  validateOnBlur: true,
  autoSave: false,
  storageKey: "PHONE",
  required: false,
  errorMessages: () => ({
    required: "Phone number is required",
    invalid: "Please enter a valid 10-digit mobile phone number",
  }),
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
  validityChange: [isValid: boolean];
  input: [value: string];
  blur: [value: string];
  focus: [value: string];
}>();

const inputValue = ref(props.modelValue);
const errorMessage = ref("");
const inputId = `phone-input-${Math.random().toString(36).substr(2, 9)}`;

// 计算属性：手机号是否有效
const isValid = computed(() => {
  if (!inputValue.value) return !props.required;
  return isPhilippinePhoneNumber(inputValue.value);
});

// 组件挂载时从本地存储恢复数据
onMounted(() => {
  if (props.autoSave && !props.modelValue) {
    const savedPhone = getLocalStorage(props.storageKey);
    if (savedPhone) {
      inputValue.value = savedPhone;
      emit("update:modelValue", savedPhone);
    }
  }
});

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal;
    if (props.validateOnInput) {
      validatePhone();
    }
  }
);

// 监听有效性变化
watch(
  () => isValid.value,
  (newValid) => {
    emit("validityChange", newValid);
  },
  { immediate: true }
);

// 处理输入事件
const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  let value = target.value;

  // 限制最大长度
  if (value.length > props.maxLength) {
    value = value.substring(0, props.maxLength);
    target.value = value;
  }

  inputValue.value = value;
  emit("update:modelValue", value);
  emit("input", value);

  // 清除错误信息
  if (errorMessage.value) {
    errorMessage.value = "";
  }

  // 实时验证
  if (props.validateOnInput) {
    validatePhone();
  }
};

// 处理失焦事件
const handleBlur = () => {
  emit("blur", inputValue.value);

  if (props.validateOnBlur) {
    validatePhone();
  }

  // 自动保存到本地存储
  if (props.autoSave && isValid.value) {
    setLocalStorage(props.storageKey, inputValue.value);
  }
};

// 处理聚焦事件
const handleFocus = () => {
  emit("focus", inputValue.value);
  // 聚焦时清除错误信息
  errorMessage.value = "";
};

// 清除输入
const clearInput = () => {
  inputValue.value = "";
  errorMessage.value = "";
  emit("update:modelValue", "");
  emit("input", "");
  emit("validityChange", !props.required);
};

// 验证手机号
const validatePhone = () => {
  if (!inputValue.value) {
    if (props.required) {
      errorMessage.value = props.errorMessages.required;
    } else {
      errorMessage.value = "";
    }
  } else if (!isPhilippinePhoneNumber(inputValue.value)) {
    errorMessage.value = props.errorMessages.invalid;
  } else {
    errorMessage.value = "";
  }
};

// 暴露验证方法给父组件
defineExpose({
  validate: validatePhone,
  clear: clearInput,
  focus: () => {
    const input = document.getElementById(inputId) as HTMLInputElement;
    input?.focus();
  },
  isValid: () => isValid.value,
});
</script>

<style scoped lang="scss">
.phone-number-input {
  width: 100%;

  .phone-label {
    display: block;
    margin-bottom: 8px;
    color: #666;
    font-size: 14px;
    font-weight: 400;
    font-family: Inter;
  }

  .phone-input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    background-color: #f4f7fd;
    transition: border-color 0.3s ease;

    &:focus-within {
      border-color: #4086f4;
      box-shadow: 0 0 0 2px rgba(64, 134, 244, 0.1);
    }

    &:hover {
      border-color: #bbb;
    }

    .country-code {
      padding: 0 12px;
      color: #333;
      font-size: 16px;
      font-weight: 600;
      font-family: D-DIN, sans-serif;
      border-right: 1px solid #eee;
      white-space: nowrap;
      user-select: none;
    }

    .phone-input {
      flex: 1;
      padding: 0 12px;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      font-family: D-DIN, sans-serif;

      &::placeholder {
        color: #999;
        font-weight: 400;
        font-family: Inter, sans-serif;
        font-size: 12px;
      }

      &:disabled {
        color: #999;
        cursor: not-allowed;
      }

      // 移除数字输入框的箭头
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type="number"] {
        -moz-appearance: textfield;
      }
    }

    .clear-icon {
      margin-right: 12px;
      cursor: pointer;
      opacity: 0.6;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 1;
      }
    }

    // 错误状态
    &.error {
      border-color: #ac1140;

      &:focus-within {
        border-color: #ac1140;
        box-shadow: 0 0 0 2px rgba(172, 17, 64, 0.1);
      }
    }

    // 禁用状态
    &.disabled {
      background-color: #f5f5f5;
      border-color: #e0e0e0;
      cursor: not-allowed;
    }
  }

  .error-message {
    margin-top: 6px;
    color: #ac1140;
    font-size: 12px;
    line-height: 1.4;
  }
}
</style>
