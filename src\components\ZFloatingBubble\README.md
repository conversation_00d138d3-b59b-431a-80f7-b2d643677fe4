# ZFloatingBubble 浮动气泡组件

## 概述

`ZFloatingBubble` 是基于 Vant 的 `van-floating-bubble` 组件封装的自定义浮动气泡组件，支持自定义内容、样式和交互行为。

## 功能特性

- ✅ **基于 Vant**：使用 Vant 4.6.0+ 的 FloatingBubble 组件
- ✅ **支持 Slot**：可以自定义气泡内容
- ✅ **灵活定位**：支持 left/right、top/bottom 定位
- ✅ **拖拽功能**：支持 x、y、xy 轴向拖拽
- ✅ **磁吸效果**：支持边缘磁吸功能
- ✅ **自定义样式**：支持颜色、大小、字体等自定义
- ✅ **响应式**：自动适配屏幕大小变化
- ✅ **TypeScript**：完整的类型支持

## Props 配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| left | 距离左边的距离（px） | number | null |
| right | 距离右边的距离（px） | number | null |
| top | 距离上边的距离（px） | number | null |
| bottom | 距离下边的距离（px） | number | null |
| color | 气泡背景颜色 | string | '#1989fa' |
| size | 气泡大小（px） | number | 50 |
| axis | 拖拽轴向 | 'x' \| 'y' \| 'xy' | 'xy' |
| magnetic | 磁吸效果 | 'x' \| 'y' \| 'xy' | 'x' |
| gap | 距离窗口边缘的最小间距（px） | number | 24 |
| fontSize | 字体大小（px） | number | 12 |
| textColor | 字体颜色 | string | '#fff' |
| shadow | 是否显示阴影 | boolean | true |

## Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| click | 点击气泡时触发 | event: Event |
| offset-change | 位置改变时触发 | offset: { x: number, y: number } |

## Methods 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| setPosition | 设置气泡位置 | (x: number, y: number) |
| getPosition | 获取当前位置 | - |
| resetPosition | 重置到初始位置 | - |

## 使用示例

### 1. 基础用法

```vue
<template>
  <ZFloatingBubble>
    默认气泡
  </ZFloatingBubble>
</template>

<script setup>
import ZFloatingBubble from '@/components/ZFloatingBubble/index.vue';
</script>
```

### 2. 自定义位置

```vue
<template>
  <!-- 左上角定位 -->
  <ZFloatingBubble :left="20" :top="100">
    左上角
  </ZFloatingBubble>
  
  <!-- 右下角定位 -->
  <ZFloatingBubble :right="20" :bottom="100">
    右下角
  </ZFloatingBubble>
</template>
```

### 3. 自定义样式

```vue
<template>
  <ZFloatingBubble 
    :size="60"
    color="#ff6b6b"
    text-color="#fff"
    :font-size="14"
    :shadow="true"
  >
    自定义样式
  </ZFloatingBubble>
</template>
```

### 4. 限制拖拽方向

```vue
<template>
  <!-- 只能水平拖拽 -->
  <ZFloatingBubble axis="x" magnetic="x">
    水平拖拽
  </ZFloatingBubble>
  
  <!-- 只能垂直拖拽 -->
  <ZFloatingBubble axis="y" magnetic="y">
    垂直拖拽
  </ZFloatingBubble>
</template>
```

### 5. 自定义内容

```vue
<template>
  <ZFloatingBubble :size="80" color="#4CAF50">
    <div class="custom-content">
      <van-icon name="chat-o" size="20" />
      <span>客服</span>
    </div>
  </ZFloatingBubble>
</template>

<style scoped>
.custom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
</style>
```

### 6. 事件处理

```vue
<template>
  <ZFloatingBubble 
    @click="handleClick"
    @offset-change="handleOffsetChange"
  >
    点击我
  </ZFloatingBubble>
</template>

<script setup>
const handleClick = (event) => {
  console.log('气泡被点击了', event);
};

const handleOffsetChange = (offset) => {
  console.log('位置改变了', offset);
};
</script>
```

### 7. 使用 ref 控制

```vue
<template>
  <div>
    <ZFloatingBubble ref="bubbleRef">
      可控制的气泡
    </ZFloatingBubble>
    
    <div class="controls">
      <button @click="moveToCenter">移动到中心</button>
      <button @click="resetPosition">重置位置</button>
      <button @click="getPosition">获取位置</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const bubbleRef = ref();

const moveToCenter = () => {
  const x = window.innerWidth / 2 - 25; // 减去气泡半径
  const y = window.innerHeight / 2 - 25;
  bubbleRef.value?.setPosition(x, y);
};

const resetPosition = () => {
  bubbleRef.value?.resetPosition();
};

const getPosition = () => {
  const position = bubbleRef.value?.getPosition();
  console.log('当前位置:', position);
};
</script>
```

### 8. 实际应用场景

#### 客服按钮
```vue
<template>
  <ZFloatingBubble 
    :right="20" 
    :bottom="100"
    :size="60"
    color="#07c160"
    @click="openCustomerService"
  >
    <van-icon name="service-o" size="24" />
  </ZFloatingBubble>
</template>

<script setup>
const openCustomerService = () => {
  // 打开客服对话框
  console.log('打开客服');
};
</script>
```

#### 返回顶部按钮
```vue
<template>
  <ZFloatingBubble 
    v-show="showBackTop"
    :right="20" 
    :bottom="200"
    :size="50"
    color="rgba(0, 0, 0, 0.6)"
    @click="scrollToTop"
  >
    <van-icon name="back-top" size="20" />
  </ZFloatingBubble>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const showBackTop = ref(false);

const handleScroll = () => {
  showBackTop.value = window.scrollY > 300;
};

const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>
```

## 注意事项

1. **Vant 版本要求**：需要 Vant >= 4.6.0 版本
2. **全局样式**：组件使用了全局样式来重置 Vant 的默认样式
3. **位置优先级**：left 优先于 right，top 优先于 bottom
4. **响应式**：组件会自动适配屏幕大小变化
5. **性能**：避免在循环中创建大量气泡组件

## 样式自定义

如果需要进一步自定义样式，可以通过全局样式覆盖：

```scss
.van-floating-bubble {
  .floating-bubble-content {
    // 自定义样式
    border: 2px solid #fff;
    
    &:hover {
      transform: scale(1.1) rotate(5deg);
    }
  }
}
```

通过这个封装的 ZFloatingBubble 组件，你可以轻松地在应用中添加各种浮动交互元素！
