<template>
  <div class="tournament-list">
    <!-- 空状态 -->
    <TournamentEmptyState
      v-if="processedTournaments.length === 0"
      message="No tournaments available"
    />

    <!-- 锦标赛列表 -->
    <template v-else>
      <TournamentCard
        v-for="tournament in processedTournaments"
        :key="tournament.id"
        :tournament="tournament"
        @click="handleTournamentClick"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: "Tournament" });

import { computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import TournamentCard from "./Components/TournamentCard.vue";
import TournamentEmptyState from "./Components/TournamentEmptyState.vue";
import { ProcessedTournament, Tournament } from "./configs";
import { processTournamentList } from "./utils/promoUtils";
import { statusBadgeMap } from "./configs";
import { getQueryByUrl, getQuery, preloadImages } from "@/utils/core/tools";
import pageBg from "@/assets/images/tournament/tournament-bg.png";
import rank1 from "@/assets/images/tournament/rank-first.png";
import rank2 from "@/assets/images/tournament/rank-second.png";
import rank3 from "@/assets/images/tournament/rank-third.png";
import topThreeBg from "@/assets/images/tournament/rank-top-bg.png";

const router = useRouter();

// Props定义
const props = defineProps<{
  tournamentList: Tournament[];
}>();

// 处理锦标赛数据
const processedTournaments = computed(() => {
  const processed = processTournamentList(props.tournamentList);
  // 为每个锦标赛添加徽章图片
  return processed.map((tournament) => ({
    ...tournament,
    badgeImage: statusBadgeMap[tournament.statusName || "waiting"],
  }));
});

// 外部传入数据
const query = getQueryByUrl(location.search);

// 处理锦标赛点击事件
const handleTournamentClick = (tournament: ProcessedTournament) => {
  const data = JSON.parse(JSON.stringify(query));
  data.id = tournament.id;
  const queryStr = getQuery(data);
  const route = "/promos/tournament-activePage";
  router.push(route + `?${queryStr}`);
};
onMounted(() => {
  preloadImages([pageBg, rank1, rank2, rank3, topThreeBg]);
});
</script>

<style scoped lang="scss">
.tournament-list {
  min-height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px 16px 40px;
  background: linear-gradient(179.96deg, #ffffff 0%, #f4f8fb 35%);
}
</style>
