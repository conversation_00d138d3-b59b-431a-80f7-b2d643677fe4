<template>
  <div class="form-item">
    <label class="label" v-if="label">
      <span class="required-mark" v-if="required">*</span>
      {{ label }}</label
    >
    <div
      class="photo-container"
      v-if="photoBase64"
      @click="showCamera = true"
      :style="{ height: `${height}px` }"
    >
      <img class="photo-img" :src="photoBase64" />
    </div>
    <div
      v-else
      class="photo-upload"
      @click="showCamera = true"
      :style="{ height: `${height}px`, background: hasError ? '#FFF8F6' : '#f4f7fd' }"
    >
      <ZIcon type="icon-guanbi2" color="red" :size="24" v-if="hasError"></ZIcon>
      <ZIcon type="icon-xiangji" color="#999" :size="32" v-else></ZIcon>
    </div>
    <div class="error-tip" v-if="hasError">
      <span v-if="errorType === UploadErrorType.REQUIRED">Please upload picture</span>
      <span v-else-if="errorType === UploadErrorType.UPLOAD_FAILED">The picture upload failed</span>
      <span v-else>Please upload picture</span>
    </div>
    <Photograph
      v-if="showCamera"
      @close="showCamera = false"
      @confirm="handleConfirm"
      :photoLabel="photoLabel"
    ></Photograph>
  </div>
</template>

<script setup lang="ts">
import Photograph from "./Photograph/index.vue";
import { uploadKycImg } from "@/api/kyc";
import { showToast } from "vant";
import { UploadErrorType } from "@/types/upload";

const photoBase64 = ref("");
const showCamera = ref(false);
const hasError = ref(false);
const errorType = ref<UploadErrorType | null>(null);

interface Props {
  required?: boolean;
  label?: string;
  height?: number;
  /** 初始照片 URL 或 base64 */
  modelValue?: string;
  photoLabel: string;
}
interface Emits {
  (e: "confirm", value: string): void;
  (e: "update:modelValue", value: string): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  required: true,
  label: "",
  height: 78,
  modelValue: "",
});

// 监听 modelValue 变化，实现照片回显
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && newValue !== photoBase64.value) {
      // 设置照片并清除错误状态
      photoBase64.value = newValue;
      hasError.value = false;
      errorType.value = null;
      console.log("照片回显设置成功:", newValue.substring(0, 50) + "...");
    }
  },
  { immediate: true }
);

const handleConfirm = async (value: string) => {
  try {
    const response: any = await uploadKycImg({ font_side_url: value });
    if (response.code == 200 || response.code == 0) {
      if (response.data) {
        // 上传成功
        photoBase64.value = value;
        hasError.value = false;
        errorType.value = null;

        // 触发事件
        emit("confirm", response.data.font_side_url);
        emit("update:modelValue", value);

        console.log("照片上传成功");
      } else {
        // 上传失败（服务器返回成功但无数据）
        hasError.value = true;
        errorType.value = UploadErrorType.UPLOAD_FAILED;
        console.warn("上传失败：服务器返回无数据");
      }
    } else {
      // 上传失败（服务器返回错误）
      hasError.value = true;
      errorType.value = UploadErrorType.UPLOAD_FAILED;
      showToast(response.msg || "Upload failed");
      console.warn("上传失败：", response.msg);
    }
  } catch (error) {
    // 网络错误或其他异常
    hasError.value = true;
    errorType.value = UploadErrorType.UPLOAD_FAILED;
    showToast("Upload failed, please try again");
    console.error("Upload error:", error);
  }
};

const validate = () => {
  if (!props.required) return true;

  if (!photoBase64.value) {
    // 未填写（未上传照片）
    hasError.value = true;
    errorType.value = UploadErrorType.REQUIRED;
    return false;
  }

  // 已上传且无错误
  hasError.value = false;
  errorType.value = null;
  return true;
};

// 清除错误状态
const clearError = () => {
  hasError.value = false;
  errorType.value = null;
};

// 获取错误类型
const getErrorType = () => {
  return errorType.value;
};

// 检查是否有照片
const hasPhoto = () => {
  return !!photoBase64.value;
};

// 直接设置照片值（用于回显，不触发上传）
const setValue = (value: string, skipUpload = false) => {
  if (skipUpload) {
    // 直接设置照片，不上传
    photoBase64.value = value;
    hasError.value = false;
    errorType.value = null;
    emit("update:modelValue", value);
    console.log("照片值已直接设置（跳过上传）");
  } else {
    // 正常上传流程
    handleConfirm(value);
  }
};

defineExpose({
  setValue,
  validate,
  clearError,
  getErrorType,
  hasPhoto,
});
</script>

<style scoped lang="scss">
.form-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  .error-tip {
    color: #ff4757;
    font-size: 12px;
    margin-top: 2px;
    height: 10px;
  }
  .label {
    margin-bottom: 8px;
    color: #666;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    .required-mark {
      color: #ff4757;
      margin-left: 2px;
    }
  }
}
.photo-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  border-radius: 20px;
  cursor: pointer;

  .photo-img {
    position: absolute;
    width: 100%;
    object-fit: fill;
    height: 100%;
    // transform-origin: 0 0;
    // transform: rotate(90deg) translateY(-100%);
  }
}

.photo-upload {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  border-radius: 20px;
  background: #f4f7fd;
  cursor: pointer;
}
</style>
