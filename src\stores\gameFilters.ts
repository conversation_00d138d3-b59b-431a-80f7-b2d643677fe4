/**
 * 游戏筛选状态全局 Store
 * 用于在 game-categories 和 casino-cate 页面间共享筛选状态
 */

import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { FilterState } from "@/views/game-categories/types";
import { GameFiltersStorage } from "@/utils/managers/GameFiltersStorage";

export const useGameFiltersStore = defineStore("gameFilters", () => {
  // ==================== 状态定义 ====================

  // 强制更新触发器
  const updateTrigger = ref(0);

  // 筛选状态 - 作为本地存储的代理
  const filterState = computed<FilterState>(() => {
    // 依赖 updateTrigger 来触发重新计算
    updateTrigger.value;
    const storage = GameFiltersStorage.getFilters();
    return {
      searchValue: storage.searchValue,
      selectedCategories: storage.selectedProviders,
    };
  });

  // 是否正在同步 URL 参数（防止循环更新）
  const isSyncingUrl = ref(false);

  // 订阅本地存储变化
  GameFiltersStorage.subscribe(() => {
    updateTrigger.value++;
  });

  // ==================== 计算属性 ====================

  // 是否有筛选条件
  const hasFilters = computed(() => {
    const storage = GameFiltersStorage.getFilters();
    return (
      (storage.selectedProviders.length > 0 && !storage.selectedProviders.includes("all")) ||
      (storage.searchValue && storage.searchValue.trim().length > 0)
    );
  });

  // ==================== 方法定义 ====================

  /**
   * 首次访问时从 URL query 参数初始化筛选状态（会重置所有状态）
   */
  const initFromQueryOnFirstVisit = (route: any) => {
    GameFiltersStorage.initFromQuery(route.query);
  };

  /**
   * 从 URL query 参数更新筛选状态（只更新有参数的部分）
   */
  const initFromQuery = (route: any) => {
    if (isSyncingUrl.value) {
      return;
    }
    GameFiltersStorage.initFromQuery(route.query);
  };

  /**
   * 设置搜索值
   */
  const setSearchValue = (value: string) => {
    GameFiltersStorage.setSearchValue(value);
  };

  /**
   * 设置厂商筛选
   */
  const setSelectedCategories = (categories: string[]) => {
    // 确保数组去重并过滤空值
    const cleanedCategories = [...new Set(categories)].filter((cat) => cat && cat.trim());
    const finalCategories = cleanedCategories.length > 0 ? cleanedCategories : ["all"];
    GameFiltersStorage.setSelectedProviders(finalCategories);
    console.log("setSelectedCategories:", finalCategories);
  };

  /**
   * 清除所有筛选条件
   */
  const clearFilters = () => {
    GameFiltersStorage.clearFilters();
  };

  /**
   * 重置筛选状态（不同步到 URL）
   */
  const resetFilters = () => {
    GameFiltersStorage.clearFilters();
  };

  /**
   * 处理搜索
   */
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  /**
   * 处理清除筛选条件
   */
  const handleClearFilters = () => {
    clearFilters();
  };

  /**
   * 处理确认筛选条件
   */
  const handleConfirmFilters = (categories: string[] = []) => {
    setSelectedCategories(categories);
  };

  // ==================== 返回 ====================

  return {
    // 状态
    filterState,
    hasFilters,
    isSyncingUrl,

    // 方法
    initFromQuery,
    initFromQueryOnFirstVisit,
    setSearchValue,
    setSelectedCategories,
    clearFilters,
    resetFilters,
    handleSearch,
    handleClearFilters,
    handleConfirmFilters,
  };
});
