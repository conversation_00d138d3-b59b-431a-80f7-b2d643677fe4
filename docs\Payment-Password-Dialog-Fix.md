# 支付密码弹窗关闭问题修复

## 🚨 问题描述

在 `safe-privacy.vue` 中使用更新支付密码弹窗：

```vue
<ZVerifyDialog 
  v-model:showDialog="showUpdatePaymentPasswordDialog"
  :verifyType="PN_VERIFY_TYPE.ChangePaymentPassword" 
  :succCallBack="() => {
    showUpdatePaymentPasswordDialog = false
  }"
>
</ZVerifyDialog>
```

当在 `VerifyDialogPaymentPassword.vue` 中成功设置密码后，虽然调用了 `props.succCallBack && props.succCallBack()`，但是弹窗没有关闭。

## 🔍 问题原因

### 弹窗层级结构

```
ZVerifyDialog (主弹窗)
├── visible (控制主弹窗显示)
└── VerifyDialogPaymentPassword (子弹窗)
    └── showNextDialog (控制子弹窗显示)
```

### 问题分析

1. **主弹窗**: `ZVerifyDialog` 使用 `v-model:showDialog` 控制显示
2. **子弹窗**: `VerifyDialogPaymentPassword` 使用 `v-model="showNextDialog"` 控制显示
3. **关闭逻辑**: 当密码设置成功时，只调用了 `handleCancel()` 关闭子弹窗
4. **缺失环节**: 没有通知主弹窗关闭

### 原来的代码流程

```typescript
// VerifyDialogPaymentPassword.vue 完成时
.then((res) => {
  globalStore.updateUserInfo({ withdraw_password: 1 });
  props.toastText && showToast(props.toastText);
  handleCancel(); // ❌ 只关闭子弹窗
  emit('complete');
  props.succCallBack && props.succCallBack(); // ❌ 回调执行但主弹窗仍显示
})
```

## ✅ 解决方案

### 1. 添加 close 事件

在 `VerifyDialogPaymentPassword.vue` 中添加 `close` 事件：

```typescript
// 添加 close 事件到 emit
const emit = defineEmits(["update:showNextDialog", 'complete', 'repeat', 'close']);

// 完成时通知父组件关闭主弹窗
.then((res) => {
  globalStore.updateUserInfo({ withdraw_password: 1 });
  props.toastText && showToast(props.toastText);
  handleCancel(); // 关闭当前弹窗
  emit('complete'); // 通知父组件完成
  emit('close'); // ✅ 通知父组件关闭主弹窗
  props.succCallBack && props.succCallBack();
})
```

### 2. 监听 close 事件

在 `ZVerifyDialog/index.vue` 中监听 `close` 事件：

```vue
<!-- 更新支付密码 -->
<template v-if="verifyType === PN_VERIFY_TYPE.ChangePaymentPassword">
  <VerifyDialogPaymentPassword 
    v-model="showNextDialog" 
    :succCallBack="succCallBack"
    @close="handleClose"
  ></VerifyDialogPaymentPassword>
</template>
```

### 3. 完整的关闭流程

```
1. 用户设置密码成功
2. VerifyDialogPaymentPassword 调用 handleCancel() (关闭子弹窗)
3. VerifyDialogPaymentPassword 发出 close 事件
4. ZVerifyDialog 监听到 close 事件，调用 handleClose()
5. ZVerifyDialog 发出 update:showDialog 事件
6. safe-privacy.vue 接收到事件，设置 showUpdatePaymentPasswordDialog = false
7. 主弹窗关闭
```

## 🎯 修复效果

现在当用户成功设置支付密码后：

1. ✅ 密码设置成功
2. ✅ 全局状态更新 (`withdraw_password: 1`)
3. ✅ 显示成功提示
4. ✅ 子弹窗关闭
5. ✅ 主弹窗关闭
6. ✅ 页面显示更新为 "Modify"

## 🔧 验证方法

### 测试步骤

1. 打开 `safe-privacy.vue` 页面
2. 点击 "Payment Password" 行（显示 "Not Set"）
3. 在弹出的密码设置弹窗中设置密码
4. 确认密码设置成功
5. 验证弹窗是否完全关闭
6. 验证页面显示是否更新为 "Modify"

### 预期结果

- ✅ 弹窗应该完全关闭，不留任何遮罩
- ✅ 页面应该显示 "Modify" 而不是 "Not Set"
- ✅ 不需要手动刷新页面

## 📋 其他类似问题

检查项目中是否还有其他类似的嵌套弹窗关闭问题：

```bash
# 搜索类似的弹窗使用模式
grep -r "ZVerifyDialog" src/
grep -r "VerifyDialog" src/
```

## 🎉 总结

这个修复解决了嵌套弹窗的关闭问题：

1. **子弹窗完成时**: 通过事件通知父弹窗关闭
2. **父弹窗响应**: 监听子弹窗的关闭事件
3. **完整关闭**: 确保整个弹窗层级都正确关闭
4. **状态同步**: 保持响应式状态更新

现在支付密码设置功能应该可以正常工作，弹窗会在完成后正确关闭！
