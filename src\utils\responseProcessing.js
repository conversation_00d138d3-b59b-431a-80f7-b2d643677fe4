import { showToast } from "vant";
import enTranslations from "@/utils/I18n/en.json";

/**
 * 统一处理SMS相关API响应的函数
 * @param {Object|number} responseOrCode - API响应对象 {code, msg, data} 或错误码
 * @param {Object} options - 配置选项
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @param {Object} options.countdownRef - 倒计时组件引用
 * @returns {boolean|string} 完整处理时返回是否成功，仅获取消息时返回错误消息
 */
export function handleSmsResponse(response, options = {}) {
  const { onSuccess, onError, countdownRef } = options;
  const { code, msg, data } = response || {};

  // 成功处理
  if (code === 200 || code === 0) {
    showToast(enTranslations.sendCodeMsgTip7);
    // 启动倒计时
    if (countdownRef?.value) {
      countdownRef.value.start();
    }
    // 执行成功回调
    if (typeof onSuccess === "function") {
      onSuccess(data);
    }
    return;
  }
  // 错误码映射表
  const responseTips = {
    103019: enTranslations.sendCodeMsgTip1, // 手机号码不存在
    103039: enTranslations.sendCodeMsgTip2, // 手机号格式不对
    103040: enTranslations.sendCodeMsgTip3, // 手机号被封
    103031: enTranslations.sendCodeMsgTip4, // 要先绑定手机号
    102031: enTranslations.sendCodeMsgTip5, // 手机号已经被绑定
    102041: enTranslations.sendCodeMsgTip6, // 短信发送失败
    102040: enTranslations.sendCodeMsgTip6, // 短信发送失败
    600: enTranslations.tipword23, // 特殊错误码
    200: enTranslations.sendCodeMsgTip7, // 成功提示
    0: enTranslations.sendCodeMsgTip7, // 成功提示
  };

  // 获取对应错误提示，没有则继续
  if (responseTips[code]) {
    showToast(responseTips[code]);
    // 执行错误回调
    if (typeof onError === "function") {
      onError(code, responseTips[code]);
    }
    return;
  }

  // 处理倒计时组件失败状态
  if (countdownRef?.value && typeof countdownRef.value.handleSendFailed === "function") {
    countdownRef.value.handleSendFailed();
  }

  return;
}
