<!--
  隐私政策确认弹窗组件
-->
<template>
  <ZActionSheet
    v-model="dialogVisible"
    title="Tips"
    :show-confirm-button="true"
    :show-cancel-button="false"
    :on-confirm="handleConfirm"
    :on-cancel="handleClose"
  >
    <div class="privacy-content">
      <div class="agreement-text">
        By logging in or registering, you confirm that you are over 21 years old, not listed in the
        PAGCOR NDRP, not a GEL licenseholder, and not a government employee. You also agree with the
        <span class="link-text" @click="handleNavigate('/protocal/terms-of-use')"
          >Terms of use</span
        >
        and
        <span class="link-text" @click="handleNavigate('/protocal/privacy-policy')"
          >Privacy Policy.</span
        >
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
/**
 * 隐私政策确认弹窗组件
 */
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useLoginStore } from "@/stores/login";

const router = useRouter();
const loginStore = useLoginStore();

/** 弹窗可见状态 */
const dialogVisible = computed({
  get: () => loginStore.isPrivacyDialogVisible,
  set: (value: boolean) => {
    loginStore.isPrivacyDialogVisible = value;
  },
});

/**
 * 处理确认按钮点击
 */
const handleConfirm = () => {
  loginStore.isPrivacyAgreed = true;
  handleClose();
};

/**
 * 处理关闭弹窗
 */
const handleClose = () => {
  loginStore.isPrivacyDialogVisible = false;
};

/**
 * 处理页面导航
 */
const handleNavigate = (path: string) => {
  router.push(path);
  handleClose();
};
</script>

<style scoped lang="scss">
.privacy-content {
  .agreement-text {
    color: #333;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px; /* 200% */
    font-style: normal;
    font-weight: 500;
    line-height: normal;

    .link-text {
      color: #f26026;
      font-weight: 500;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>
