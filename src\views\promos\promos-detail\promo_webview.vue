<template>
  <div class="iframe-container" v-show="!loading">
    <iframe
      ref="iframeRef"
      :src="url"
      @load="loading = false"
      @error="loading = false"
      frameborder="0"
      scrolling="auto"
      class="iframe-element"
    >
    </iframe>
  </div>
  <div v-if="loading" class="iframe-loading">
    <ZLoading />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";

const route = useRoute();
const { url } = route.query;
const loading = ref(true);
</script>

<style scoped lang="scss">
.iframe-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #fff;

  .iframe-element {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
.iframe-loading {
  width: 100%;
  height: 100vh;
  text-align: center;
  display: flex;
  align-items: center;
}
</style>
