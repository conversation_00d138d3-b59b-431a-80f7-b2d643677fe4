/**
 * Service Worker for PWA
 * 提供离线缓存和后台同步功能
 */

const CACHE_NAME = 'h5-web-vue-v1.0.0';
const STATIC_CACHE_NAME = 'h5-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'h5-dynamic-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// 需要缓存的 API 路径模式
const API_CACHE_PATTERNS = [
  /\/api\/games/,
  /\/api\/banners/,
  /\/api\/user/
];

// 不需要缓存的路径
const EXCLUDE_PATTERNS = [
  /\/api\/auth/,
  /\/api\/payment/,
  /\/api\/withdraw/
];

// Service Worker 安装事件
self.addEventListener('install', (event) => {
  console.log('SW: Service Worker 安装中...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('SW: 缓存静态资源');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('SW: 静态资源缓存完成');
        // 强制激活新的 Service Worker
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('SW: 静态资源缓存失败:', error);
      })
  );
});

// Service Worker 激活事件
self.addEventListener('activate', (event) => {
  console.log('SW: Service Worker 激活中...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // 删除旧版本的缓存
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName !== CACHE_NAME) {
              console.log('SW: 删除旧缓存:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('SW: Service Worker 激活完成');
        // 立即控制所有页面
        return self.clients.claim();
      })
  );
});

// 网络请求拦截
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 只处理同源请求
  if (url.origin !== location.origin) {
    return;
  }
  
  // 排除不需要缓存的请求
  if (EXCLUDE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return;
  }
  
  event.respondWith(
    handleRequest(request)
  );
});

// 处理请求的核心逻辑
async function handleRequest(request) {
  const url = new URL(request.url);
  
  try {
    // 对于 HTML 页面，使用网络优先策略
    if (request.destination === 'document') {
      return await networkFirst(request, STATIC_CACHE_NAME);
    }
    
    // 对于静态资源，使用缓存优先策略
    if (request.destination === 'script' || 
        request.destination === 'style' || 
        request.destination === 'image') {
      return await cacheFirst(request, STATIC_CACHE_NAME);
    }
    
    // 对于 API 请求，使用网络优先策略
    if (url.pathname.startsWith('/api/')) {
      // 检查是否是可缓存的 API
      if (API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
        return await networkFirst(request, DYNAMIC_CACHE_NAME, 5000); // 5秒超时
      } else {
        // 不缓存的 API 直接请求网络
        return await fetch(request);
      }
    }
    
    // 其他请求使用网络优先策略
    return await networkFirst(request, DYNAMIC_CACHE_NAME);
    
  } catch (error) {
    console.error('SW: 请求处理失败:', error);
    
    // 如果是页面请求失败，返回离线页面
    if (request.destination === 'document') {
      const cache = await caches.open(STATIC_CACHE_NAME);
      return await cache.match('/') || new Response('离线模式');
    }
    
    // 其他请求失败，返回错误响应
    return new Response('网络错误', { status: 503 });
  }
}

// 缓存优先策略
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    console.log('SW: 从缓存返回:', request.url);
    return cachedResponse;
  }
  
  console.log('SW: 缓存未命中，请求网络:', request.url);
  const networkResponse = await fetch(request);
  
  // 缓存成功的响应
  if (networkResponse.ok) {
    cache.put(request, networkResponse.clone());
  }
  
  return networkResponse;
}

// 网络优先策略
async function networkFirst(request, cacheName, timeout = 3000) {
  const cache = await caches.open(cacheName);
  
  try {
    // 设置超时的网络请求
    const networkResponse = await Promise.race([
      fetch(request),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('网络超时')), timeout)
      )
    ]);
    
    // 缓存成功的响应
    if (networkResponse.ok) {
      console.log('SW: 网络请求成功，更新缓存:', request.url);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('SW: 网络请求失败，尝试缓存:', request.url);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('SW: 从缓存返回:', request.url);
      return cachedResponse;
    }
    
    throw error;
  }
}

// 后台同步事件
self.addEventListener('sync', (event) => {
  console.log('SW: 后台同步事件:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // 这里可以添加后台同步逻辑
      console.log('SW: 执行后台同步')
    );
  }
});

// 推送通知事件
self.addEventListener('push', (event) => {
  console.log('SW: 收到推送通知:', event);
  
  const options = {
    body: event.data ? event.data.text() : '您有新消息',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看详情',
        icon: '/icons/icon-192x192.png'
      },
      {
        action: 'close',
        title: '关闭',
        icon: '/icons/icon-192x192.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('H5 App', options)
  );
});

// 通知点击事件
self.addEventListener('notificationclick', (event) => {
  console.log('SW: 通知被点击:', event);
  
  event.notification.close();
  
  if (event.action === 'explore') {
    // 打开应用
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// 消息事件（与主线程通信）
self.addEventListener('message', (event) => {
  console.log('SW: 收到消息:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});
