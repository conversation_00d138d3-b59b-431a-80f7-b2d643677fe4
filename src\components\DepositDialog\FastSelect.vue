<template>
  <div class="quick-amounts">
    <div
      v-for="tag in item.tags"
      :key="item.id"
      class="amount-item"
      :class="{ active: selectedAmount == tag }"
      @click="() => depositStore.setSelectedAmount(tag)"
    >
      <div class="value">{{ amountFormatThousands(tag.toLocaleString(), 0) }}</div>
      <div class="bonus" v-if="depositStore.getFirstBonusNum(tag) > 0 && isFirstDeposit">
        <div class="bonus-wrap">
          <div class="bouns-img"></div>
          <div class="triangle-img"></div>
          <span class="bouns-value"
            >+{{ amountFormatThousands(depositStore.getFirstBonusNum(tag), 0) }}</span
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useDepositStore } from "@/stores/deposit";
import { amountFormatThousands } from "@/utils/core/tools";
const depositStore = useDepositStore();
const { selectedAmount, isFirstDeposit } = storeToRefs(depositStore);

const props = defineProps({
  // recharge item
  item: {
    type: Object,
    required: true,
  },
});
</script>

<style lang="scss" scoped>
.quick-amounts {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;

  .amount-item {
    background: #f4f7fd;
    color: #666;
    border-radius: 999px;
    text-align: center;
    cursor: pointer;
    position: relative;
    font-weight: 700;
    width: 104px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 10px;
    border: 1px solid transparent;
    color: #666;
    font-family: D-DIN;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    &.isMaya {
      background-color: #01d46a;
    }

    &.active {
      border: 1px solid #ac1140;
      background: rgba(172, 17, 64, 0.05);
    }

    .triangle {
      position: absolute;
      bottom: -3px;
      left: 0px;
      width: 5px;
      height: 3px;
      background: url("../../assets/icons/account/deposit_triangle.svg") no-repeat;
    }

    .bonus {
      position: absolute;
      top: -6px;
      left: -5px;

      .bonus-wrap {
        display: flex;
        flex-direction: column;
        position: relative;

        .bouns-img {
          width: 52px;
          height: 18px;
          background: url("../../assets/icons/account/deposit_bouns.svg") no-repeat center;
          background-size: 100% 100%;
        }

        .triangle-img {
          width: 5px;
          height: 3px;
          flex-shrink: 0;
          background: url("../../assets/icons/account/deposit_triangle.svg") no-repeat center;
          background-size: 100% 100%;
        }

        .bouns-value {
          position: absolute;
          top: 2px;
          left: 2px;
          color: #fff;
          text-align: center;
          font-family: D-DIN;
          font-size: 12px;
          font-style: normal;
          font-weight: 700;
          line-height: normal;
        }
      }
    }

    .value {
      color: #666;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }
}
</style>
