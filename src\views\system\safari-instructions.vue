<!-- system/safari-instructions -->
<template>
  <ZPage :enableScroll="false">
    <div class="page">
      <div class="part">
        <img :src="Slice1" />
        <img :src="Slice2" />
        <img :src="Slice3" />
      </div>
      <div class="go-btn">
        <ZButton @click="handleGotIt">Got it</ZButton>
      </div>
    </div>
  </ZPage>
</template>
<script setup lang="ts">
import { useRouter } from "vue-router";
import Slice1 from "@/assets/images/home/<USER>/Slice1.png";
import Slice2 from "@/assets/images/home/<USER>/Slice2.png";
import Slice3 from "@/assets/images/home/<USER>/Slice3.png";

const router = useRouter();

const handleGotIt = () => {
  // 检查是否有存储的回调函数
  const safariTipsCallback = (window as any).__safariTipsCallback;

  if (safariTipsCallback && typeof safariTipsCallback === "function") {
    // 执行回调函数（游戏跳转逻辑）
    safariTipsCallback();

    // 清除回调函数
    delete (window as any).__safariTipsCallback;

    // 外跳成功后，替换当前历史记录，避免用户返回到这个页面
    // 使用 router.replace 跳转到首页，这样用户按返回键不会回到指导页面
    // router.replace("/home");
  } else {
    // 没有回调函数时，正常返回上一页
    router.back();
  }
};
</script>
<style lang="scss" scoped>
.page {
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden; // 防止页面级别的滚动

  .part {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden; // 只允许垂直滚动
    padding-top: 10px;
    padding-bottom: 100px; // 为底部按钮留出空间
    box-sizing: border-box;

    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }

  .go-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background-color: #fff;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    border-top: 1px solid #e0e0e0;
    z-index: 100;

    :deep(.z-button) {
      width: 100%;
      height: 52px;
      border-radius: 26px;
    }
  }
}
</style>
