// src/env.d.ts
/// <reference types="vite/client" />
/// <reference types="vite-svg-loader" />

export interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_PORT: number;
  readonly VITE_API_URL: string;
  readonly VITE_ASSETS_URL: string;
  readonly VITE_GCASH_SHOP_URL: string;
  readonly VITE_PROXY: Array<any>;
  readonly VITE_DROP_CONSOLE: boolean;
  readonly VITE_ENABLE_VCONSOLE: string;
  readonly VITE_BUILD_COMPRESS: string;
  readonly VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE: boolean;
  readonly VITE_CF_TURNSTILE_HOST: string;
  readonly VITE_CF_SITE_KEY_DEV: string;
  readonly VITE_CF_SITE_KEY_TEST: string;
  readonly VITE_CF_SITE_KEY_PRE: string;
  readonly VITE_CF_SITE_KEY_PROD: string;
  readonly VITE_VERIFICATION_TYPE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// SVG 模块类型声明
declare module "*.svg" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent;
  export default component;
}
