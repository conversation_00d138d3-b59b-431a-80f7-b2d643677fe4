import http from "@/utils/http";
// 获取kyc详情
//更换v2接口
// { "code": 200, "msg": "success","data": { "status": 2,  "reject": "",  "kyc_status": 1,// 加一个 3代表审核版本， "is_in_register_period":true// true,false }
export const getKycStatus = (data = {}) => {
  return http.post("/common/api/kyc/v2/user/status", data, {
    type: "formData",
  });
};

export const getKycData = (data = {}) => {
  return http.post("/common/api/kyc/v2/get/kyc", data, {
    type: "formData",
  });
};

// 获取kyc注册奖励提示
export const getKycBonus = (params = {}) => {
  return http.get("/open/api/activity/user/kyc/rw", { params });
};

// 图片上传
export const uploadKycImg = (data = {}) => {
  return http.post("/common/api/kyc/v2/img/upload", data, {
    transformResult: (res) => res.data,
  });
};
// 提交
export const postKycSubmit = (data = {}) => {
  return http.post("/common/api/kyc/v2/submit", data, {});
};
