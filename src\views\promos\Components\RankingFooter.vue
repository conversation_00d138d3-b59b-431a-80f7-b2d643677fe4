<template>
  <div class="ranking-footer" :style="footerStyle">
    <span class="rank-num">{{ userRank }}</span>
    <span class="user-label">{{ userLabel }}</span>
    <span class="rank-bet">
      <IconCoin />
      {{ formatBetAmount(userBetAmount) }}
    </span>
    <span class="rank-bonus">
      <IconCoin />
      {{ formatAward(userAward) }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from "vue";
import { formatAward, formatBetAmount } from "../utils/promoUtils";
import type { ThemeConfig } from "../configs";
import footBG from "@/assets/images/promos/promo7_footbg.png";

// Define component name for better debugging
defineOptions({
  name: "RankingFooter",
});

interface Props {
  userRank: string | number;
  userBetAmount: string | number;
  userAward: string | number;
  userLabel?: string;
  theme?: ThemeConfig;
  customStyle?: Record<string, string>;
}

const props = withDefaults(defineProps<Props>(), {
  userLabel: "Me",
});

// 计算样式
const footerStyle = computed(() => {
  const baseStyle = {
    // props.theme?.colors.footerBackground ||
    background: `url(${footBG})`,
    backgroundSize: "cover",
    backgroundRepeat: "no-repeat",
    ...props.customStyle,
  };

  return baseStyle;
});
</script>

<style lang="scss" scoped>
.ranking-footer {
  height: 16.7vw;
  z-index: 5;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  color: #ff9a3c;
  background-color: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 32px;
  font-size: 18px;
  font-weight: 600;

  .rank-num {
    color: #ff9a3c;
    font-weight: 700;
  }

  .user-label {
    color: #333;
    font-weight: 600;
  }

  .rank-bet,
  .rank-bonus {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #ff9a3c;
  }
}
</style>
