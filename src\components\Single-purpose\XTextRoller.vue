<template>
  <!-- 大转盘专用 底部右侧文本滚动组件 -->
  <div class="scroll-board" ref="boxRef">
    <ZScrollList :list="list" :step="0.4">
      <div v-for="(item, idx) in list" :key="idx" class="scroll-item">
        <span class="user">{{ item.user_id }}</span>
        <span class="amount">₱{{ formatNumberToThousands(item.prize_amount) }}</span>
        <span class="time">{{ item.prize_time }}</span>
      </div>
    </ZScrollList>
  </div>
</template>

<script setup lang="ts">
import ZScrollList from "@/components/ZScrollList/index.vue";
import { formatNumberToThousands } from "@/utils/core/tools";

const props = defineProps({
  list: { type: Array, default: [] },
});
</script>

<style scoped>
.scroll-board {
  height: 40px;
  overflow: hidden;
  border-radius: 8px;
  padding: 2px 0;
  box-sizing: border-box;
  font-size: 12px;
  margin-top: 8px;
  .scroll-item {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    color: #fff;
    font-weight: bold;
    font-family: D-DIN;

    .amount {
      color: #00f570;
    }
  }
}
</style>
