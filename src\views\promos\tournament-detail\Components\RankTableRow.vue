<template>
  <van-row
    class="table-row"
    :class="{
      'is-me': isCurrentUser,
      'top-rank': index < 3,
    }"
    @click="() => handleClick(rankData)"
  >
    <van-col span="3" class="rank">
      <img
        class="isMe-icon"
        v-if="isCurrentUser"
        src="@/assets/images/tournament/isMe.png"
        alt=""
      />
      <span v-if="rankData.rank <= 3" class="rank-medal" :class="`rank-${rankData.rank}`">
        {{ rankData.rank }}
      </span>
      <span v-else>{{ rankData.rank }}</span>
    </van-col>
    <van-col span="6" class="user-id">
      <img :src="getUserAvatar(rankData.avatar)" alt="Avatar" class="mini-avatar" />
      {{ maskString(rankData.user_id, 2, 3, 1) }}
    </van-col>
    <van-col span="7" class="bet">
      <IconCoin :size="16" />
      {{ rankData.score }}
    </van-col>
    <van-col span="6" class="bonus">
      <IconCoin :size="16" />
      {{ rankData.bonus }}
    </van-col>
    <van-col span="2">
      <img
        @click="() => handleClick(rankData)"
        class="arrow-right"
        src="@/assets/images/tournament/arrow-right.png"
        alt=""
      />
    </van-col>
  </van-row>
</template>

<script setup lang="ts">
defineOptions({ name: "RankTableRow" });

import { maskString, getUserAvatar } from "@/utils/core/tools";
import type { RankItem } from "../../configs";

interface Props {
  rankData: RankItem;
  index: number;
  isCurrentUser: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  click: [item: RankItem];
}>();

// 事件处理
const handleClick = (item: RankItem) => {
  emit("click", item);
};
</script>

<style lang="scss" scoped>
.table-row {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(90deg, #377aff 0%, #7040ff 80%);
  margin: 4px 0;
  border-radius: 10px;
  font-family: "D-DIN";
  font-weight: 700;
  font-size: 15px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  cursor: pointer;

  > div {
    padding: 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.is-me {
    position: relative;
    background: linear-gradient(90deg, #00aaff 0%, #9736ff 100%),
      linear-gradient(90deg, #00c4ff 0%, #9736ff 100%);
    border: 0.5px solid #ffffff;

    .isMe-icon {
      width: 32px;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .rank {
    font-weight: 700;
    font-size: 16px;
    color: #fff588;
    font-family: "D-DIN";

    .rank-medal {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      color: #fff;

      &.rank-1 {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #333;
      }

      &.rank-2 {
        background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
        color: #333;
      }

      &.rank-3 {
        background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
        color: #fff;
      }
    }
  }

  .user-id {
    display: flex;
    align-items: center;
    gap: 8px;

    .mini-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .bet,
  .bonus {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 700;
    font-size: 15px;
    line-height: 100%;
    letter-spacing: 0px;
  }

  .bonus {
    color: #fff588;
  }

  .arrow-right {
    width: 14px;
    cursor: pointer;
  }
}
</style>
