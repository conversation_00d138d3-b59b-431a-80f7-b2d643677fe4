import { onMounted, onUnmounted } from "vue";

export function useScroll(scrollRef: any, gamesListRef: any, callback: (isAtTop: boolean, isAtBottom: boolean) => void) {
  const handleScroll = () => {
    const scrollElement = scrollRef.value;
    const gamesList = gamesListRef.value;

    if (!scrollElement || !gamesList) return;

    // 获取游戏列表的位置信息
    const gamesRect = gamesList.getBoundingClientRect();
    const scrollRect = scrollElement.getBoundingClientRect();

    // 判断是否滚动到当前分类的底部或顶部
    const isAtBottom = gamesRect.bottom <= scrollRect.bottom;
    const isAtTop = gamesRect.top >= scrollRect.top;

    // 调用回调函数
    callback(isAtTop, isAtBottom);
  };

  onMounted(() => {
    const scrollElement = scrollRef.value;
    if (scrollElement) {
      scrollElement.addEventListener("scroll", handleScroll);
    }
  });

  onUnmounted(() => {
    const scrollElement = scrollRef.value;
    if (scrollElement) {
      scrollElement.removeEventListener("scroll", handleScroll);
    }
  });

  return {
    handleScroll,
  };
}
