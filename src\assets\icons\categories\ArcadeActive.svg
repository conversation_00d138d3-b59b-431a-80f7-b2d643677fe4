<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_717_3386)">
<rect width="24" height="24" rx="10" fill="url(#paint0_linear_717_3386)"/>
</g>
<mask id="mask0_717_3386" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" rx="10" fill="url(#paint1_linear_717_3386)"/>
</mask>
<g mask="url(#mask0_717_3386)">
<path d="M26.5 18.5L17.5 9.5L4.5 17L14.5 28L26.5 18.5Z" fill="#E90800"/>
<g filter="url(#filter1_i_717_3386)">
<path d="M5.54415 10.3674C5.81638 9.55069 6.58066 8.99983 7.44152 8.99983H16.5585C17.4193 8.99983 18.1836 9.55069 18.4558 10.3674L20 14.9998H4L5.54415 10.3674Z" fill="white"/>
</g>
<ellipse cx="12" cy="12" rx="5" ry="2" fill="#FF672B"/>
<g filter="url(#filter2_i_717_3386)">
<path d="M4 14.9998H20V15.9998C20 17.1044 19.1046 17.9998 18 17.9998H6C4.89543 17.9998 4 17.1044 4 15.9998V14.9998Z" fill="#FFE0CA"/>
</g>
<rect x="11" y="5" width="2" height="7" rx="1" fill="white"/>
<ellipse cx="12" cy="5.5" rx="3" ry="2.5" fill="#00E9E5"/>
</g>
<path opacity="0.6" d="M11.3193 20.582L11.9004 20.9004L11.3193 21.2178L11.001 21.7998L10.6826 21.2178L10.1006 20.9004L10.6826 20.582L11.001 20L11.3193 20.582ZM4.30176 17.4395L5.23926 17.9521L4.30176 18.4648L3.78906 19.4023L3.27637 18.4648L2.33887 17.9521L3.27637 17.4395L3.78906 16.5029L4.30176 17.4395ZM22.1592 8.61426L22.7734 8.9502L22.1592 9.28613L21.8232 9.90039L21.4873 9.28613L20.873 8.9502L21.4873 8.61426L21.8232 8L22.1592 8.61426ZM5.18066 3.43555L5.69629 3.71777L5.18066 4L4.89844 4.51562L4.61621 4L4.10059 3.71777L4.61621 3.43555L4.89844 2.9209L5.18066 3.43555ZM16.835 2.44043L17.5791 2.84766L16.835 3.25488L16.4277 4L16.0205 3.25488L15.2754 2.84766L16.0205 2.44043L16.4277 1.69531L16.835 2.44043Z" fill="white"/>
<defs>
<filter id="filter0_i_717_3386" x="0" y="0" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.914203 0 0 0 0 0.0309151 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_3386"/>
</filter>
<filter id="filter1_i_717_3386" x="4" y="8.99983" width="16" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.884381 0 0 0 0 0.804893 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_3386"/>
</filter>
<filter id="filter2_i_717_3386" x="4" y="14.9998" width="16" height="3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.800584 0 0 0 0 0.663485 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_3386"/>
</filter>
<linearGradient id="paint0_linear_717_3386" x1="0" y1="0" x2="24" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF7E3D"/>
<stop offset="1" stop-color="#FF3800"/>
</linearGradient>
<linearGradient id="paint1_linear_717_3386" x1="0" y1="0" x2="24" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF8A55"/>
<stop offset="1" stop-color="#FF6445"/>
</linearGradient>
</defs>
</svg>
