// 引入字体样式
@use "./fonts.scss" as *;

// 颜色变量
:root {
  --primary-color: #4a90e2;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --gold-color: #ffdb9e;
  --yellow-color: #ffdf5d;
  --red-color: #ac1140;
  // --small-gold-color: #FFDB9E;
  --black-color: #242b3c;
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --text-color-black: #111111;
  --text-color-white: #ffffff;
  --border-color: #dcdfe6;
  --background-color: #f5f7fa;

  --checkbox-color: #ac1140;

  --van-action-sheet-max-height: 100vh;

  // promos_3
  --untab-text-color: #b3b3b3;
  --promo3-border-color: #8431c8;
  --secondary-color: #250f3b;
  --accent-color: #c381fa;
  --line-color: #48325d;
  --border-color: #66239e;
}

// 重置默认样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

// 去除列表样式
ul,
ol {
  list-style: none;
}

// 去除链接下划线
a {
  text-decoration: none;
  color: inherit;
}

// 去除输入框外边框
input,
button {
  outline: none;
  border: none;
  background: none;
}

// 常用工具类
.flex {
  display: flex;
}
// 水平居中
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 水平布局
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
// 垂直布局
.flex-column {
  display: flex;
  flex-direction: column;
}

.padding-8 {
  padding: 8px;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
// 文本省略 两行
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 全屏
.full-page {
  min-height: 100vh;
  width: 100%;
}

// 底部安全区适配
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 顶部安全区适配
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

// 空状态样式
.empty-div {
  width: 100%;
  margin-top: 100px;
  text-align: center;
  color: #999;
  background-color: transparent;
  img {
    width: 180px;
    height: auto;
    margin: 0 auto;
  }
}
