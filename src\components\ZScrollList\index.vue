<template>
  <!-- 根容器：无缝滚动的外层盒子 -->
  <div
    ref="scrollRef"
    class="seamless-scroll"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @wheel="handleWheel"
  >
    <!-- 实际滚动内容的容器，通过 translate 位移实现滚动效果 -->
    <div ref="realBoxRef" :style="realBoxStyle">
      <!-- 主内容区域 -->
      <div ref="slotListRef" :style="floatStyle">
        <!-- 插槽：允许用户自定义渲染列表项 -->
        <slot :list="list"></slot>
      </div>
      <!-- 复制内容区域（用于无缝衔接滚动） -->
      <!-- 当 smartCopyNum > 0 时，复制一份内容，形成循环滚动视觉 -->
      <div :style="floatStyle" v-if="smartCopyNum > 0">
        <slot :list="list"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { throttle } from "lodash-es";
import { ref, computed, onMounted, watch, nextTick, CSSProperties, getCurrentInstance } from "vue";

// ==================== Props 定义 ====================
// 定义组件接收的所有属性（props）
const props = defineProps({
  // 要滚动的数据列表
  list: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 控制是否开启自动滚动（v-model 控制）
  modelValue: {
    type: Boolean,
    default: true,
  },
  // 滚动方向：上、下、左、右
  direction: {
    type: String,
    default: "up",
    validator: (val: string) => ["up", "down", "left", "right"].includes(val),
  },
  // 每帧移动的像素步长
  step: {
    type: Number,
    default: 1,
  },
  // 列表长度达到多少时才开启滚动
  limitScrollNum: {
    type: Number,
    default: 3,
  },
  // 是否在鼠标悬停时暂停滚动
  hover: {
    type: Boolean,
    default: false,
  },
  // 单行滚动时，每项的高度（单位：px 或 rem）
  singleHeight: {
    type: Number,
    default: 0,
  },
  // 单列滚动时，每项的宽度（单位：px 或 rem）
  singleWidth: {
    type: Number,
    default: 0,
  },
  // 每滚动完一项后停留的时间（毫秒）
  singleWaitTime: {
    type: Number,
    default: 1000,
  },
  // 是否使用 rem 单位进行尺寸计算
  isRemUnit: {
    type: Boolean,
    default: false,
  },
  // 动画延迟时间（ms）
  delay: {
    type: Number,
    default: 0,
  },
  // 动画缓动函数（支持字符串或贝塞尔曲线对象）
  ease: {
    type: [String, Object],
    default: "ease-in",
  },
  // 滚动循环次数限制（-1 表示无限）
  count: {
    type: Number,
    default: -1,
  },
  // 复制内容的份数（用于无缝衔接，0 表示不复制）
  copyNum: {
    type: Number,
    default: 1,
  },
  // 是否启用鼠标滚轮控制滚动
  wheel: {
    type: Boolean,
    default: false,
  },
  // 是否为单行/单列布局（flex 布局）
  singleLine: {
    type: Boolean,
    default: false,
  },
  // 是否监听 list 变化并重新初始化
  isWatch: {
    type: Boolean,
    default: true,
  },
});

// ==================== 事件定义 ====================
// 定义组件对外抛出的事件
const emit = defineEmits([
  "stop", // 滚动停止时触发
  "count", // 每完成一次循环触发
  "scroll", // 滚动过程中触发（返回当前索引）
  "update:modelValue", // 用于 v-model 双向绑定控制
]);

// ==================== DOM 引用与状态变量 ====================
const scrollRef = ref<HTMLDivElement>(null); // 外层容器引用
const slotListRef = ref<HTMLDivElement>(null); // 插槽内容引用
const realBoxRef = ref<HTMLDivElement>(null); // 实际滚动盒子引用
const reqFrame = ref<number | null>(null); // requestAnimationFrame ID
const singleWaitTimeout = ref<NodeJS.Timeout | null>(null); // 单步停留定时器
const realBoxWidth = ref(0); // 滚动盒子实际宽度
const realBoxHeight = ref(0); // 滚动盒子实际高度
const xPos = ref(0); // X 轴偏移量
const yPos = ref(0); // Y 轴偏移量
const isHover = ref(false); // 是否处于鼠标悬停状态
const loopCount = ref(0); // 当前已完成的循环次数
const scrollIndex = ref(0); // 当前滚动到的列表项索引

// ==================== 计算属性 ====================

// 判断是否满足滚动条件（列表长度 >= 限制数量）
const isScroll = computed(() => props.list.length >= props.limitScrollNum);

// 判断是否为水平滚动方向
const isHorizontal = computed(() => ["left", "right"].includes(props.direction));

// 智能复制数量：当数据只有1条时，不复制避免重复显示
const smartCopyNum = computed(() => {
  // 如果数据少于等于1条，不复制
  if (props.list.length <= 1) {
    return 0;
  }
  // 如果不满足滚动条件，也不复制
  if (!isScroll.value) {
    return 0;
  }
  // 否则使用配置的复制数量
  return props.copyNum;
});

// 获取根字体大小（用于 rem 单位转换）
const baseFontSize = computed(() => {
  if (!props.isRemUnit) return 1;
  return parseInt(getComputedStyle(document.documentElement).fontSize) || 16;
});

// 实际单步宽度（考虑 rem 转换）
const realSingleStopWidth = computed(() => props.singleWidth * baseFontSize.value);

// 实际单步高度（考虑 rem 转换）
const realSingleStopHeight = computed(() => props.singleHeight * baseFontSize.value);

// 最终使用的步长（校验合理性）
const step = computed(() => {
  let singleStep = isHorizontal.value ? realSingleStopWidth.value : realSingleStopHeight.value;
  if (singleStep > 0 && singleStep % props.step > 0) {
    console.warn("step 需是单步大小的约数，否则可能导致滚动位置不准确");
  }
  return props.step;
});

// 滚动盒子的样式（包含位移和过渡效果）
const realBoxStyle = computed<CSSProperties>(() => ({
  width: realBoxWidth.value ? `${realBoxWidth.value}px` : "auto",
  transform: `translate(${xPos.value}px, ${yPos.value}px)`,
  transition: `all ${
    typeof props.ease === "string"
      ? props.ease
      : `cubic-bezier(${props.ease.x1},${props.ease.y1},${props.ease.x2},${props.ease.y2})`
  } ${props.delay}ms`,
  overflow: "hidden",
  display: props.singleLine ? "flex" : "block",
}));

// 插槽内容的浮动样式（水平滚动需要 float: left）
const floatStyle = computed<CSSProperties>(() =>
  isHorizontal.value
    ? {
        float: "left",
        overflow: "hidden",
        display: props.singleLine ? "flex" : "block",
        flexShrink: props.singleLine ? 0 : 1,
      }
    : { overflow: "hidden" }
);

// ==================== 工具函数与滚动逻辑 ====================

// 取消当前动画帧
const cancelAnimation = () => {
  if (reqFrame.value) {
    window.cancelAnimationFrame(reqFrame.value);
    reqFrame.value = null;
  }
};

// 核心动画函数：根据方向和步长执行位移
const animation = (direction: string, step: number, isWheel?: boolean) => {
  reqFrame.value = window.requestAnimationFrame(() => {
    const halfHeight = realBoxHeight.value / 2; // 一半高度（用于无缝衔接）
    const halfWidth = realBoxWidth.value / 2; // 一半宽度

    switch (direction) {
      case "up":
        // 当向上滚动超过一半高度时，重置位置，实现无缝循环
        if (Math.abs(yPos.value) >= halfHeight) {
          yPos.value = 0;
          loopCount.value++;
          emit("count", loopCount.value);
        }
        yPos.value -= step;

        // 如果设置了单步高度，计算当前滚动到的索引
        if (props.singleHeight && Math.abs(yPos.value) % realSingleStopHeight.value < step) {
          scrollIndex.value =
            Math.floor(Math.abs(yPos.value) / realSingleStopHeight.value) % props.list.length;
          emit("scroll", scrollIndex.value);
        }
        break;

      case "down":
        if (yPos.value >= 0) {
          yPos.value = -halfHeight;
          loopCount.value++;
          emit("count", loopCount.value);
        }
        yPos.value += step;
        break;

      case "left":
        if (Math.abs(xPos.value) >= halfWidth) {
          xPos.value = 0;
          loopCount.value++;
          emit("count", loopCount.value);
        }
        xPos.value -= step;
        break;

      case "right":
        if (xPos.value >= 0) {
          xPos.value = -halfWidth;
          loopCount.value++;
          emit("count", loopCount.value);
        }
        xPos.value += step;
        break;
    }

    // 非滚轮触发时，继续下一帧动画
    if (!isWheel) {
      continueScroll();
    }
  });
};

// 决定是否继续滚动（处理单步停留逻辑）
const continueScroll = () => {
  if (singleWaitTimeout.value) {
    clearTimeout(singleWaitTimeout.value);
  }

  // 是否启用单步滚动（按项滚动）
  const isHeightStep = props.singleHeight && !isHorizontal.value;
  const isWidthStep = props.singleWidth && isHorizontal.value;

  // 如果当前位移正好落在某一项的边界上，则等待一段时间再继续
  if (
    (isHeightStep && Math.abs(yPos.value) % realSingleStopHeight.value < step.value) ||
    (isWidthStep && Math.abs(xPos.value) % realSingleStopWidth.value < step.value)
  ) {
    singleWaitTimeout.value = setTimeout(() => {
      move();
    }, props.singleWaitTime);
    return;
  }

  // 否则立即继续滚动
  move();
};

// 启动滚动（递归调用 animation）
const move = () => {
  cancelAnimation();

  // 如果暂停状态、不满足滚动条件、或已达到最大循环次数，则停止
  if (isHover.value || !isScroll.value || (props.count !== -1 && loopCount.value >= props.count)) {
    emit("stop", loopCount.value);
    return;
  }

  animation(props.direction, step.value, false);
};

// 初始化滚动参数（获取尺寸、设置初始状态）
const initMove = () => {
  // 水平滚动：计算总宽度（包含复制内容）
  if (isHorizontal.value && slotListRef.value) {
    realBoxWidth.value = slotListRef.value.offsetWidth * (smartCopyNum.value + 1) + 1;
  }

  // 设置滚动容器高度（用于无缝重置）
  if (isScroll.value && realBoxRef.value) {
    realBoxHeight.value = realBoxRef.value.offsetHeight;
    if (props.modelValue) {
      move(); // 如果开启自动滚动，则开始
    }
  } else {
    resetPosition(); // 否则重置位置
  }
};

// 重置所有滚动状态（位置、计数器等）
const resetPosition = () => {
  cancelAnimation();
  xPos.value = 0;
  yPos.value = 0;
};

// ==================== 鼠标事件处理 ====================

// 鼠标进入：暂停滚动（如果 hover 开启）
const handleMouseEnter = () => {
  if (props.hover) {
    isHover.value = true;
    cancelAnimation();
  }
};

// 鼠标离开：恢复滚动（如果 modelValue 为 true）
const handleMouseLeave = () => {
  if (props.hover) {
    isHover.value = false;
    if (props.modelValue) {
      move();
    }
  }
};

// 滚轮事件处理（使用 lodash throttle 防抖）
// 向上滚：向下动画；向下滚：向上动画
const handleWheel = throttle((e: WheelEvent) => {
  if (!props.wheel || !props.hover) return;

  cancelAnimation();
  const step = props.singleHeight || 15; // 使用单步高度或默认 15px

  if (e.deltaY < 0) {
    animation("down", step, true);
  } else if (e.deltaY > 0) {
    animation("up", step, true);
  }
}, 30); // 每 30ms 最多触发一次

// ==================== 暴露方法与监听 ====================

// 提供外部调用的重置方法
const reset = () => {
  resetPosition();
  initMove();
};

// 将 reset 方法暴露给父组件使用（通过 getCurrentInstance）
const { proxy } = getCurrentInstance() as any;
if (proxy) {
  proxy.reset = reset;
}

// 监听 list 变化：重新初始化（deep 深度监听）
watch(
  () => props.list,
  () => {
    if (props.isWatch) {
      nextTick(initMove);
    }
  },
  { deep: true }
);

// 监听 modelValue 变化：控制滚动启停
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      move();
    } else {
      cancelAnimation();
    }
  }
);

// 监听 direction 变化：重置并重新初始化
watch(
  () => props.direction,
  () => {
    resetPosition();
    initMove();
  }
);

// 组件挂载后初始化
onMounted(() => {
  nextTick(initMove);
});
</script>

<style scoped>
.seamless-scroll {
  overflow: hidden;
  position: relative;
}
</style>
