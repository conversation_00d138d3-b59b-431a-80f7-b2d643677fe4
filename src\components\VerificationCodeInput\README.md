# VerificationCodeInput 组件

一个简洁的验证码输入组件，专为 6 位数字验证码设计。

## 功能特性

- ✅ 固定 6 位数字输入
- ✅ 自动限制输入为纯数字
- ✅ 简洁的样式设计
- ✅ 完整的事件支持
- ✅ 轻量级实现

## 基本用法

```vue
<template>
  <VerificationCodeInput v-model="code" placeholder="Enter the code" />
</template>

<script setup>
import { ref } from "vue";
import VerificationCodeInput from "@/components/VerificationCodeInput/index.vue";

const code = ref("");
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string` | `""` | 双向绑定的验证码值 |
| `placeholder` | `string` | `"Enter the code"` | 占位符文本 |
| `disabled` | `boolean` | `false` | 是否禁用 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `value: string` | 值变化时触发 |
| `input` | `value: string` | 输入时触发 |
| `blur` | `value: string` | 失焦时触发 |
| `focus` | `value: string` | 聚焦时触发 |
| `complete` | `value: string` | 输入完成时触发（6位数字） |

## 特点

- **轻量级**：只有必要的功能，代码简洁
- **专注性**：专为 6 位数字验证码设计
- **易用性**：简单的 API，易于集成
- **样式统一**：与现有设计系统保持一致

## 使用示例

### 在验证码弹窗中使用

```vue
<template>
  <div class="verification-dialog">
    <div class="send-code-tip">验证码已发送到 +63 912 345 6789</div>

    <VerificationCodeInput v-model="code" placeholder="Enter the code" @complete="onCodeComplete" />

    <button @click="resendCode">重新发送</button>
  </div>
</template>

<script setup>
import { ref } from "vue";
import VerificationCodeInput from "@/components/VerificationCodeInput/index.vue";

const code = ref("");

const onCodeComplete = (value) => {
  console.log("验证码输入完成:", value);
  // 自动提交验证
  submitCode(value);
};

const submitCode = (code) => {
  // 提交验证码逻辑
  console.log("提交验证码:", code);
};

const resendCode = () => {
  // 重发验证码逻辑
  console.log("重新发送验证码");
};
</script>
```

### 在登录页面中使用

```vue
<template>
  <div class="login-form">
    <VerificationCodeInput
      v-model="verCode"
      placeholder="Enter the code"
      @complete="handleCodeComplete"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import VerificationCodeInput from "@/components/VerificationCodeInput/index.vue";

const verCode = ref("");

const handleCodeComplete = (value) => {
  console.log("验证码输入完成:", value);
  // 自动提交登录
  submitLogin();
};

const submitLogin = () => {
  // 登录逻辑
  console.log("提交登录:", verCode.value);
};
</script>
```

## 样式说明

组件使用了与现有设计系统一致的样式：

- **字体**：D-DIN 字体，18px，700 字重
- **颜色**：文字 #222，占位符 #c0c0c0
- **背景**：透明背景，适配各种容器
- **边框**：无边框设计，简洁美观

## 注意事项

- 组件固定为 6 位数字输入，无法配置长度
- 只接受数字输入，自动过滤其他字符
- 需要在父容器中设置合适的样式（如边框、背景等）
- 建议配合 `complete` 事件实现自动提交功能
