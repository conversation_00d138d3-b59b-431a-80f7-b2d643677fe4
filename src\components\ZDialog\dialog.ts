import { h, render, AppContext } from "vue";
import { showDialog, showConfirmDialog } from "vant";
import BaseDialog from "./index.vue";

type DialogOptions = {
  title?: string;
  message?: string;
  describe?: string;
  showConfirmButton?: boolean;
  showCancelButton?: boolean;
  cancelText?: string;
  confirmText?: string;
  showClose?: boolean;
  onConfirm?: (() => void) | (() => Promise<void>);
  onCancel?: (() => void) | (() => Promise<void>);
  animationDuration?: number; // 动画时长，单位 ms
  zIndex?: number; // 弹窗层级，默认为 2000
};

export function useDialog(appContext?: AppContext) {
  // 组件式调用
  const DialogComponent = (options: DialogOptions) => {
    const container = document.createElement("div");

    // 设置容器的 z-index
    if (options.zIndex) {
      container.style.zIndex = String(options.zIndex);
      container.style.position = "relative";
    }

    const onConfirm = async () => {
      try {
        if (options.onConfirm) {
          await options.onConfirm();
        }
        unmount();
      } catch (error) {
        console.error("Dialog confirm error:", error);
      }
    };

    const onCancel = async () => {
      try {
        if (options.onCancel) {
          await options.onCancel();
        }
        unmount();
      } catch (error) {
        console.error("Dialog cancel error:", error);
      }
    };

    const unmount = () => {
      setTimeout(() => {
        render(null, container);
        container.remove();
      }, options.animationDuration || 300); // 默认动画时长 300ms
    };

    const vnode = h(BaseDialog, {
      ...options,
      modelValue: true,
      onConfirm,
      onCancel,
    });

    if (appContext) {
      vnode.appContext = appContext;
    }
    render(vnode, container);
    document.body.appendChild(container);
    return {
      unmount,
    };
  };

  // 函数式调用（兼容Vant原生方式）
  DialogComponent.alert = (options: DialogOptions) => {
    const vantOptions: any = { ...options };

    // 如果设置了 zIndex，添加到 Vant 的配置中
    if (options.zIndex) {
      vantOptions.overlayStyle = {
        zIndex: options.zIndex,
        ...vantOptions.overlayStyle,
      };
    }

    return showDialog(vantOptions);
  };

  DialogComponent.confirm = (options: DialogOptions) => {
    const vantOptions: any = { ...options };

    // 如果设置了 zIndex，添加到 Vant 的配置中
    if (options.zIndex) {
      vantOptions.overlayStyle = {
        zIndex: options.zIndex,
        ...vantOptions.overlayStyle,
      };
    }

    return showConfirmDialog(vantOptions);
  };

  return DialogComponent;
}
