<template>
  <div class="tab-content betting-tab">
    <!-- 环形图 -->
    <div class="doughnut-chart-container">
      <div ref="doughnutChart" class="chart-canvas"></div>
    </div>

    <!-- 过滤器按钮 -->
    <div class="filter-buttons">
      <button
        v-for="filter in filters"
        :key="filter.key"
        class="filter-btn"
        :class="{ active: activeFilter === filter.key }"
        @click="$emit('update:activeFilter', filter.key)"
      >
        {{ filter.label }}
      </button>
    </div>

    <!-- 游戏表格 -->
    <div class="game-table">
      <van-row class="game-table-head">
        <van-col span="9">Game name</van-col>
        <van-col span="9">{{ currentTableLabel }}</van-col>
        <van-col span="6">Go Bet</van-col>
      </van-row>
      <div class="game-table-body" v-if="displayTableData && displayTableData?.length > 0">
        <van-row
          v-for="(game, index) in displayTableData"
          :key="index"
          :class="`game-row ${game.color || ''}`"
        >
          <van-col span="9" class="game-name">
            <div class="color-dot" :style="{ backgroundColor: game.colorCode }"></div>
            {{ game.name }}
          </van-col>
          <van-col span="9" class="bet-amount">
            <IconCoin :size="14" />{{ game.currentValue }}
          </van-col>
          <van-col span="6" class="action-cell">
            <button
              v-if="!isEmpty(game.game_id)"
              class="bet-button"
              @click="() => readyToJump(game)"
            >
              <img src="@/assets/images/tournament/go-btn.png" alt="Go" />
            </button>
          </van-col>
        </van-row>
      </div>
      <div v-else class="game-table-body">
        <div class="game-row nodata">No Data</div>
      </div>
    </div>
    <ZActionSheet
      v-model="showDialog"
      title="Tips"
      :showCancelButton="false"
      confirmText="Confirm"
      :onConfirm="hanldeGoToGame"
    >
      Confirm to go to XXX game?
    </ZActionSheet>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watchEffect, nextTick, computed } from "vue";
import * as echarts from "echarts";
import { jumpGame } from "@/utils/JumpGame";
import { getGameInfo } from "@/api/games";
import { showToast } from "vant";
import { isEmpty } from "@/utils/core/tools";

// Define the type locally to avoid import issues
type seriesItem = {
  name: string;
  value: number;
};

const props = withDefaults(
  defineProps<{
    seriesData: Array<seriesItem>;
    activeFilter: string;
    tableData?: Array<any>;
  }>(),
  {
    seriesData: () => [],
    activeFilter: "0",
    tableData: () => [],
  }
);
console.log("tableData", props.tableData);

const emits = defineEmits<{
  "update:activeFilter": [filter: string];
}>();

// 过滤器配置
const filters = [
  { key: "0", label: "Bet" },
  { key: "1", label: "Net Win" },
  { key: "2", label: "Win Rate" },
];

const tableLabels = ["Valid Bet", "Win Rate", "Net Win"];

// 根据activeFilter获取当前表格标签
const currentTableLabel = computed(() => {
  const filterIndex = parseInt(props.activeFilter);
  // 映射关系：0->Bet Amount, 1->Net Win, 2->Win Rate
  const labelMap = {
    0: tableLabels[0], // "Bet Amount"
    1: tableLabels[2], // "Net Win"
    2: tableLabels[1], // "Win Rate"
  };
  return labelMap[filterIndex] || "Valid Bet";
});

// 图表颜色配置
const chartColors = ["#3E70FF", "#9A2FFF", "#FF9D00", "#FF4A3D", "#F28AFC", "#00D2DA"];

// 计算表格显示数据
type DisplayTableItem = {
  name: string;
  bet: string | number;
  colorCode: string;
  color: string;
  game_id?: string | number;
};

const displayTableData = computed<DisplayTableItem[]>(() => {
  let result: DisplayTableItem[] = [];
  if (props.tableData && props.tableData.length > 0) {
    result = props.tableData.map((item, index) => {
      return {
        ...item,
        name: item.game_name || item.name,
        currentValue: item.value.replace("₱", "") || "--",
        colorCode: chartColors[index % chartColors.length],
        color: "",
      };
    });
  } else {
    // 如果没有tableData，使用seriesData生成表格数据
    result = props.seriesData.map((item, index) => ({
      ...item,
      name: item.name,
      bet: item.value.toString(),
      colorCode: chartColors[index % chartColors.length],
      color: "",
    }));
  }
  console.log("displayTableData", result);

  return result;
});

// 图表相关
const doughnutChart = ref<HTMLDivElement | null>(null);
const doughnutChartInstance = ref<echarts.ECharts | null>(null);

// 初始化图表
const initChart = () => {
  if (doughnutChart.value && !doughnutChartInstance.value) {
    doughnutChartInstance.value = echarts.init(doughnutChart.value);
    const option = setOptions(props.seriesData);
    console.log("Chart option:", option);
    doughnutChartInstance.value.setOption(option);
    console.log("Chart initialized successfully");
  }
};

function setOptions(seriesData: Array<seriesItem> = []): echarts.EChartsOption {
  return {
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"], // 调整内外半径，使饼图更厚
        center: ["50%", "50%"],
        data: seriesData,
        label: {
          show: false, // 隐藏内部标签，保持简洁
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.2)",
          },
          scale: true,
          scaleSize: 5,
        },
        itemStyle: {
          borderWidth: 2,
          borderColor: "#fff", // 添加白色边框分隔
        },
      },
    ],
    tooltip: {
      trigger: "item",
      // formatter: "{a} <br/>{b}: {c} ({d}%)",
      formatter: "{b}: {c} ({d}%)",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      textStyle: {
        color: "#fff",
      },
    },
    // 标题
    title: { show: false },
    // 图例
    legend: { show: false },
    // 使用的转盘色
    color: chartColors,
  };
}

watchEffect(() => {
  console.log("Chart data updated:", props.seriesData);
  nextTick(() => {
    if (doughnutChartInstance.value && props.seriesData.length > 0) {
      doughnutChartInstance.value.setOption(setOptions(props.seriesData));
    }
  });
});

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (doughnutChartInstance.value) {
    doughnutChartInstance.value.dispose();
    doughnutChartInstance.value = null;
  }
});

const showDialog = ref(false);
const currentGame = ref({});
// 显示确认弹窗
const readyToJump = (game: any) => {
  showDialog.value = true;
  currentGame.value = game;
};
// 存储游戏信息
// const gameInfoMap = new Map<string, Promise<any>>();
// 跳转游戏
const hanldeGoToGame = async () => {
  const game = currentGame.value;
  if (!game.game_id) showToast("No game data found");
  try {
    console.log("调用 API 获取游戏详情", game);
    // 调用 API 获取游戏详情
    const res = await getGameInfo({ id: game.game_id });
    console.log("调用 API 获取游戏详情", res);
    // 从响应中提取游戏数据
    const gameData = res?.data || res;
    if (gameData && gameData.length > 0) {
      jumpGame(gameData[0]);
    } else {
      showToast("No game data found");
    }
  } catch (error) {
    console.error("Failed to get game info:", error);
  }
};
</script>

<style scoped lang="scss">
.betting-tab {
  display: flex;
  flex-direction: column;
}

.doughnut-chart-container {
  height: 190px;
  margin: 10px auto;
  width: 200px;
  background: transparent;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 过滤器按钮 */
.filter-buttons {
  width: 94%;
  display: flex;
  justify-content: center;
  margin: 10px 12px;
  background-color: #f1efff;
  border-radius: 8px;
  font-family: "Inter";
  font-weight: 700;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  height: 36px;
}

.filter-btn {
  flex: 1;
  border: none;
  background: transparent;
  border-radius: 8px;
  text-align: center;
  color: #222222;
  transition: all 0.3s ease;

  &.active {
    background: linear-gradient(270deg, #194afc 0%, #7700ff 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
  }

  &:hover:not(.active) {
    background: rgba(25, 74, 252, 0.1);
  }
}

/* 游戏表格 */
.game-table {
  margin: 0 12px;
  background: #f6fcff;
  max-height: calc(70vh - 290px);
  border-radius: 8px;

  .game-table-head {
    height: 32px;
    width: 100%;
    background: #e9f8ff;
    border-radius: 8px;
    overflow: hidden;
    text-align: center;
    line-height: 32px;
    font-size: 12px;
    color: #222222;
    font-weight: 700;
  }

  .game-table-body {
    max-height: calc(70vh - 322px);
    min-height: 100px;
    overflow-y: auto;
  }

  .game-row {
    vertical-align: middle;
    height: 32px;
    line-height: 32px;
    color: #222222;

    &.nodata {
      color: #666;
      text-align: center;
      padding: 30px 0;
    }

    .bet-amount img {
      margin-right: 4px;
    }

    > div {
      font-weight: 400;
      font-size: 12px;
      display: flex;
      gap: 0;
      justify-content: start;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 10px;
    }

    .action-cell {
      justify-content: center;
    }
  }

  .color-dot {
    width: 8px;
    height: 8px;
    margin: 0 4px;
    flex-shrink: 0;
  }
}

.bet-button {
  border: none;
  background: transparent;
  transition: transform 0.2s ease;

  img {
    width: 40px;
    height: auto;
  }
}
</style>
