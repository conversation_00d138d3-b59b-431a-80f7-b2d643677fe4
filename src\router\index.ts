import { createRouter, createWebHistory } from "vue-router";
import { GameFiltersStorage } from "@/utils/managers/GameFiltersStorage";
import { getToken } from "@/utils/auth";
import { useFirstVisitStore } from "@/stores/firstVisit";
import { routes } from "./routes";
import { testRoutes } from "./testRoutes";

// 应用初始化状态
let appInitialized = false;

// 检查是否是页面刷新
const isPageRefresh = () => {
  // 使用 sessionStorage 标记来检测是否需要重新初始化
  // 如果没有 appInitialized 标记，说明是新会话或页面刷新
  return !sessionStorage.getItem("appInitialized");
};

// 设置应用初始化状态
export const setAppInitialized = (initialized: boolean) => {
  console.log("设置应用初始化状态:", initialized);
  appInitialized = initialized;
  if (initialized) {
    // 在 sessionStorage 中标记应用已初始化
    sessionStorage.setItem("appInitialized", "true");
    console.log("应用初始化完成，已设置 sessionStorage 标记");
  } else {
    sessionStorage.removeItem("appInitialized");
    console.log("重置应用初始化状态，已清除 sessionStorage 标记");
  }
};

// 检查是否需要初始化
export const needsInitialization = () => {
  // 如果是页面刷新，总是需要重新初始化
  if (isPageRefresh()) {
    appInitialized = false;
    // 注意：不要在这里删除 appInitialized，因为可能会影响 targetRoute 的保存
    return true;
  }

  return !appInitialized;
};

const router = createRouter({
  history: createWebHistory(),
  routes: [...routes, ...testRoutes],
});

router.beforeEach((to, from, next) => {
  const firstVisitStore = useFirstVisitStore();
  /*  if (to.path.includes("/test")) {
    next();
    return;
  } */
  // 如果访问的是启动页，直接通过
  if (to.path === "/splash") {
    next();
    return;
  }
  console.log("to.fullPath", to.fullPath);
  // 如果应用还没初始化，重定向到启动页，并保存目标路由
  if (needsInitialization()) {
    sessionStorage.setItem("targetRoute", to.fullPath);
    next("/splash");
    return;
  }

  function firstVisitJudge() {
    // 检查是否需要首次访问控制
    if (to.meta?.requiresFirstCheck && firstVisitStore.isFirstVisit(to.path)) {
      firstVisitStore.markAsVisited(to.path);
      // 确保 firstVisitRoute 存在
      if (to.meta.firstVisitRoute) {
        const returnTo = String(to.meta.returnTo || to.path);
        const redirectUrl = `${to.meta.firstVisitRoute}?returnTo=${encodeURIComponent(returnTo)}`;
        next(redirectUrl);
      } else {
        next();
      }
    } else {
      next();
    }
  }

  // 检查并清理游戏筛选状态
  GameFiltersStorage.checkAndCleanup(to.path);

  // 只要没有明确 requiresAuth: false 的页面，都需要登录
  if (to.meta.requiresAuth === false) {
    firstVisitJudge();
  } else if (!getToken()) {
    console.log("路由守卫：用户未认证，跳转到登录页");
    next("/login");
  } else {
    console.log("路由守卫：用户已认证，继续路由导航");
    firstVisitJudge();
  }
});

export default router;
