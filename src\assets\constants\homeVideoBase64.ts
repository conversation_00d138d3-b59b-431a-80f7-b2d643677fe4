const imgBase64 = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAV8AAADFCAYAAADzP/EQAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAVoSURBVHgB7dxNblNXGMfh44sQUmCQDCAJI3sF0BVgltAdtCsAVgBZAekKmq6gSyDdQbqCpKMgZ5BMiBDCSc+xkgioybf9l+jzSI5v7GvB6KdXb3xvr1zT3t7esD49PTo6etaee73e4vHx8WIB+EHVzh3Uzu3Uw/b4u+u6zYcPH26Wa+hd5eTt7e3FBw8evKj/+EuhBZjYqVHeHI/Ha6urqzuX/dCl4tuie//+/dctugWAqWqENy4b4QvjOxqNXtSnNyZdgEtpk/Dao0ePNs476dz41vC+Ne0CXF3dB6/XffCr770/Nb4na4Y/a3iHBYDr2jo8PHw+GAwOvn2jm3b2wsLCO+EFuLGnbZCd9sZ/4ttWDe0DBYAba4Ps3t7e229f/2rtUMP7Sz3x9wLAraptfbWysrJ++vtZfHd3d/t1QfyuHvYLALftoO5/B6f737O1w507d14X4QWYlXaR2uvTXyaT78nUu10AmKk6/S616Xcy+Z5MvQDM2MLCwuTaiUl8fa0MYG7aVcOlV1cOw5M/tAEwB7W5z+ujGxYA5mY8Hj9ta4cnBYB5Grb49gsAc9Pr9Z509Ue/ADBPi5379ALM3WJXAJg78QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUIEF+AAPEFCBBfgADxBQgQX4AA8QUI6Hq93kEBYJ4OuuPjY/EFmKM69O60tcNWAWBujo6O/mmT718FgLmpk+9Wi6/JF2COuq7b7LWD0Wi0XyO8WACYtZ3l5eXB5Ktm4/H4twLAzNWVw+bkuf3Y399f/PTp034BYKbqH9sGq6urk287lKWlpYO6djD9AsxQnXo3Wnjb8dkVbvfu3XvjgguAmdmpK96101/O4tum38+fP68VAG5dXTesnU69zVf3dnj8+PG69QPA7WpdreHd+PK13rQTR6PRu3rysABwU1vLy8s/ffvi1Lua3b179+fismOAG2lfKzs8PHw+9b3zPvj+/fv1+uEXBYAraauGlZWVl997/9z7+bYP1iXxr/VwpwBwofatsRreV+eFt7nwZuptSVwD3MbmPwoA39Wm3Q8fPgxqeNcvOrdXrmB3d7ffdd2bevisPvoF4H+uTbrtFg0fP35cHwwGl75W4krx/VIN8bCGeFgPn9RHv/4H+m7OA/zITlYKLbBb7Xa87a6QdTuwWa7hX4+NIOhQWsPhAAAAAElFTkSuQmCC`;

export default imgBase64;
