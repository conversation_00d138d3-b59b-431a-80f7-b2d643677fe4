# TurntableButton 组件

## 概述

`TurntableButton` 是一个复杂的浮动按钮组件，专为转盘/抽奖游戏功能设计。它具有多层动画元素、实时奖金金额更新，以及基于用户认证状态的条件渲染。

## 文件位置

```
src/components/Single-purpose/TurntableButton.vue
```

## 功能特性

- **多层动画**: 多个同步动画（缩放、旋转、浮动）
- **实时更新**: 动态奖金金额，带滚动数字显示
- **认证感知**: 登录用户与游客用户的不同行为
- **状态管理**: 与自动弹窗和全局状态管理集成
- **触摸优化**: 全面的移动端触摸优化
- **资源管理**: 防止图像拖拽和右键菜单

## 使用方法

### 基础实现

```vue
<template>
  <TurntableButton />
</template>

<script setup>
import TurntableButton from "@/components/Single-purpose/TurntableButton.vue";
</script>
```

### 浮动定位示例

```vue
<template>
  <div class="game-container">
    <div class="floating-controls">
      <TurntableButton />
    </div>
  </div>
</template>

<script setup>
import TurntableButton from "@/components/Single-purpose/TurntableButton.vue";
</script>

<style scoped>
.floating-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}
</style>
```

## 属性 (Props)

该组件不接受任何属性。所有功能通过内部状态和状态管理集成进行管理。

## 事件 (Events)

该组件不发出自定义事件。所有交互通过状态管理更新和导航在内部处理。

## 依赖项

### 组件依赖

- `XNumberRoller` - 用于动画数字显示

### 状态管理依赖

- `useAutoPopMgrStore` - 管理转盘弹窗状态
- `useGlobalStore` - 提供认证状态
- `POP_FORM` - 弹窗来源跟踪枚举

### API 依赖

- `getActivitySpinConfig` - 获取转盘配置数据

### 工具依赖

- `getRandomInt` - 为奖金金额生成随机增量

### 资源依赖

多个转盘主题图像：

- `win-title.png` - "WIN YOUR DAY" 标题
- `g.png` - 背景光效
- `turntable-bg.png` - 主转盘背景
- `lpnr.png` - 旋转风车元素
- `spin-btn.png` - 转盘按钮
- `hand.png` - 指针手
- `count-bg.png` - 计数显示背景
- `amount-bg.png` - 金额显示背景

## 行为特性

### 基于认证的行为

#### 登录用户

- 显示转盘次数和总奖金金额
- 点击打开转盘弹窗
- 每 2 秒实时更新奖金金额

#### 游客用户

- 显示金额占位符 "--"
- 点击重定向到登录页面
- 无实时更新

### 动画系统

#### 缩放动画

- **元素**: 背景光效和手指指针
- **效果**: 从 1x 到 1.3x 的脉冲缩放
- **持续时间**: 1.2 秒无限循环 ease-in-out

#### 浮动动画

- **元素**: "WIN YOUR DAY" 标题
- **效果**: 垂直浮动运动（0 到 -10px）
- **持续时间**: 1.2 秒无限循环 ease-in-out

#### 旋转动画

- **元素**: 风车/转盘元素
- **效果**: 连续 360° 旋转
- **持续时间**: 1.6 秒线性无限循环

### 实时更新

- 奖金金额每 2 秒递增
- 使用随机整数生成实现真实更新
- 组件挂载时自动开始
- 组件卸载时清理定时器

## 样式设计

### 容器结构

- **尺寸**: 100px 最大宽度/高度，100px 最小宽度
- **定位**: 相对定位，带右偏移
- **形状**: 圆形（100px 边框半径）
- **优化**: 全面的拖拽防护

### 层级结构 (Z-index)

1. **背景光效** (z-index: auto)
2. **主背景** (z-index: auto)
3. **风车** (z-index: auto)
4. **转盘按钮** (z-index: 2)
5. **手指指针** (z-index: 3)
6. **计数显示** (z-index: 4)
7. **标题** (z-index: 7)

### 响应式设计

- 在不同屏幕尺寸上适当缩放
- 针对移动设备触摸优化
- 防止不必要的交互（拖拽、选择、右键菜单）

## 技术实现

### 生命周期管理

```javascript
onMounted(() => {
  getTimesInfo(); // 开始实时更新
});

onBeforeUnmount(() => {
  clearInterval(timer); // 清理定时器
  timer = null;
});
```

### 状态管理集成

```javascript
const { showSpinWheelTip, sourceSpinWheel, spinInfo } = storeToRefs(autoPopMgrStore);
const isLogin = computed(() => !!globalStore.token);
```

### 点击处理逻辑

```javascript
const handleClick = (event: Event) => {
  if (isLogin.value) {
    showSpinWheelTip.value = true;
    sourceSpinWheel.value = POP_FORM.CLICK;
  } else {
    router.replace("/login");
  }
};
```

## 性能考虑

### 动画优化

- 使用 CSS 变换进行 GPU 加速
- Will-change 属性用于优化渲染
- 高效的动画时间函数

### 内存管理

- 组件卸载时正确清理定时器
- 响应式引用用于优化 Vue 响应性
- 最少的 DOM 操作

### 资源优化

- 全面的拖拽防护
- 优化的图像加载
- 指针事件管理

## 集成要求

### 状态管理配置

```javascript
// 自动弹窗状态管理设置
export const useAutoPopMgrStore = defineStore("autoPopMgr", {
  state: () => ({
    showSpinWheelTip: false,
    sourceSpinWheel: null,
    spinInfo: {
      left_times: 0,
      total_prize: 0,
    },
  }),
});

// 全局状态管理设置
export const useGlobalStore = defineStore("global", {
  state: () => ({
    token: null,
  }),
});
```

### API 集成

```javascript
// 活动 API 设置
export const getActivitySpinConfig = async () => {
  // 获取转盘配置的实现
};
```

## 浏览器兼容性

- **现代浏览器**: 完全支持动画和交互
- **移动端 Safari**: 优化的触摸交互
- **Android Chrome**: 全面的移动端优化
- **旧版浏览器**: 动画的优雅降级

## 故障排除

### 常见问题

1. **动画不流畅**

   - 检查 GPU 加速支持
   - 验证 CSS 变换属性
   - 确保没有冲突的动画

2. **实时更新不工作**

   - 验证定时器初始化
   - 检查认证状态
   - 确保正确清理

3. **图像无法加载**

   - 验证所有资源路径
   - 检查图像文件存在性
   - 验证资源导入路径

4. **移动端触摸问题**

   - 确保应用了触摸优化 CSS
   - 检查冲突的触摸处理器
   - 验证指针事件配置

5. **状态管理集成问题**
   - 验证状态管理初始化
   - 检查响应式引用
   - 确保正确的状态管理方法调用

## 使用建议

### 最佳实践

- 确保在游戏页面或活动页面中使用
- 测试不同设备上的动画性能
- 验证登录状态的正确处理

### 注意事项

- 组件依赖多个图像资源，确保所有路径正确
- 需要配合转盘弹窗组件使用
- 建议在固定位置使用以获得最佳用户体验
