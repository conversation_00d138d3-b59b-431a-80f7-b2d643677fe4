<template>
  <XPage :navBarStyle="{ backgroundColor: '#FFAB13', color: '#333' }">
    <!--  <template #left-icon>
      <ZIcon type="icon-fanhui" color="#fff" />
    </template> -->
    <div class="promo-detail">
      <img src="@/assets/images/promos/promo_14.jpg" alt="" />
      <ZFootPng />
    </div>
    <!-- <div class="promo-details">
       <h2 class="title">GENERAL MECHANICS</h2>
    <ul class="mechanics-list">
      <li>This promotion is open to both new and existing NUSTAR members.</li>
      <li>
        NUSTAR Members who place bets on five games designated by yellow bat can enjoy a extra 0.35%
        rebate increase on top of the original rebate! Here are the five games participating in the
        event.
        <van-row gutter="12" class="games-list">
          <van-col span="8" class="game-item" v-for="item in gameList" :key="item.id">
            <img :src="item.url || Placeholder" alt="Dragon Gems" />
            <div class="game-label">{{ item.name }}</div>
          </van-col>
        </van-row>
      </li>
      <li>
        NUSTAR members will receive bonuses based on their valid bets placed daily from 00:00:01 AM
        to 11:59:59 PM.
      </li>
      <li>Bonuses will be credited to accounts by 3:00 PM the following day.</li>
      <li>A minimum bonus of ₱1 will be awarded.</li>
      <li>No wagering requirements are necessary for withdrawal.</li>
    </ul>
    </div>-->
  </XPage>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
// .icon-fanhui {
//   width: 30px;
//   height: 30px;
//   border-radius: 50%;
//   background-color: rgba(7, 7, 7, 0.3);
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   color: #fff;
// }
.promo-detail {
  background: #24cbb9;
  min-height: 100vh;
  font-family: "Inter", "D-DIN";
}

// .promo-details {
//   // background: #1ac2ae;
//   min-height: 100%;
//   // padding: 10px 0 0 0;
//   color: #222;
//   font-family: Arial, sans-serif;
//   position: relative;
//   &:deep(.van-icon-arrow-left) {
//     width: 30px;
//     height: 30px;
//     background: rgba(0, 0, 0, 0.3);
//     border-radius: 50%;
//     line-height: 30px;
//     text-align: center;
//     color: #fff;
//   }
// }
// .back-btn {
//   position: absolute;
//   top: 10px;
//   left: 12px;
//   z-index: 10;
//   cursor: pointer;
// }
// .title {
//   text-align: left;
//   font-size: 22px;
//   font-weight: bold;
//   margin: 30px 0 12px 30px;
//   letter-spacing: 1px;
// }
// .mechanics-list {
//   margin: 0 24px;
//   padding: 0 0 26px 18px;
//   font-size: 14px;
//   line-height: 1.8;
//   list-style: auto;
//   li {
//     margin-bottom: 14px;
//   }
// }
// .games-list {
//   margin: 16px 0 8px 0;
//   width: 100%;
//   justify-content: center;
// }
// .game-item {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   width: 100px;
//   margin-bottom: 14px;
// }
// .game-item img {
//   width: 80px;
//   height: 80px;
//   border-radius: 12px;
//   object-fit: cover;
//   margin-bottom: 4px;
//   background: #fff;
//   border: 2px solid #fff;
//   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
// }
// .game-label {
//   background: #d32f2f;
//   color: #fff;
//   font-size: 10px;
//   font-weight: 500;
//   border-radius: 4px;
//   padding: 2px 8px;
//   margin-top: 2px;
//   text-align: center;
// }
// .footer-bar {
//   position: fixed;
//   bottom: -5px;
//   left: 0;
//   width: 100%;
//   background: rgba(0, 0, 0, 0.05);
//   padding-top: 8px;
//   img {
//     width: 100%;
//   }
// }
</style>
