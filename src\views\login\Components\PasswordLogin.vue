<template>
  <!-- 密码登录 -->
  <div class="main">
    <PhoneInput
      ref="phoneInputRef"
      v-model="loginStore.userPhone"
      @validityChange="onPhoneValidityChange"
    />
    <div class="form-item">
      <div class="input-wrap">
        <input
          class="input"
          v-model="password"
          :type="passwordType"
          maxlength="20"
          minlength="8"
          placeholder="Enter Your Password"
          @input="pwdChange"
        />
        <div class="action-group">
          <ZIcon
            v-show="password"
            type="icon-close"
            size="16"
            color="#999"
            @click="clearPassword"
          />
          <ZIcon
            @click="togglePasswordVisibility"
            size="16"
            :class="`icon-${passwordType === 'password' ? 'yanjing' : 'yanjing1'}`"
            color="#999"
          />
        </div>
      </div>
      <div class="tip-wrap">
        <div class="password-error">
          {{
            pwdError
              ? "The login password is 8~20 characters and must contain letters and numbers."
              : " "
          }}
        </div>
        <span @click="showForgot" class="forget">forgot ?</span>
      </div>
    </div>

    <ZButton @click="handleLogin" class="btn"> Log in </ZButton>

    <div @click="checkLoginTypeClick" class="form-redline">
      Login With Verification Code
      <ZIcon type="icon-qianjin" color=""></ZIcon>
    </div>

    <!-- 重置密码弹窗 -->
    <ZVerifyDialog
      v-model:showDialog="showResetPwdDialog"
      :passPhone="loginStore.userPhone"
      :verifyType="PN_VERIFY_TYPE.ForgetPassword"
      :succCallBack="resetPwdSuccessCallback"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, onUnmounted } from "vue";
import ZVerifyDialog from "@/components/ZVerifyDialog/index.vue";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import { phoneExists } from "@/api/user";
import { showToast } from "vant";
import PhoneInput from "./PhoneInput.vue";
import { loginManager } from "@/utils/managers/LoginManager";
import { useLoginStore } from "@/stores/login";
import { isPhilippinePhoneNumber } from "@/utils/core/tools";

// 使用 login store
const loginStore = useLoginStore();

// 响应式数据
const showResetPwdDialog = ref(false);
const password = ref("");
const passwordType = ref<"password" | "text">("password");
const pwdError = ref(false);
const phoneIsValid = ref(isPhilippinePhoneNumber(loginStore.userPhone));
const phoneInputRef = ref();

// 密码验证正则：包含数字与字母，长度在8-20之间
const PASSWORD_REGEX = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,20}$/;

// 事件处理方法
const checkLoginTypeClick = () => {
  loginStore.switchToCodeLogin(loginStore.userPhone);
};

const onPhoneValidityChange = (isValid: boolean) => {
  phoneIsValid.value = isValid;
};

const togglePasswordVisibility = () => {
  passwordType.value = passwordType.value === "password" ? "text" : "password";
};

const clearPassword = () => {
  password.value = "";
  pwdError.value = false;
};
// 移除实时密码校验，改为在登录时校验
const pwdChange = () => {
  // 只清除错误状态，不进行实时校验
  pwdError.value = false;
};
const showForgot = async () => {
  if (!phoneIsValid.value) {
    return;
  }

  try {
    const res = await phoneExists({ phone: loginStore.userPhone });
    // 根据实际 API 响应结构调整
    const responseData = res.data || res;
    if (responseData.exists === 0) {
      showToast("Please register an account first");
      return;
    } else if (responseData.exists === 2) {
      loginManager.showLock(responseData);
      return;
    }
    showResetPwdDialog.value = true;
  } catch (error) {
    console.error("Check phone exists error:", error);
    showToast("Network error, please try again");
  }
};

const resetPwdSuccessCallback = (loginPWD: string) => {
  showToast("Login password reset successfully");
  showResetPwdDialog.value = false;
  password.value = loginPWD;
};

const handleLogin = () => {
  // 在点击登录时进行校验

  // 清除之前的错误状态
  pwdError.value = false;

  // 1. 使用手机号组件的内部验证
  phoneInputRef.value?.validatePhone();
  if (!phoneInputRef.value?.isValid()) {
    return;
  }

  // 2. 校验密码是否为空
  if (!password.value.trim()) {
    pwdError.value = true;
    return;
  }

  // 3. 校验密码格式
  if (!PASSWORD_REGEX.test(password.value)) {
    pwdError.value = true;
    return;
  }

  try {
    loginStore.handlePasswordLogin({
      password: password.value,
      phone: loginStore.userPhone,
    });
  } catch (error) {
    console.error("Login error:", error);
    // 错误处理由 store 处理
  }
};

// 组件销毁时清理
onUnmounted(() => {
  password.value = "";
  pwdError.value = false;
  showResetPwdDialog.value = false;
});
</script>
<style lang="scss" scoped>
.form-redline {
  text-align: center;
  margin-top: 16px;
  color: #666;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  cursor: pointer;
}

.btn {
  margin-top: 16px;
  height: 48px;
}

/* 表单项 */
.form-item {
  margin-top: 20px;
  position: relative;

  .tip-wrap {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 10px;

    .password-error {
      flex: 1;
      color: var(--Nustar, #ac1140);
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .forget {
      color: #666;
      text-align: right;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      cursor: pointer;
    }
  }

  /* 输入框样式 */
  .input-wrap {
    width: 100%;
    height: 30px;
    border-bottom: 1px solid #ddd;
    font-size: 16px;
    font-weight: 600;
    outline: none;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-group {
      display: flex;
      gap: 16px;
    }

    .input {
      width: 100%;
      margin-left: 6px;
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      font-weight: 600;
      color: #222;
      font-family: D-DIN;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      &::placeholder {
        color: #c0c0c0;
        /* 输入框内默认文字 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }
}
</style>
