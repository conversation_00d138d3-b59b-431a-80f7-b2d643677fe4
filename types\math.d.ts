/**
 * 数学工具类型声明
 */

declare class MathUtils {
  /**
   * 加法运算
   * @param a - 加数
   * @param b - 被加数
   * @returns 计算结果
   */
  static add(a: number | string, b: number | string): number;

  /**
   * 减法运算
   * @param a - 减数
   * @param b - 被减数
   * @returns 计算结果
   */
  static subtract(a: number | string, b: number | string): number;

  /**
   * 乘法运算
   * @param a - 乘数
   * @param b - 被乘数
   * @returns 计算结果
   */
  static multiply(a: number | string, b: number | string): number;

  /**
   * 除法运算
   * @param a - 除数
   * @param b - 被除数
   * @returns 计算结果
   */
  static divide(a: number | string, b: number | string): number;

  /**
   * 四舍五入
   * @param num - 数字
   * @param decimalPlaces - 小数位数
   * @returns 计算结果
   */
  static round(num: number | string, decimalPlaces?: number): number;

  /**
   * 向上取整
   * @param num - 数字
   * @returns 计算结果
   */
  static ceil(num: number | string): number;

  /**
   * 向下取整
   * @param num - 数字
   * @returns 计算结果
   */
  static floor(num: number | string): number;

  /**
   * 取绝对值
   * @param num - 数字
   * @returns 计算结果
   */
  static abs(num: number | string): number;

  /**
   * 比较两个数字
   * @param a - 数字a
   * @param b - 数字b
   * @returns 比较结果 (-1: a < b, 0: a = b, 1: a > b)
   */
  static compare(a: number | string, b: number | string): number;

  /**
   * 检查是否为零
   * @param num - 数字
   * @returns 是否为零
   */
  static isZero(num: number | string): boolean;

  /**
   * 检查是否为正数
   * @param num - 数字
   * @returns 是否为正数
   */
  static isPositive(num: number | string): boolean;

  /**
   * 检查是否为负数
   * @param num - 数字
   * @returns 是否为负数
   */
  static isNegative(num: number | string): boolean;
}

export default MathUtils;
