<script setup lang="ts">
defineOptions({ name: "CasinoCate" });

import { ref, reactive, computed, onMounted, onActivated, nextTick } from "vue";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { useRoute } from "vue-router";
import { useCasinoStore } from "@/stores/gameCasino";
import CustomNavbar from "./Components/CustomNavbar.vue";
import { GameFiltersStorage } from "@/utils/managers/GameFiltersStorage";
import GameItem from "@/components/GameItem.vue";

const casinoStore = useCasinoStore();

const route = useRoute();
const navbarRef = ref();

// 游戏容器引用，用于滚动
const gamesContainers = ref<Record<string, HTMLElement>>({});

// 设置游戏容器引用
const setGamesContainerRef = (el: HTMLElement | null, tabId: string) => {
  if (el) {
    gamesContainers.value[tabId] = el;
  }
};

// 滚动到当前标签页顶部
const scrollToCurrentTabTop = () => {
  nextTick(() => {
    const currentTab = casinoStore.filteredTabs[casinoStore.currentIndex];
    if (!currentTab) return;

    const container = gamesContainers.value[String(currentTab.id)];
    if (container) {
      container.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  });
};

// 使用本地存储管理筛选状态
const pageFilters = reactive(GameFiltersStorage.getFilters());

// 辅助函数：同步本地状态
const syncLocalState = () => {
  Object.assign(pageFilters, GameFiltersStorage.getFilters());
};

// 计算是否有筛选条件
const hasFilters = computed((): boolean => {
  return Boolean(
    pageFilters.selectedProviders.length > 0 && !pageFilters.selectedProviders.includes("all")
  );
});

// 搜索、筛选、清除使用本地存储
const handleConfirmFilters = async (categories: string[] = [], providerDetails: any[] = []) => {
  showZLoading();

  try {
    GameFiltersStorage.setSelectedProviders(
      categories.length > 0 ? categories : ["all"],
      providerDetails
    );
    syncLocalState();

    // 等待一小段时间确保状态更新完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    scrollToCurrentTabTop(); // 触发滚动
  } finally {
    closeZLoading();
  }
};

const handleClearFilters = async () => {
  showZLoading();

  try {
    GameFiltersStorage.clearFilters();
    syncLocalState();
    navbarRef.value?.setCheckedProviders();

    // 等待一小段时间确保状态更新完成
    await new Promise((resolve) => setTimeout(resolve, 200));

    scrollToCurrentTabTop(); // 触发滚动
  } finally {
    closeZLoading();
  }
};

const handleSearch = async (value: string) => {
  // 只有在有搜索内容时才显示 loading
  if (value && value.trim()) {
    showZLoading();
  }

  try {
    GameFiltersStorage.setSearchValue(value || "");
    syncLocalState();

    // 等待一小段时间确保状态更新完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    scrollToCurrentTabTop(); // 触发滚动
  } finally {
    // 只有在显示了 loading 时才关闭
    if (value && value.trim()) {
      closeZLoading();
    }
  }
};

const shouldShowNoData = (tab: any) => {
  return !casinoStore.isDataLoading && (!tab.pagedGames || !tab.pagedGames.length);
};

onMounted(() => {
  // 首次进入时，从 URL 初始化筛选条件
  GameFiltersStorage.initFromQuery(route.query);
  syncLocalState();
  casinoStore.fetchCasinoGames();
});

onActivated(() => {
  casinoStore.initializeTabFromRoute(route.query.id);
  // 激活时刷新导航栏状态和本地状态
  syncLocalState();
  navbarRef.value?.forceRefresh?.();
});
</script>

<template>
  <ZPage>
    <div class="categories-container">
      <div class="nav-bar-effect">
        <CustomNavbar
          ref="navbarRef"
          class="nav-bar"
          :confirm="handleConfirmFilters"
          :hasFilters="hasFilters"
          :searchValue="pageFilters.searchValue"
          :selectedProviders="pageFilters.selectedProviders"
          :providerDetails="pageFilters.providerDetails"
          v-model:visible="casinoStore.dialogVisible"
          @search="handleSearch"
          @confirm-filters="handleConfirmFilters"
          @clear-filters="handleClearFilters"
        />
      </div>
      <div class="categories">
        <div class="categories-tabs">
          <van-tabs
            v-model:active="casinoStore.currentIndex"
            @change="casinoStore.handleTabChange"
            swipeable
            shrink
            line-height="0"
            background="transparent"
          >
            <van-tab v-for="tab in casinoStore.filteredTabs" :key="tab.id" :title="tab.name">
              <div
                class="games-container"
                :ref="(el) => setGamesContainerRef(el as HTMLElement, String(tab.id))"
                @scroll="casinoStore.handleScroll($event, tab.id)"
              >
                <van-row
                  class="games-grid"
                  gutter="12"
                  v-if="tab.pagedGames && tab.pagedGames.length > 0"
                >
                  <van-col v-for="game in tab.pagedGames" :key="`${game.id}`" :span="8">
                    <GameItem :game="game as any" @updateLike="casinoStore.updateGameLike" />
                  </van-col>
                </van-row>
                <div v-if="shouldShowNoData(tab)" class="no-data">
                  <template v-if="casinoStore.hasFilters">
                    <div>
                      <ZNoData text="Your filters has returned no results"></ZNoData>
                      <div @click="handleClearFilters" class="clear-filters-btn">Clear Filters</div>
                    </div>
                  </template>
                  <template v-else>
                    <ZNoData text="No Record"></ZNoData>
                  </template>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<style lang="scss" scoped>
// 防止页面滚动穿透
:global(body) {
  overflow: hidden;
}

:global(html) {
  overflow: hidden;
}
.categories-container {
  height: 100%; // 使用视口高度确保全屏
  overflow: hidden; // 防止页面级别的滚动
  position: relative;

  .nav-bar-effect {
    min-height: 60px;

    .nav-bar {
      // 导航栏固定顶部并添加磨砂玻璃效果
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      z-index: 1000; // 提高 z-index 确保在最顶层
      // 防止被其他元素覆盖
    }
  }

  .categories {
    height: calc(100% - 60px);
    overflow: hidden;

    .categories-tabs {
      height: 100%;

      &:deep(.van-tabs) {
        height: 100%;

        .van-tabs__nav {
          // 优化 tab 导航栏样式，确保固定在顶部
          backdrop-filter: blur(12px);
          position: sticky;
          top: 0;
          z-index: 100; // 提高 z-index
          width: 100%;
          // 确保不被遮挡
          transform: translateZ(0);
        }

        .van-tabs__content {
          height: calc(100% - 44px);
        }

        .van-tab__panel {
          height: 100%;
        }
      }

      .games-container {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden; // 防止水平滚动
        padding: 12px;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        // 确保滚动容器独立
        position: relative;
        // 防止滚动穿透
        overscroll-behavior: contain;

        .games-grid {
          /* 移除重复的滚动设置，避免双重滚动条 */

          &:deep(.game-item) {
            margin-bottom: 20px;

            .game-item-img {
              height: 110px !important;
            }

            .game-item-like {
              right: 6px;
              bottom: 6px;
            }
          }
        }

        &::-webkit-scrollbar {
          width: 0;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 20px;
        text-align: center;

        .clear-filters-btn {
          margin-top: 16px;
          cursor: pointer;
          background-color: #eee;
          display: inline-block;
          padding: 6px 10px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
