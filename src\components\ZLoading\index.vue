<template>
  <div class="bg">
    <ZFrameAnimation ref="walkAnimation" animationName="walk" :frameUrls="walkFrames" :fps="19"
      :imageStyle="{ width: 100, height: 92 }" :centerAlign="true" :fixedSize="true" :normalizeSize="true"
      objectFit="contain" :preloadFrames="10" />
  </div>
</template>
<script setup>
import ZFrameAnimation from '@/components/ZFrameAnimation/index.vue';
import nustar2 from '@/assets/frame/nustart/2.png';
import nustar3 from '@/assets/frame/nustart/3.png';
import nustar4 from '@/assets/frame/nustart/4.png';
import nustar5 from '@/assets/frame/nustart/5.png';
import nustar6 from '@/assets/frame/nustart/6.png';
import nustar7 from '@/assets/frame/nustart/7.png';
import nustar8 from '@/assets/frame/nustart/8.png';
import nustar9 from '@/assets/frame/nustart/9.png';
import nustar10 from '@/assets/frame/nustart/10.png';
import nustar11 from '@/assets/frame/nustart/11.png';
import nustar12 from '@/assets/frame/nustart/12.png';
import nustar13 from '@/assets/frame/nustart/13.png';
import nustar14 from '@/assets/frame/nustart/14.png';
import nustar15 from '@/assets/frame/nustart/15.png';
import nustar16 from '@/assets/frame/nustart/16.png';
import nustar17 from '@/assets/frame/nustart/17.png';
import nustar18 from '@/assets/frame/nustart/18.png';
import nustar19 from '@/assets/frame/nustart/19.png';
import nustar20 from '@/assets/frame/nustart/20.png';

const walkFrames = [
  nustar2,
  nustar3,
  nustar4,
  nustar5,
  nustar6,
  nustar7,
  nustar8,
  nustar9,
  nustar10,
  nustar11,
  nustar12,
  nustar13,
  nustar14,
  nustar15,
  nustar16,
  nustar17,
  nustar18,
  nustar19,
  nustar20,
];


</script>

<style scoped lang="scss">
.bg {
  width: 100px;
  height: 92px;
  background-color: #E5E5E5;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
