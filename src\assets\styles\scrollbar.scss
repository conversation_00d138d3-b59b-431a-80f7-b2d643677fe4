/**
 * 全局滚动条美化样式
 * 适配不同浏览器和设备
 */

/* ==================== 基础滚动条样式 ==================== */

/* WebKit 浏览器 (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.6);
}

/* 滚动条角落 */
::-webkit-scrollbar-corner {
  background: transparent;
}

/* ==================== 细滚动条样式 ==================== */

/* 适用于小容器的细滚动条 */
.thin-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 2px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* ==================== 隐藏滚动条样式 ==================== */

/* 隐藏滚动条但保持滚动功能 */
.hidden-scrollbar {
  /* Firefox */
  scrollbar-width: none;
  /* IE and Edge */
  -ms-overflow-style: none;
}

.hidden-scrollbar::-webkit-scrollbar {
  display: none;
}

/* ==================== 深色主题滚动条 ==================== */

.dark-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dark-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.dark-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.dark-scrollbar::-webkit-scrollbar-thumb:active {
  background: rgba(255, 255, 255, 0.7);
}

/* ==================== 彩色滚动条样式 ==================== */

/* 蓝色主题滚动条 */
.blue-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #4facfe 0%, #00f2fe 100%);
}

.blue-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #3b8bfe 0%, #00d4fe 100%);
}

/* 绿色主题滚动条 */
.green-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #43e97b 0%, #38f9d7 100%);
}

.green-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2dd865 0%, #1ee6c1 100%);
}

/* 紫色主题滚动条 */
.purple-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #a855f7 0%, #e879f9 100%);
}

.purple-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9333ea 0%, #d946ef 100%);
}

/* ==================== 移动端优化 ==================== */

/* 移动端滚动优化 */
@media (max-width: 768px) {
  /* 移动端使用更细的滚动条 */
  ::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.25);
  }
}

/* 触摸设备滚动优化 */
@media (pointer: coarse) {
  /* 触摸设备隐藏滚动条 */
  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
}

/* ==================== 特殊容器滚动条 ==================== */

/* 游戏列表滚动条 */

/* ==================== Firefox 滚动条样式 ==================== */

/* Firefox 滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
}

/* 细滚动条 */
.thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.15) rgba(0, 0, 0, 0.03);
}

/* 深色主题 */
.dark-scrollbar {
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}

/* ==================== 平滑滚动 ==================== */

/* 全局平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 容器平滑滚动 */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* ==================== 滚动条动画 ==================== */

/* 滚动条出现动画 */
@keyframes scrollbar-appear {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 悬停时显示滚动条 */
.hover-scrollbar {
  overflow: auto;
}

.hover-scrollbar::-webkit-scrollbar {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hover-scrollbar:hover::-webkit-scrollbar {
  opacity: 1;
}

/* ==================== 自定义滚动条工具类 ==================== */

/* 无滚动条 */
.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* 自动隐藏滚动条 */
.auto-hide-scrollbar::-webkit-scrollbar {
  width: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.auto-hide-scrollbar:hover::-webkit-scrollbar {
  opacity: 1;
}

/* 圆角滚动条 */
.rounded-scrollbar::-webkit-scrollbar {
  width: 10px;
}

.rounded-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

.rounded-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
}
