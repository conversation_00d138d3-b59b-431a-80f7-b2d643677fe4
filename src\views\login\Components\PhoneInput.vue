<template>
  <div class="phone-input-container">
    <div class="input-wrap">
      <span class="phone-code">+63</span>
      <input
        class="input"
        :autofocus="false"
        id="phone"
        maxlength="10"
        v-model="inputValue"
        type="number"
        inputmode="numeric"
        @blur="handleBlur"
        @focus="handleFocus"
        @input="handleInput"
        :placeholder="placeholder"
      />
      <ZIcon v-show="inputValue" type="icon-close" size="16" color="#999" @click="clearInput" />
    </div>
    <div v-if="errorMessage" class="null-tip">{{ errorMessage }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits, onUnmounted } from "vue";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { isPhilippinePhoneNumber } from "@/utils/core/tools";
import { onMounted } from "vue";

interface Props {
  modelValue?: string;
  placeholder?: string;
  required?: boolean;
  validateOnInput?: boolean;
  validateOnBlur?: boolean;
  errorMessages?: {
    required: string;
    invalid: string;
    noRegister: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "Enter Your Phone Number",
  required: true,
  validateOnInput: false,
  validateOnBlur: true,
  errorMessages: () => ({
    required: "Please enter the phone number",
    invalid: "Please enter a valid 10-digit mobile phone number",
    noRegister: "Please register an account first",
  }),
});

const emits = defineEmits<{
  "update:modelValue": [value: string];
  validityChange: [isValid: boolean];
}>();

const inputValue = ref(props.modelValue);
const errorMessage = ref("");

// 计算属性：手机号是否有效
const isValid = computed(() => {
  if (!inputValue.value) return false;
  return isPhilippinePhoneNumber(inputValue.value);
});

onMounted(() => {
  const phoneNum = getLocalStorage("phone");
  if (phoneNum) {
    inputValue.value = phoneNum;
    emits("update:modelValue", phoneNum + "");
  }
});

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal;
    if (props.validateOnInput) {
      validatePhone();
    }
  }
);
// 清除输入框内容
const clearInput = () => {
  inputValue.value = "";
  errorMessage.value = "";
  emits("update:modelValue", "");
  emits("validityChange", false);
};

// 处理输入事件
const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const val = target.value;
  // 清除错误信息
  if (errorMessage.value) {
    errorMessage.value = "";
  }

  // 限制最大长度
  if (val.length > 10) {
    inputValue.value = val.substring(0, 10);
  } else {
    inputValue.value = val;
  }

  // 同步到父组件
  emits("update:modelValue", inputValue.value);

  // 实时验证
  if (props.validateOnInput) {
    validatePhone();
  }

  // 通知父组件有效性变化
  emits("validityChange", isValid.value);
};

// 处理失焦事件
const handleBlur = () => {
  if (props.validateOnBlur) {
    validatePhone();
  }
};

// 处理聚焦事件
const handleFocus = () => {
  // 聚焦时清除错误信息
  errorMessage.value = "";
};

// 验证手机号
const validatePhone = () => {
  if (!inputValue.value) {
    if (props.required) {
      errorMessage.value = props.errorMessages.required;
    } else {
      errorMessage.value = "";
    }
  } else if (!isPhilippinePhoneNumber(inputValue.value)) {
    errorMessage.value = props.errorMessages.invalid;
  } else {
    errorMessage.value = "";
    setLocalStorage("phone", inputValue.value);
  }

  // 通知父组件有效性变化
  emits("validityChange", isValid.value);
};

// 手动显示错误
const showError = (message: string) => {
  errorMessage.value = message;
};

// 清除错误
const clearError = () => {
  errorMessage.value = "";
};

// 组件销毁时清理
onUnmounted(() => {
  errorMessage.value = "";
  inputValue.value = "";
});

// 暴露方法给父组件
defineExpose({
  showError,
  clearError,
  validatePhone,
  isValid: () => isValid.value,
});
</script>

<style scoped lang="scss">
.phone-input-container {
  .input-wrap {
    width: 100%;
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #ddd;
    outline: none;
    display: flex;
    align-items: center;

    .phone-code {
      font-size: 16px;
      line-height: 14px;
      color: #222;
      border-right: 1px solid #ddd;
      padding-right: 6px;
      white-space: nowrap;
      font-weight: 600;
      font-family: D-DIN;
      font-size: 18px;
    }

    .input {
      width: 100%;
      margin-left: 6px;
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      font-weight: 600;
      color: #222;
      font-family: D-DIN;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      &::placeholder {
        color: #c0c0c0;
        /* 输入框内默认文字 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .null-tip {
    margin-top: 4px;
    color: var(--Nustar, #ac1140);
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
