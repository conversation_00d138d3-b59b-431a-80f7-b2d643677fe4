<template name="FootPng">
  <footer class="foot-png" :style="background ? { background: background } : {}">
    <img :src="background ? imageSrc : nsFootPngBlack" alt="" class="foot-icon" />
  </footer>
</template>

<script setup lang="ts">
import nsFootPng from "@/assets/images/ns-foot.png";
import nsFootPngBlack from "@/assets/images/ns-foot-black.png";
// 可选的props，用于自定义样式或图片
interface Props {
  /** 自定义图片路径 */
  imageSrc?: string;
  /** 背景颜色 */
  background?: string;
}

const props = withDefaults(defineProps<Props>(), {
  imageSrc: nsFootPng,
  background: "",
});
</script>

<style lang="scss" scoped>
.foot-png {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;

  .foot-icon {
    width: 100%;
    height: auto;
    object-fit: contain;
  }
}
</style>
