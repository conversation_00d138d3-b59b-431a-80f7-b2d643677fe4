<template>
  <div class="exception-refresh">
    <img src="@/assets/images/noWifi.png" alt="Network Error" />
    <div class="exception-title">{{ title }}</div>
    <div class="exception-message">{{ message }}</div>
    <ZButton
      size="mini"
      class="refresh-btn"
      :disabled="isRefreshing"
      :loading="isRefreshing"
      @click="handleRefresh"
    >
      {{ refreshText }}
    </ZButton>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from "vue";

interface Props {
  /** 标题文本 */
  title?: string;
  /** 错误消息 */
  message?: string;
  /** 刷新按钮文本 */
  refreshText?: string;
}

interface Emits {
  /** 点击刷新按钮时触发 */
  (e: "refresh"): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: "Oops !",
  message: "There is an abnormality in the network",
  refreshText: "Refresh",
});

const emit = defineEmits<Emits>();

// 响应式状态
const isRefreshing = ref(false);

// 处理刷新点击
const handleRefresh = async () => {
  if (isRefreshing.value) {
    return;
  }
  try {
    isRefreshing.value = true;
    // 触发刷新事件
    emit("refresh");
  } catch (error) {
  } finally {
    isRefreshing.value = false;
  }
};
</script>
<style lang="scss" scoped>
.exception-refresh {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  font-family: Inter;

  img {
    width: 112px;
    height: auto;
    margin-bottom: 16px;
  }

  .exception-title {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .exception-message {
    color: #999;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 24px;
    max-width: 280px;
  }

  .refresh-btn {
    height: 40px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 20px;
    width: 100px !important;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}
</style>
