<template>
  <ZPopOverlay :show="showVipTip">
    <div class="content">
      <div class="top_v">
        <img :src="vip1Image" />
      </div>
      <div class="top_p">
        <span class="bg" :style="{ backgroundImage: `url(${vip2Image})` }">
          <div class="text">
            Congratulations !
          </div>
        </span>
      </div>
      <div class="vip_tip">You’re now a VIP !</div>
      <div class="vip_desc">Enjoy 0.8% cashback and other exclusive privileges!</div>
      <ZButton color="#000" @click="handleDetail">Detail</ZButton>
      <div class="close" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="30" />
      </div>
    </div>
  </ZPopOverlay>
</template>
<script lang="ts" setup>
import { useAutoPopMgrStore, STORE_KEY_MAP } from "@/stores/autoPopMgr";
import { useGlobalStore } from "@/stores/global";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { setLocalStorage, } from "@/utils/core/Storage";
import router from '@/router';
// 导入图片资源，确保与预加载使用相同的路径
import vip1Image from '@/assets/images/popDialog/vip1.png';
import vip2Image from '@/assets/images/popDialog/vip2.png';

const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showVipTip } = storeToRefs(autoPopMgrStore)

const handleClose = () => {
  showVipTip.value = false
  AutoPopMgr.destroyCurrentPopup()
}

const handleDetail = () => {
  showVipTip.value = false
  router.push(`/account/vip-introduction`)
}

const handle = () => {
  if (globalStore.userInfo) {
    const currentDate = new Date();
    const dayOfMonth = currentDate.getDate();
    const isWithinFirstHalf = dayOfMonth >= 1 && dayOfMonth <= 15;
    const isWithinSecondHalf = dayOfMonth >= 16 && dayOfMonth <= 31;
    let isVip = parseInt(globalStore.userInfo?.is_vip) || 0;
    if (isVip && isWithinFirstHalf) {
      //本地存储 是否弹出过vip升级提示
      setLocalStorage(STORE_KEY_MAP.HAS_SHOWN_UPGRADE_TIP + globalStore.userInfo.user_id, true);
    } else if (isVip && isWithinSecondHalf) {
      //本地存储 是否弹出过vip升级提示
      setLocalStorage(STORE_KEY_MAP.HAS_SHOWN_UPGRADE_TIP2 + globalStore.userInfo.user_id, true);
    }
  }
}

watch(
  () => autoPopMgrStore.showVipTip,
  (newVal) => {
    if (newVal) {
      handle()
    }
  }, { immediate: false }
);

</script>
<style lang="scss" scoped>
.content {
  width: 330px;
  height: 287px;
  padding: 20px;
  border-radius: 33px;
  box-sizing: border-box;
  background: linear-gradient(180deg, #FFF7DD 0%, #FFF 70%);
  position: relative;
  z-index: 9;

  .close {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
  }

  .vip_tip {
    color: #D89000;
    text-align: center;
    font-family: Inter;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-align: center;
    margin-top: 10px;
  }

  .vip_desc {
    margin: 20px auto 30px;
    text-align: center;
    color: #222;
    text-align: center;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    /* 150% */
  }

  .top_v {
    width: 152px;
    height: 152px;
    position: absolute;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 8;
  }

  .top_p {
    width: 268px;
    height: 52px;

    .bg {
      width: 268px;
      height: 52px;
      position: absolute;
      top: 0px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 8;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .text {
        color: #222;
        text-align: center;
        font-family: Inter;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }
    }



  }
}
</style>
