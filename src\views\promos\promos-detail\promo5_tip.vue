<template>
  <XPage navTitle="LeaderBoard" :navBarStyle="{ backgroundColor: '#924aff', color: '#fff' }">
    <div class="promo5-tip">
      <h1>General Mechanics</h1>
      <ol>
        <li>This promotion is open to both new and existing NUSTAR members.</li>
        <li>
          Bets placed on EVO, Pragmatic Play, and PLAY TIME along with a qualifying deposit history,
          are eligible. Eligible games include all Pragmatic Play casino games, EVO games (excluding
          First Person games, e.g., First Person HiLo, First Person Deal or No Deal), and PLAY
          TIME's Peraphy Time.
        </li>
        <li>
          The top 20 NUSTAR members with the highest total betting amounts across EVO, Pragmatic
          Play, and PLAY TIME will be ranked daily and rewarded according to the table below.
        </li>
      </ol>
      <div class="table-container">
        <van-row class="table-head">
          <van-col span="6">Ranking</van-col>
          <van-col span="12">Number of Winners</van-col>
          <van-col span="6">Bonus</van-col>
        </van-row>
        <van-row v-for="row in prizeList" :key="row.ranking">
          <van-col span="6">{{ row.ranking }}</van-col>
          <van-col span="12">{{ row.numberOfWinners }}</van-col>
          <van-col span="6">₱{{ row.bonus }}</van-col>
        </van-row>
      </div>
      <ol start="4">
        <li>
          In the event of a tied ranking, the NUSTAR members of the same ranking will all be
          rewarded.
        </li>
        <li>
          Bonuses will be distributed automatically to eligible NUSTAR member's accounts by 3 PM the
          following day.
        </li>
        <!-- <li>There is NO wagering requirement for withdrawal.</li> -->
      </ol>
    </div>
    <ZFootPng />
  </XPage>
</template>

<script setup>
const prizeList = [
  { ranking: "1st", numberOfWinners: 1, bonus: "30,000" },
  { ranking: "2nd", numberOfWinners: 1, bonus: "10,000" },
  { ranking: "3rd", numberOfWinners: 1, bonus: "5,000" },
  { ranking: "4th-10th", numberOfWinners: 7, bonus: "1,000" },
  { ranking: "11st-20th", numberOfWinners: 10, bonus: "500" },
  { ranking: "21st - 50th", numberOfWinners: 30, bonus: "300" },
  { ranking: "51th - 100th", numberOfWinners: 50, bonus: "100" },
];
</script>

<style scoped lang="scss">
.promo5-tip {
  background: linear-gradient(180deg, #4d13f5, #8f48fe); // 紫色背景
  color: #efe3fc;
  font-size: 16px;
  min-height: 100vh;
  padding: 24px 20px 32px;
  font-family: "Inter", "D-DIN";
  font-weight: 300;
}

h1 {
  font-size: 22px;
  font-weight: 600;
  color: #f9edba; // 淡黄色
  margin: 28px 0;
}

ol {
  margin-left: 18px;
  margin-bottom: 18px;
  font-size: 1.1rem;
  list-style: auto;

  li {
    margin-top: 20px;
    color: #efe3fc;
    line-height: 1.7;
  }
}

.table-container {
  margin: 16px 10px;
  font-size: 12px;
  background: #8b2dff;
  text-align: center;
  line-height: 34px;
  border: 1px solid #9762fa;
  box-shadow: -1px 1px 4px 1px rgba(13, 0, 0, 0.3), 1px -1px 4px 1px rgba(13, 0, 0, 0.1);
  border-radius: 16px;
  overflow: hidden;
  font-weight: 300;

  .table-head {
    background: #7002f9;
    color: #ffe082;
  }

  &:deep(.van-row) {
    border-top: 1px solid #9762fa;
    color: #fff;
    &:first-child {
      border: none;
    }
    .van-col {
      &:first-child,
      &:nth-child(2) {
        border-right: 1px solid #9762fa;
      }
    }
  }
}

.footer-bar {
  width: 100%;
  background-color: #000000;

  img {
    width: 100%;
  }
}
</style>
