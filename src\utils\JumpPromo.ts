import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import router from "@/router";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";

/**
 * 活动跳转工具类
 * 处理 banner 和 promos 的跳转逻辑
 */

export interface PromoItem {
  home_page_jump?: number;
  jump_type?: string | number;
  activity_list?: string | number;
  url?: string;
  [key: string]: any;
}

/**
 * 活动跳转方法
 * @param item 活动项数据  jump_type: 0 None不跳转，1 Internal Url 内部框架打开URL链接，2 Outside URL 跳转到外部浏览器打开URL，4 Activity detail 程序定义的详情页枚举，7 Custom Activity 自定义活动
 * @param source 来源类型，默认为 "promos"
 */
export const jumpPromo = async (item: PromoItem, source: string = "promos") => {
  if (!router) {
    console.error("Router not available");
    return;
  }

  // banner跳转根据home_page_jump判断，1时跳转promos列表页面，2时跳转自定义页面
  if (item.home_page_jump == 1 && source == "banner") {
    router.push("/promos");
    return;
  }

  // 自定义活动
  if (`${item.jump_type}` === "7") {
    router.push({
      path: `/promos/promo_0`,
      query: { detail: JSON.stringify(item) },
    });
    return;
  }

  // 固定活动 promo详情
  if (`${item.jump_type}` === "4") {
    // 大转盘
    if (item.activity_list === "6") {
      const autoPopMgrStore = useAutoPopMgrStore();
      const { showSpinWheelTip } = storeToRefs(autoPopMgrStore);
      // 更新store状态
      showSpinWheelTip.value = true;
      return;
    }
    const path = `/promos/promo_${item.activity_list}`;
    router.push({ path, query: { detail: JSON.stringify(item) } });
    return;
  }

  // 外跳
  if (`${item.jump_type}` === "2") {
    if (!item.url) {
      console.warn("External jump URL is missing");
      return;
    }

    // 使用统一的外跳方法
    try {
      await MobileWindowManager.launchExternalGame(
        async () => item.url,
        (error) => {
          console.error("Failed to open promo external URL:", item.url, error);
        }
      );
    } catch (error) {
      console.error("Promo external jump error:", error);
    }

    return;
  }

  // 内跳
  if (`${item.jump_type}` === "1") {
    router.push({ path: "/promo-webview", query: { url: item.url } });
    return;
  }

  // 大转盘
  if (item.activity_list === "6") {
    const autoPopMgrStore = useAutoPopMgrStore();
    autoPopMgrStore.openSpinWheel();
    return;
  }
};

/**
 * Banner 跳转的便捷方法
 * @param item banner 数据
 */
export const jumpBanner = (item: PromoItem) => {
  jumpPromo(item, "banner");
};

/**
 * Promos 跳转的便捷方法
 * @param item promos 数据
 */
export const jumpPromosItem = (item: PromoItem) => {
  jumpPromo(item, "promos");
};
