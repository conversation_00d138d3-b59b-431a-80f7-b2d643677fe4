/**
 * 上传相关的类型定义
 */

// 上传错误类型枚举
export enum UploadErrorType {
  /** 必填字段未填写（未上传） */
  REQUIRED = "required",
  /** 上传过程中失败 */
  UPLOAD_FAILED = "upload_failed",
}

// 上传状态枚举
export enum UploadStatus {
  /** 未开始 */
  IDLE = "idle",
  /** 上传中 */
  UPLOADING = "uploading",
  /** 上传成功 */
  SUCCESS = "success",
  /** 上传失败 */
  FAILED = "failed",
}

// 上传组件的验证结果接口
export interface UploadValidationResult {
  /** 是否验证通过 */
  isValid: boolean;
  /** 错误类型（如果有错误） */
  errorType?: UploadErrorType;
  /** 错误信息 */
  errorMessage?: string;
}
