<template>
  <ZPopOverlay :show="showPWAUnSupportGuide">
    <div class="content">
      <!-- 使用计算属性来动态获取图片 -->
      <img class="img" :src="currentGuideImage" :alt="isIOS ? 'iOS' : 'Android'" />
      <div class="close" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="30" />
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { recordPWAPromptShown } from "@/utils/pwa";
import { watch, computed, ref } from "vue";
import { isIOS } from "@/utils/core/tools";

// 导入图片
import iosGuideImage from "@/assets/images/popDialog/iosInstallGuider.png";
import androidGuideImage from "@/assets/images/popDialog/androidInstallGuider.png";

const autoPopMgrStore = useAutoPopMgrStore();
const { showPWAUnSupportGuide, pwaInfo } = storeToRefs(autoPopMgrStore);

// 计算当前应该显示的指南图片
const currentGuideImage = computed(() => {
  return isIOS ? iosGuideImage : androidGuideImage;
});

const handleClose = () => {
  showPWAUnSupportGuide.value = false;
  AutoPopMgr.destroyCurrentPopup();
};
watch(
  () => showPWAUnSupportGuide.value,
  (newVal) => {
    if (newVal) {
      // 从 pwaInfo 中获取每日最大弹窗次数，如果没有配置则使用默认值 3
      const maxDailyPrompts = pwaInfo.value?.PWA_open_times || 3;
      recordPWAPromptShown(maxDailyPrompts);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  position: relative;
  z-index: 9;
  width: 327px;
  .img {
    width: 100%;
    height: auto;
  }
  .close {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
