<template>
  <ZActionSheet
    v-model="dialogVisible"
    :closeOnClickOverlay="true"
    title="Select a Game Provider"
    :onConfirm="handleConfirm"
    :onCancel="handleCancel"
    :onClose="handleClose"
    cancelText="Clear Filters"
    :closeOnClickCancelBtn="false"
    :cancelDisabled="isClearFiltersDisabled"
  >
    <div class="content">
      <van-checkbox-group
        direction="horizontal"
        v-model="localCheckedOptions"
        class="checkbox-group"
        :key="checkboxGroupKey"
      >
        <van-checkbox name="all" key="all" class="checkbox">
          <template #icon>
            <div class="checkitem-All" @click.stop="() => handleCheckboxToggle('all')">
              <ZIcon type="icon-type" color="#666666"></ZIcon>
              All
            </div>
          </template>
        </van-checkbox>
        <van-checkbox
          v-for="provider in chooseProviders"
          :key="`${provider.id}`"
          :name="String(provider.id)"
          class="checkbox"
        >
          <template #icon>
            <div
              class="provider-checkitem"
              @click.stop="() => handleCheckboxToggle(String(provider.id))"
            >
              <template v-if="provider.imageUrl && !provider.imageError">
                <van-image
                  :src="provider.imageUrl"
                  @load="provider.imageError = false"
                  @error="provider.imageError = true"
                  :loading="provider.short_name || provider.provider"
                />
              </template>
              <div class="checkitem-All" v-if="provider.imageError">
                {{ provider.short_name || provider.provider }}
              </div>
            </div>
          </template>
        </van-checkbox>
      </van-checkbox-group>
    </div>
  </ZActionSheet>
</template>
<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";

const gameStore = useGameStore();
const { chooseProviders } = storeToRefs(gameStore);

interface ProviderDetail {
  id: string;
  provider: string;
}

interface Props {
  checkedProviders: string[];
  visible: boolean;
  confirm: (providers: string[], providerDetails: ProviderDetail[]) => void;
}

const props = defineProps<Props>();

// 定义 emits 事件
const emit = defineEmits<{
  "update:visible": [value: boolean];
}>();

// 本地选中项（用于临时状态，只有确认时才提交）
const localCheckedOptions = ref<string[]>(["all"]);
// 用于强制重新渲染 checkbox-group 的 key
const checkboxGroupKey = ref(0);

// 初始化本地选中项
const initializeLocalOptions = () => {
  // console.log("initializeLocalOptions props.checkedProviders:", props.checkedProviders);

  // 先去重并过滤空值
  const cleanedProviders = [...new Set(props.checkedProviders)].filter((p) => p && p.trim());

  // 如果清理后为空或者只包含无效值，默认选择 "all"
  if (cleanedProviders.length === 0) {
    localCheckedOptions.value = ["all"];
  } else {
    // 如果传入的值包含 "all"，则只保留 "all"
    if (cleanedProviders.includes("all")) {
      localCheckedOptions.value = ["all"];
    } else {
      localCheckedOptions.value = [...cleanedProviders];
    }
  }

  // 强制重新渲染 checkbox-group
  checkboxGroupKey.value++;
};

// 监听 props 变化，同步到本地状态
watch(
  () => props.checkedProviders,
  () => {
    initializeLocalOptions();
  },
  { immediate: true }
);

// 监听弹窗显示状态，当弹窗打开时重置本地状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 弹窗打开时，重置本地状态为 props 的值
      initializeLocalOptions();
    }
  }
);

// 弹窗显示、隐藏控制
const dialogVisible = computed({
  get: () => props.visible,
  set: (val: boolean) => emit("update:visible", val),
});

// 计算 Clear Filters 按钮是否禁用（只有选中 "all" 时禁用）
const isClearFiltersDisabled = computed(() => {
  return localCheckedOptions.value.length === 1 && localCheckedOptions.value[0] === "all";
});

// 处理复选框切换
const handleCheckboxToggle = (value: string | number) => {
  const stringValue = String(value);

  if (stringValue === "all") {
    localCheckedOptions.value = ["all"];
  } else {
    if (localCheckedOptions.value.includes(stringValue)) {
      // 如果已选中，则取消选择
      const newSelection = localCheckedOptions.value.filter((v) => v !== stringValue);
      localCheckedOptions.value = newSelection.length === 0 ? ["all"] : newSelection;
    } else {
      // 如果未选中，则添加选择，并移除 "all"
      const withoutAll = localCheckedOptions.value.filter((v) => v !== "all");
      localCheckedOptions.value = [...withoutAll, stringValue];
    }
  }
};

// 缓存计算结果，避免重复计算
const cachedProviderMap = computed(() => {
  const map = new Map();
  chooseProviders.value.forEach((provider) => {
    map.set(String(provider.id), provider);
  });
  return map;
});

// 确认选择 - 优化版本，减少调用链
const handleConfirm = () => {
  // 第一优先级：立即关闭弹窗
  emit("update:visible", false);

  // 使用缓存的 Map 进行快速查找
  const cleanedSelection = [...new Set(localCheckedOptions.value)].filter(Boolean);
  const finalSelection = cleanedSelection.length > 0 ? cleanedSelection : ["all"];

  const providerDetails = finalSelection.map((id) => {
    if (id === "all") return { id: "all", provider: "All" };

    const provider = cachedProviderMap.value.get(String(id));
    return {
      id: id,
      provider: provider?.provider || provider?.short_name || "Unknown",
    };
  });

  // 调用回调
  props.confirm(finalSelection, providerDetails);
};

// 清空筛选（重置为 "all"）
const handleCancel = () => {
  localCheckedOptions.value = ["all"];
};

// 关闭弹窗时恢复原始状态
const handleClose = () => {
  // 重置到原始状态，key 的变化会强制重新渲染确保 UI 同步
  initializeLocalOptions();
};
</script>
<style lang="scss" scoped>
.content {
  .checkbox {
    margin-bottom: 6px;
  }
  &:deep(.van-checkbox-group) {
    width: 100%;
    display: flex;
    .van-checkbox--horizontal {
      margin: 0;
      margin-bottom: 6px;
    }

    .van-checkbox {
      flex: 0 0 25%;
    }

    .van-checkbox__icon {
      padding: 3px;
      height: 52px;
      text-align: center;
      line-height: 40px;
      border: 3px solid transparent;
      font-size: 16px;
      border-radius: 18px;

      &.van-checkbox__icon--checked {
        border-color: #ac1140;
      }
    }

    .checkitem-All {
      width: 70px;
      color: #666666;
      background-color: #f9eef2;
      border-radius: 12px;
      cursor: pointer;
      user-select: none;
    }

    .provider-checkitem {
      border-radius: 12px;
      color: #666666;
      width: 70px;
      height: 40px;
      overflow: hidden;
      cursor: pointer;
      user-select: none;
    }

    .van-image {
      width: 100%;
    }

    .van-image__img {
      width: 100%;
      height: 40px;
    }

    .van-image__loading {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
