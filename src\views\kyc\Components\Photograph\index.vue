<template>
  <teleport to="body">
    <div class="kyc-photograph">
      <div v-show="currentStep === 1">
        <div class="kyc-photograph__camera" ref="cameraContainer">
          <!-- 加载中提示 -->
          <teleport v-if="isLoading" to="body">
            <div class="kyc-photograph__loading" v-if="isLoading">
              <ZLoading />
            </div>
          </teleport>
          <!-- 关闭按钮 -->
          <button class="kyc-photograph__close-btn" @click="handleClose" v-show="!isLoading">
            <ZIcon type="icon-close" color="#ddd" :size="32" />
          </button>
          <!-- 摄像头画面 -->
          <div class="kyc-photograph__video-container">
            <video ref="videoEl" autoplay playsinline id="kyc-photograph-video"></video>
            <canvas ref="canvasEl" style="display: none" id="kyc-photograph-canvas"></canvas>
            <div class="kyc-photograph-video-wrap">
              <div class="top-left"></div>
              <div class="top-right"></div>
              <div class="bottom-right"></div>
              <div class="bottom-left"></div>
            </div>
            <div class="kyc-photograph-tip">
              Please place the <b>photo of your ID</b> in the frame
            </div>
          </div>

          <!-- 拍照按钮 -->
          <button class="kyc-photograph__capture-btn" @click="handleCapture" v-show="!isLoading">
            <ZIcon type="icon-paizhaoanniu" color="#eee" :size="50" />
          </button>
        </div>
      </div>
      <div v-show="currentStep === 2" class="kyc-photograph__confirm">
        <div class="kyc-photograph__confirm-content">
          <div class="kyc-photograph__confirm-tip">Please confirm the front side</div>
          <div class="kyc-photograph__confirm-actions">
            <button class="kyc-photograph__reshoot-btn" @click="handleReshoot">Reshoot</button>
            <button class="kyc-photograph__continue-btn" @click="handleContinue">Continue</button>
          </div>
          <div class="kyc-photograph__photo-preview">
            <img :src="photoBase64" alt="front side photo" />
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { setupCamera } from "./media";
import { defineEmits } from "vue";

const currentStep = ref(1);
const photoBase64 = ref("");

const emit = defineEmits(["close", "confirm"]);

const handleReshoot = () => {
  currentStep.value = 1;
};
const handleContinue = () => {
  emit("close");
  emit("confirm", photoBase64.value);
};
const handleClose = () => {
  emit("close");
};

// 组件状态
const cameraContainer = ref<HTMLElement | null>(null);
const videoEl = ref<HTMLVideoElement | null>(null);
const canvasEl = ref<HTMLCanvasElement | null>(null);
const isLoading = ref(true);
const isError = ref(false);
const errorMessage = ref("");

// 媒体对象
const media = {
  canvasCtx: null as CanvasRenderingContext2D | null,
  video: null as HTMLVideoElement | null,
  mediaStream: null as MediaStream | null,
  animationFrameId: null as number | null,
};

// 初始化摄像头
const initCamera = async () => {
  try {
    if (!cameraContainer.value || !videoEl.value || !canvasEl.value) {
      throw new Error("Camera container not found");
    }

    const config = {
      el: cameraContainer.value,
      facingMode: "environment", // "environment" : "user";  切换后置/前置摄像头
      isAudio: false,
    };
    media.canvasCtx = canvasEl.value.getContext("2d");
    media.video = videoEl.value;
    await setupCamera(media, config, null);
    isLoading.value = false;
  } catch (err: any) {
    isError.value = true;
    errorMessage.value = err.message || "Camera initialization failed";
    console.error("Camera init error:", err);
  }
};

// 拍照
const handleCapture = () => {
  if (!media.canvasCtx || !media.video) {
    isError.value = true;
    errorMessage.value = "Canvas or Video not initialized";
    return;
  }
  try {
    const tempCanvas = document.createElement("canvas");
    tempCanvas.width = media.video.videoWidth;
    tempCanvas.height = media.video.videoHeight;
    const ctx = tempCanvas.getContext("2d");
    if (ctx) {
      ctx.drawImage(media.video, 0, 0, tempCanvas.width, tempCanvas.height);
      photoBase64.value = tempCanvas.toDataURL("image/png");
      currentStep.value = 2;
    }
  } catch (err: any) {
    isError.value = true;
    errorMessage.value = "Capture failed: " + err.message;
    console.error("Capture error:", err);
  }
};

// 销毁摄像头实例
const destroyCamera = () => {
  if (media.animationFrameId) {
    cancelAnimationFrame(media.animationFrameId);
  }
  if (media.mediaStream) {
    media.mediaStream.getTracks().forEach((track) => track.stop());
  }
  photoBase64.value = "";
};

onMounted(async () => {
  // await nextTick();
  await initCamera();
});
onUnmounted(() => {
  destroyCamera();
});
</script>

<style lang="scss" scoped>
$z-index-camera: 999999;
$z-index-loading: 1000000;

.kyc-photograph {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  z-index: $z-index-camera;
  background: #000;
  font-family: "Inter";

  &__camera {
    width: 100vw;
    height: 100vh;
    position: relative;
    background: #000;
  }

  &__video-container {
    width: 100%;
    margin: 0 auto;
    height: calc(100vh - 150px);
    height: calc(100dvh - 150px);
    position: absolute;
    top: 50px;
    left: 0%;

    video {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      object-fit: cover;
    }

    canvas {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      object-fit: cover;
      display: none;
    }
  }

  &__close-btn {
    position: fixed;
    top: 0px;
    right: 10px;
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
    z-index: 2;
  }

  &__capture-btn {
    position: fixed;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
    z-index: 2;
  }

  &__loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    inset: 0;
    z-index: $z-index-loading;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__confirm {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  &__confirm-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    position: relative;
  }

  &__confirm-tip {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    writing-mode: vertical-rl;
    text-orientation: mixed;
    font-size: 18px;
    color: #333;
  }

  &__confirm-actions {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &__reshoot-btn,
  &__continue-btn {
    width: 40px;
    height: 160px;
    border: none;
    border-radius: 16px;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }

  &__reshoot-btn {
    background-color: #888;
  }

  &__continue-btn {
    background-color: #333;
  }

  &__photo-preview {
    width: 240px;
    height: 360px;
    border: 2px solid #ccc;
    border-radius: 12px;
    overflow: hidden;
    margin-left: 25px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .kyc-photograph-video-wrap {
    width: 80%;
    height: 80%;
    margin: auto;
    position: absolute;
    left: 10%;
    top: 10%;
    border: 3px solid #fff;
    border-radius: 12px;
    > div {
      position: absolute;
      width: 30px;
      height: 30px;
    }
    .top-left {
      top: 10px;
      left: 10px;
      border-left: 3px solid #000;
      border-top: 3px solid #000;
      border-radius: 10px 0 0 0;
    }
    .top-right {
      top: 10px;
      right: 10px;
      border-right: 3px solid #000;
      border-top: 3px solid #000;
      border-radius: 0 10px 0 0;
    }
    .bottom-right {
      bottom: 10px;
      right: 10px;
      border-right: 3px solid #000;
      border-bottom: 3px solid #000;
      border-radius: 0 0 10px 0;
    }
    .bottom-left {
      bottom: 10px;
      left: 10px;
      border-left: 3px solid #000;
      border-bottom: 3px solid #000;
      border-radius: 0 0 0 10px;
    }
  }
  .kyc-photograph-tip {
    position: absolute;
    left: -45%;
    top: 45%;
    color: #fff;
    font-size: 18px;
    text-align: center;
    width: 100%;
    line-height: 1.5;
    font-weight: 600;
    transform: rotate(90deg);
    white-space: nowrap;
    b {
      color: #f9f770;
      font-weight: 600;
    }
  }
}
</style>
