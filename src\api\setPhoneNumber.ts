import http from "@/utils/http";

// 发送验证码
export const sendCodeMsg = (data = {}) => {
  return http.post("/common/api/sms/send/short/msg", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
//  绑定手机号时是否发送邮件通知
export const isBindSend = (data = {}) => {
  return http.post("/common/api/is/bind/send", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

//  更新phone
export const changePhone = (data = {}) => {
  return http.post("/common/api/update/bind/phone", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
//  绑定phone
export const bindPhone = (data = {}) => {
  return http.post("/common/api/player/add-bind", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// 重置密码验证code
export const verifyCode = (data = {}) => {
  return http.post("/common/api/sms/verify/short/msg/code", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
