/**
 * 组件预加载工具
 * 提前加载可能使用的组件，减少首次渲染时间
 */

interface ComponentConfig {
  name: string;
  loader: () => Promise<any>;
  priority: "high" | "medium" | "low";
}

class ComponentPreloader {
  private preloadedComponents = new Set<string>();
  private preloadPromises = new Map<string, Promise<any>>();

  /**
   * 预加载单个组件
   */
  async preloadComponent(config: ComponentConfig): Promise<void> {
    const { name, loader, priority } = config;

    if (this.preloadedComponents.has(name)) {
      return;
    }

    if (this.preloadPromises.has(name)) {
      return this.preloadPromises.get(name);
    }

    console.log(`🔄 开始预加载组件: ${name} (${priority})`);
    const promise = loader();
    this.preloadPromises.set(name, promise);

    try {
      await promise;
      this.preloadedComponents.add(name);
      console.log(`✅ 组件预加载完成: ${name}`);
    } catch (error) {
      console.warn(`❌ 组件预加载失败: ${name}`, error);
    } finally {
      this.preloadPromises.delete(name);
    }
  }

  /**
   * 批量预加载组件
   */
  async preloadComponents(components: ComponentConfig[]): Promise<void> {
    // 按优先级分组
    const highPriority = components.filter((c) => c.priority === "high");
    const mediumPriority = components.filter((c) => c.priority === "medium");
    const lowPriority = components.filter((c) => c.priority === "low");

    // 高优先级并行加载
    if (highPriority.length > 0) {
      await Promise.all(highPriority.map((c) => this.preloadComponent(c)));
    }

    // 中优先级串行加载
    for (const component of mediumPriority) {
      await this.preloadComponent(component);
    }

    // 低优先级在空闲时加载
    if (lowPriority.length > 0) {
      this.preloadOnIdle(lowPriority);
    }
  }

  /**
   * 在浏览器空闲时预加载
   */
  private preloadOnIdle(components: ComponentConfig[]): void {
    if ("requestIdleCallback" in window) {
      requestIdleCallback(async () => {
        for (const component of components) {
          await this.preloadComponent(component);
        }
      });
    } else {
      setTimeout(async () => {
        for (const component of components) {
          await this.preloadComponent(component);
        }
      }, 2000);
    }
  }

  /**
   * 预加载核心组件
   * 注意：大部分核心组件已被静态导入，暂时禁用预加载避免冲突
   */
  async preloadCoreComponents(): Promise<void> {
    console.log("核心组件已被静态导入，跳过预加载");
    // 暂时禁用所有组件预加载，避免与静态导入冲突
  }

  /**
   * 预加载游戏相关组件
   * 注意：暂时禁用以避免与静态导入的冲突
   */
  async preloadGameComponents(): Promise<void> {
    console.log("游戏组件预加载已禁用，避免与静态导入冲突");
    // 暂时禁用游戏组件预加载
  }

  /**
   * 预加载弹窗组件
   * 注意：大部分弹窗组件已在 App.vue 中静态导入，无需预加载
   */
  async preloadDialogComponents(): Promise<void> {
    // 目前所有弹窗组件都已被静态导入，暂无需预加载的组件
    console.log("弹窗组件已被静态导入，跳过预加载");
  }

  /**
   * 根据页面预加载相关组件
   */
  preloadByPage(pageName: string): void {
    const pageComponentMap: Record<string, () => void> = {
      home: () => {
        this.preloadGameComponents();
        this.preloadDialogComponents();
      },
      account: () => {
        this.preloadDialogComponents();
      },
      promos: () => {
        // 预加载活动相关组件
      },
    };

    const preloader = pageComponentMap[pageName];
    if (preloader) {
      preloader();
    }
  }

  /**
   * 获取预加载状态
   */
  getPreloadStatus(): { loaded: string[]; loading: string[] } {
    return {
      loaded: Array.from(this.preloadedComponents),
      loading: Array.from(this.preloadPromises.keys()),
    };
  }
}

// 导出单例
export const componentPreloader = new ComponentPreloader();
