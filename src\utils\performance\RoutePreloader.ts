/**
 * 路由预加载工具
 * 提前加载可能访问的路由组件，提升用户体验
 */

interface PreloadConfig {
  routes: string[];
  priority: "high" | "medium" | "low";
  delay?: number;
}

class RoutePreloader {
  private preloadedRoutes = new Set<string>();
  private preloadPromises = new Map<string, Promise<any>>();

  /**
   * 预加载指定路由
   */
  async preloadRoute(routePath: string): Promise<void> {
    if (this.preloadedRoutes.has(routePath)) {
      return;
    }

    if (this.preloadPromises.has(routePath)) {
      return this.preloadPromises.get(routePath);
    }

    const promise = this.loadRouteComponent(routePath);
    this.preloadPromises.set(routePath, promise);

    try {
      await promise;
      this.preloadedRoutes.add(routePath);
      // console.log(`✅ 路由预加载完成: ${routePath}`);
    } catch (error) {
      console.warn(`❌ 路由预加载失败: ${routePath}`, error);
    } finally {
      this.preloadPromises.delete(routePath);
    }
  }

  /**
   * 批量预加载路由
   */
  async preloadRoutes(config: PreloadConfig): Promise<void> {
    const { routes, priority, delay = 0 } = config;

    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    const promises = routes.map((route) => this.preloadRoute(route));

    if (priority === "high") {
      // 高优先级：并行加载
      await Promise.all(promises);
    } else {
      // 中低优先级：串行加载，避免阻塞
      for (const promise of promises) {
        await promise;
      }
    }
  }

  /**
   * 根据路由路径加载对应组件
   */
  private async loadRouteComponent(routePath: string): Promise<any> {
    const routeMap: Record<string, () => Promise<any>> = {
      "/home": () => import("@/views/home/<USER>"),
      "/promos": () => import("@/views/promos/index.vue"),
      "/news": () => import("@/views/news/index.vue"),
      "/account": () => import("@/views/account/index.vue"),
      "/game-categories": () => import("@/views/game-categories/index.vue"),
      "/login": () => import("@/views/login/index.vue"),
      "/game-webview": () => import("@/views/game-webview/index.vue"),
      "/promo-webview": () => import("@/views/promos/promos-detail/promo_webview.vue"),
      "/account/transactions": () => import("@/views/account/transactions/index.vue"),
      "/account/bet": () => import("@/views/account/bet/index.vue"),
    };

    const loader = routeMap[routePath];
    if (!loader) {
      throw new Error(`未找到路由: ${routePath}`);
    }

    return loader();
  }

  /**
   * 基于用户行为预测并预加载路由
   */
  predictivePreload(currentRoute: string): void {
    const predictions: Record<string, string[]> = {
      "/home": ["/game-categories", "/promos", "/account"],
      "/promos": ["/promo-webview", "/home"],
      "/news": ["/home", "/account"],
      "/account": ["/account/transactions", "/account/profile", "/account/security"],
      "/login": ["/home", "/register"],
    };

    const predictedRoutes = predictions[currentRoute];
    if (predictedRoutes) {
      this.preloadRoutes({
        routes: predictedRoutes,
        priority: "medium",
        delay: 1000, // 1秒后开始预加载
      });
    }
  }

  /**
   * 在浏览器空闲时预加载
   */
  preloadOnIdle(routes: string[]): void {
    if ("requestIdleCallback" in window) {
      requestIdleCallback(() => {
        this.preloadRoutes({
          routes,
          priority: "low",
        });
      });
    } else {
      // 降级处理
      setTimeout(() => {
        this.preloadRoutes({
          routes,
          priority: "low",
        });
      }, 2000);
    }
  }

  /**
   * 预加载核心路由（首次访问时）
   */
  async preloadCoreRoutes(): Promise<void> {
    const coreRoutes = ["/home", "/promos", "/news", "/account"];

    await this.preloadRoutes({
      routes: coreRoutes,
      priority: "high",
    });
  }

  /**
   * 获取预加载状态
   */
  getPreloadStatus(): { loaded: string[]; loading: string[] } {
    return {
      loaded: Array.from(this.preloadedRoutes),
      loading: Array.from(this.preloadPromises.keys()),
    };
  }
}

// 导出单例
export const routePreloader = new RoutePreloader();
