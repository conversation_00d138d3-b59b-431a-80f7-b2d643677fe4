# XNumberRoller 组件

## 概述

`XNumberRoller` 是一个高级的动画数字显示组件，在数字变化时提供流畅的滚动过渡效果。它支持千分位分隔符，处理翻转动画（9→0），并提供可自定义的动画持续时间。

## 文件位置

```
src/components/Single-purpose/XNumberRoller.vue
```

## 功能特性

- **流畅动画**: 带缓动效果的流畅数字滚动过渡
- **翻转支持**: 9→0 过渡的特殊处理（通过 10 滚动）
- **千分位分隔符**: 大数字的自动逗号格式化
- **可自定义持续时间**: 可配置的动画时间
- **性能优化**: 高效的 DOM 更新和 CSS 变换
- **等宽字体**: 一致的数字宽度，稳定的布局

## 使用方法

### 基础实现

```vue
<template>
  <XNumberRoller :value="currentValue" />
</template>

<script setup>
import { ref } from "vue";
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";

const currentValue = ref(1234);

// 更新值以查看动画
setTimeout(() => {
  currentValue.value = 5678;
}, 2000);
</script>
```

### 自定义持续时间

```vue
<template>
  <XNumberRoller :value="score" :duration="1200" />
</template>

<script setup>
import { ref } from "vue";
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";

const score = ref(0);

// 动画到高分
const animateScore = () => {
  score.value = 999999;
};
</script>
```

### 奖金显示中的应用

```vue
<template>
  <div class="prize-display">
    <span class="currency">₱</span>
    <XNumberRoller :value="prizeAmount" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";

const prizeAmount = ref(50000);
</script>

<style scoped>
.prize-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 18px;
  font-weight: bold;
}

.currency {
  color: #ffd700;
}
</style>
```

## 属性 (Props)

| 属性       | 类型     | 必需 | 默认值 | 描述                 |
| ---------- | -------- | ---- | ------ | -------------------- |
| `value`    | `Number` | 是   | -      | 要显示和动画到的数字 |
| `duration` | `Number` | 否   | `800`  | 动画持续时间（毫秒） |

## 事件 (Events)

该组件不发出任何事件。它是一个纯显示组件。

## 动画行为

### 标准滚动

- 数字通过中间值向上滚动
- 示例：1→5 通过 1,2,3,4,5 滚动

### 翻转动画 (9→0)

- 对递减过渡的特殊处理
- 示例：9→1 通过 9,10,0,1 滚动（显示为连续向上运动）
- 为递减数字提供视觉连续性

### 千分位分隔符

- 自动用逗号格式化数字
- 示例：1234567 显示为 1,234,567
- 逗号被视为非动画元素

## 技术实现

### 核心算法

```javascript
// 带格式化的数字提取
function getDigits(num) {
  return num
    .toLocaleString()
    .replace(/,/g, "")
    .split("")
    .map((d) => (/\d/.test(d) ? Number(d) : d));
}

// 动画样式计算
const getDigitStyle = (idx) => {
  const digit = displayDigits.value[idx];
  const needRoll = rollingIndexes.value[idx];
  const isRollOver = rollingTo9To0.value[idx];

  if (needRoll && isRollOver) {
    return {
      transform: `translateY(-${(10 + digit) * 1.2}em)`,
      transition: `transform ${props.duration}ms cubic-bezier(.23,1.02,.67,1)`,
    };
  }
  // ... 其他情况
};
```

### 状态管理

```javascript
const prevValue = ref(Number(props.value));
const displayDigits = ref([]);
const rollingIndexes = ref([]);
const rollingTo9To0 = ref([]);
```

### 监听器逻辑

```javascript
watch(
  () => Number(props.value),
  (newVal, oldVal) => {
    if (newVal == oldVal) return;
    prevValue.value = oldVal;
    updateRollingIndexes();
    animate();
  }
);
```

## 样式设计

### 容器结构

```css
.rolling-number {
  display: inline-flex;
  align-items: center;
  letter-spacing: 0.5px;
  font-size: 10px;
  font-weight: bold;
  padding: 0 8px;
}
```

### 数字容器

```css
.digit-container {
  display: inline-block;
  height: 1.2em;
  overflow: hidden;
  position: relative;
  text-align: center;
}
```

### 动画属性

- **变换**: translateY 用于垂直滚动
- **过渡**: 三次贝塞尔缓动，实现流畅运动
- **字体**: 等宽字体，确保数字宽度一致
- **高度**: 每个数字 1.2em，确保适当间距

## 性能考虑

### 优化技术

- **最少 DOM 更新**: 仅动画变化的数字
- **CSS 变换**: GPU 加速动画
- **高效监听器**: 优化的变化检测
- **内存管理**: 动画状态的正确清理

### 最佳实践

- 使用合理的动画持续时间（500-1500ms）
- 避免在动画期间快速更改值
- 考虑对频繁变化的值进行防抖处理

## 集成示例

### 实时计数器

```vue
<template>
  <div class="live-counter">
    <h3>实时奖池</h3>
    <XNumberRoller :value="liveAmount" :duration="600" />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";

const liveAmount = ref(100000);
let timer = null;

onMounted(() => {
  timer = setInterval(() => {
    liveAmount.value += Math.floor(Math.random() * 1000);
  }, 3000);
});

onBeforeUnmount(() => {
  clearInterval(timer);
});
</script>
```

### 分数动画

```vue
<template>
  <div class="score-board">
    <div class="score-item">
      <span>当前分数:</span>
      <XNumberRoller :value="currentScore" />
    </div>
    <div class="score-item">
      <span>最高分数:</span>
      <XNumberRoller :value="highScore" :duration="1200" />
    </div>
  </div>
</template>

<script setup>
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";

const currentScore = ref(0);
const highScore = ref(999999);

const updateScore = (points) => {
  currentScore.value += points;
  if (currentScore.value > highScore.value) {
    highScore.value = currentScore.value;
  }
};
</script>
```

## 浏览器兼容性

- **现代浏览器**: 完全支持动画
- **移动设备**: 针对触摸界面优化
- **旧版支持**: 无动画的优雅降级

## 故障排除

### 常见问题

1. **动画卡顿**

   - 检查 CSS 变换支持
   - 验证缓动函数兼容性
   - 确保没有冲突的 CSS 过渡

2. **数字格式不正确**

   - 验证区域设置
   - 检查数字输入有效性
   - 确保正确的类型转换

3. **性能问题**

   - 减少动画频率
   - 检查监听器中的内存泄漏
   - 优化更新间隔

4. **布局偏移**
   - 使用等宽字体
   - 设置固定容器宽度
   - 确保一致的数字间距

## 高级用法

### 自定义样式

```vue
<template>
  <XNumberRoller :value="amount" class="custom-roller" />
</template>

<style scoped>
.custom-roller {
  font-size: 24px;
  color: #00ff00;
  text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.custom-roller .digit {
  font-family: "Digital-7", monospace;
}
</style>
```

### 与状态管理集成

```vue
<script setup>
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";
import XNumberRoller from "@/components/Single-purpose/XNumberRoller.vue";

const gameStore = useGameStore();
const { totalWinnings } = storeToRefs(gameStore);
</script>

<template>
  <XNumberRoller :value="totalWinnings" :duration="1000" />
</template>
```

## 使用建议

### 最佳实践

- 在显示重要数字变化时使用（如分数、金额）
- 确保动画持续时间适中，不影响用户体验
- 在高频更新场景中考虑使用防抖

### 注意事项

- 组件专为数字显示设计，不支持文本内容
- 大数字可能影响性能，建议合理控制更新频率
- 在移动设备上测试动画性能
