/**
 * 移动端窗口管理器
 * 解决移动端浏览器 window.open() 被阻止的问题
 */

export class MobileWindowManager {
  /**
   * 检测是否为移动端设备
   */
  static isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  /**
   * 检测是否为 iOS 设备
   */
  static isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  // 判断是否为 Safari 浏览器
  static isSafari() {
    // Safari 浏览器的 userAgent 包含 "Safari"，但不包含 "Chrome" 或 "Chromium"
    const userAgent = navigator.userAgent.toLowerCase();
    return /safari/.test(userAgent) && !/chrome|chromium/.test(userAgent);
  }

  /**
   * 使用 setTimeout 绕过弹窗限制打开窗口
   * 这是最有效的方法，类似 Cocos 引擎的做法
   */
  static openWithTimeout(url: string, delay: number = 100): Promise<Window | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          const newWindow = window.open(url, "_blank");
          resolve(newWindow);
        } catch (error) {
          console.warn("setTimeout window.open failed:", error);
          resolve(null);
        }
      }, delay);
    });
  }

  /**
   * 启动外跳游戏的统一方法
   * 使用 setTimeout 策略，确保不会刷新当前页面
   */
  static async launchExternalGame(
    gameUrlProvider: () => Promise<string>,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      // 获取游戏 URL
      const gameUrl = await gameUrlProvider();

      // 使用 setTimeout 打开窗口
      const newWindow = await this.openWithTimeout(gameUrl);

      if (!newWindow) {
        throw new Error("Failed to open window. Please check if pop-ups are blocked.");
      }
    } catch (error) {
      const gameError = error instanceof Error ? error : new Error("Unknown error");
      console.error("External launch failed:", gameError);

      if (onError) {
        onError(gameError);
      }
    }
  }
}

// 导出便捷方法
export const isMobile = MobileWindowManager.isMobile;
export const isSafari = MobileWindowManager.isSafari;
export const openWithTimeout = MobileWindowManager.openWithTimeout;
export const launchExternalGame = MobileWindowManager.launchExternalGame;
