// ===== TYPES =====

export interface BetItem {
  id: string | number;
  game_id: string | number;
  game_name: string;
  provider_name: string;
  win_lose: number;
  bet: number;
  effective_bet?: number;
  payouts?: number;
  house_rake?: number;
  total_refund?: number;
  third_create_time: string;
  third_updated_time?: string;
  index_time?: string;
  status?: string | number;
  game_type?: string;
  images?: string;
  [key: string]: any;
}

export interface BetOrder {
  count: number;
  total_page: number;
  current_page: number;
  list: Record<string, BetItem[]>;
}

export interface FilterProvider {
  id: string | number;
  provider: string;
}

export interface DateOption {
  name: string;
  value: number;
}

export interface GameInfo {
  id: string | number;
  images?: string;
  [key: string]: any;
}

// Component props interfaces
export interface BetFiltersProps {
  activeBtn: DateOption;
  filterProvider: FilterProvider[];
  dateOptions: DateOption[];
}

export interface BetTabsProps {
  betOrders: Record<string, BetOrder>;
  activeTab: string;
  loading: boolean;
  gamesList: Record<string | number, GameInfo>;
  tabTitles: Record<string, string>;
}

export interface BetListItemProps {
  dateKey: string;
  items: BetItem[];
  tabType: string;
  gamesList: Record<string | number, GameInfo>;
}

// ===== CONSTANTS =====

// Tab configuration - consolidated mapping
export const TAB_TITLES = {
  3: "Settled",
  1: "Unsettled",
  2: "Cancel",
  promos: "Promos",
  Settled: "3",
  Unsettled: "1",
  Cancel: "2",
  Promos: "promos",
} as const;

// Date filter options
export const DATE_OPTIONS: DateOption[] = [
  { name: "Today", value: 1 },
  { name: "Yesterday", value: 2 },
  { name: "Last 3 days", value: 3 },
  { name: "Last 7 days", value: 7 },
];

// Color constants
export const TAB_COLORS = {
  SETTLED: "#FF5353",
  UNSETTLED: "#999999",
  CANCEL: "#303030",
  PROMOS: "#2FDA88",
} as const;

// Style configuration for different tab types
export const TAB_STYLES = {
  Settled: { color: TAB_COLORS.SETTLED, "font-weight": 600 },
  Unsettled: { color: TAB_COLORS.UNSETTLED, "font-weight": 600 },
  Cancel: { color: TAB_COLORS.CANCEL, "font-weight": 600 },
  Promos: { color: TAB_COLORS.PROMOS, "font-weight": 600 },
} as const;

// Tab names for consistent reference
export const TAB_NAMES = ["Settled", "Unsettled", "Cancel", "Promos"] as const;
export type TabName = (typeof TAB_NAMES)[number];

// Helper function to create default bet order
const createDefaultBetOrder = (): BetOrder => ({
  count: 0,
  list: {},
  total_page: 0,
  current_page: 0,
});

// Default bet orders structure
export const DEFAULT_BET_ORDERS = Object.fromEntries(
  TAB_NAMES.map((tab) => [tab, createDefaultBetOrder()])
) as Record<TabName, BetOrder>;

// Default current page structure
export const DEFAULT_CURRENT_PAGE = Object.fromEntries(TAB_NAMES.map((tab) => [tab, 1])) as Record<
  TabName,
  number
>;

// API configuration
export const API_CONFIG = {
  DEFAULT_PAGE_SIZE: 15,
  DEFAULT_PAGE: 1,
} as const;
