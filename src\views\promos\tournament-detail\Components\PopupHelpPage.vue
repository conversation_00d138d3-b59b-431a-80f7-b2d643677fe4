<!-- 锦标赛详情弹框 -->
<template>
  <ZPopOverlay :show="visible" @click="handleClose">
    <div class="img-list-wrapper">
      <ZLoading v-if="loading" />
      <img @load="loading = false" v-for="url in props.imgList" :key="url" :src="url" />
    </div>
  </ZPopOverlay>
</template>
<script setup lang="ts">
// 定义组件名称
defineOptions({ name: "PopupHelp" });

import { ref } from "vue";
import { getServerSideImageUrl } from "@/utils/core/tools";

const visible = defineModel<boolean>("visible");
const props = defineProps<{
  imgList?: Array<string>;
}>();

const loading = ref(true);

const emits = defineEmits(["update:visible"]);
const handleClose = () => {
  emits("update:visible", false);
};
</script>
<style lang="scss" scoped>
.img-list-wrapper {
  max-height: 80vh;
  border-radius: 16px;
  overflow-y: overlay;
  overflow-x: hidden;

  img {
    width: 100%;
  }
}
</style>
