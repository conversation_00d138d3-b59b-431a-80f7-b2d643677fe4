<template>
  <div class="turnstile-test-page">
    <div class="header">
      <h1>Cloudflare Turnstile Test Page</h1>
      <p>Test the Turnstile component with different configurations</p>
    </div>

    <div class="test-sections">
      <!-- Basic Form Test -->
      <div class="test-section">
        <h2>Basic Form with Turnstile</h2>
        <form @submit.prevent="handleFormSubmit" class="test-form">
          <div class="form-group">
            <label for="email">Email:</label>
            <input
              id="email"
              v-model="formData.email"
              type="email"
              placeholder="Enter your email"
              required
            />
          </div>

          <div class="form-group">
            <label>Security Verification:</label>
            <TurnstileWidget
              :site-key="siteKey"
              :theme="theme"
              :size="size"
              :appearance="appearance"
              @success="handleTurnstileSuccess"
              @error="handleTurnstileError"
              @expired="handleTurnstileExpired"
            />
          </div>

          <button type="submit" :disabled="!isVerified || isSubmitting" class="submit-btn">
            {{ isSubmitting ? "Submitting..." : "Submit Form" }}
          </button>
        </form>
      </div>

      <!-- Configuration Test -->
      <div class="test-section">
        <h2>Configuration Options</h2>
        <div class="config-controls">
          <div class="control-group">
            <label>Theme:</label>
            <select v-model="theme">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto</option>
            </select>
          </div>

          <div class="control-group">
            <label>Size:</label>
            <select v-model="size">
              <option value="normal">Normal</option>
              <option value="compact">Compact</option>
            </select>
          </div>

          <div class="control-group">
            <label>Appearance:</label>
            <select v-model="appearance">
              <option value="always">Always</option>
              <option value="execute">Execute</option>
              <option value="interaction-only">Interaction Only</option>
            </select>
          </div>

          <button @click="resetTurnstile" class="reset-btn">Reset Turnstile</button>

          <button @click="testSiteKeyValidation" class="test-btn">Test Site Key Validation</button>

          <button @click="testVerificationCompatibility" class="test-btn">
            Test Verification Compatibility
          </button>

          <button @click="testLoginVerification" class="test-btn">Test Login Verification</button>
        </div>
      </div>

      <!-- Status Display -->
      <div class="test-section">
        <h2>Verification Status</h2>
        <div class="status-display">
          <div class="status-item">
            <strong>Verified:</strong>
            <span :class="{ success: isVerified, error: !isVerified }">
              {{ isVerified ? "Yes" : "No" }}
            </span>
          </div>
          <div class="status-item" v-if="verificationToken">
            <strong>Token:</strong>
            <code class="token">{{ verificationToken.substring(0, 20) }}...</code>
          </div>
          <div class="status-item" v-if="lastError">
            <strong>Last Error:</strong>
            <span class="error">{{ lastError }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import TurnstileWidget from "./TurnstileWidget.vue";
import { CloudflareMgr } from "@/utils/CloudflareMgr";
import { VerificationMgr } from "@/utils/VerificationMgr";
import { useLoginStore } from "@/stores/login";

// Form data
const formData = ref({
  email: "",
});

// Turnstile configuration
const siteKey = ref("1x00000000000000000000AA"); // Test site key (always passes)
const theme = ref<"light" | "dark" | "auto">("auto");
const size = ref<"normal" | "compact">("normal");
const appearance = ref<"always" | "execute" | "interaction-only">("always");

// Verification state
const isVerified = ref(false);
const verificationToken = ref("");
const lastError = ref("");
const isSubmitting = ref(false);

// Turnstile widget reference
const turnstileRef = ref<InstanceType<typeof TurnstileWidget>>();

// Handle Turnstile events
const handleTurnstileSuccess = (token: string) => {
  console.log("✅ Turnstile verification successful:", token);
  isVerified.value = true;
  verificationToken.value = token;
  lastError.value = "";
};

const handleTurnstileError = (error: string) => {
  console.error("❌ Turnstile verification failed:", error);
  isVerified.value = false;
  verificationToken.value = "";
  lastError.value = error;
};

const handleTurnstileExpired = () => {
  console.warn("⚠️ Turnstile verification expired");
  isVerified.value = false;
  verificationToken.value = "";
  lastError.value = "Verification expired";
};

// Handle form submission
const handleFormSubmit = async () => {
  if (!isVerified.value) {
    alert("Please complete the security verification first");
    return;
  }

  isSubmitting.value = true;

  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    alert(
      `Form submitted successfully!\nEmail: ${
        formData.value.email
      }\nToken: ${verificationToken.value.substring(0, 20)}...`
    );

    // Reset form
    formData.value.email = "";
    resetVerification();
  } catch (error) {
    console.error("Form submission failed:", error);
    alert("Form submission failed. Please try again.");
  } finally {
    isSubmitting.value = false;
  }
};

// Reset Turnstile
const resetTurnstile = () => {
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
  resetVerification();
};

// Reset verification state
const resetVerification = () => {
  isVerified.value = false;
  verificationToken.value = "";
  lastError.value = "";
};

// Test site key validation
const testSiteKeyValidation = () => {
  console.log("🧪 Running Site Key validation test...");
  CloudflareMgr.instance.testSiteKeyValidation();
};

// Test verification compatibility
const testVerificationCompatibility = async () => {
  console.log("🧪 Running Verification compatibility test...");
  await VerificationMgr.instance.testVerificationCompatibility();
};

// Test login verification
const testLoginVerification = async () => {
  console.log("🧪 Testing login verification method...");
  const loginStore = useLoginStore();

  try {
    await loginStore.handleVerification("phone_login_code", async (result: any) => {
      console.log("✅ Login verification successful:", result);

      // 显示验证结果
      if (result["cf-token"] || result.cf_token) {
        console.log("🔐 Cloudflare verification detected");
        console.log("Token:", result["cf-token"] || result.cf_token);
        console.log("Scene:", result["cf-scene"] || result.cf_type);
      } else if (result.geetest_guard) {
        console.log("🔐 Geetest verification detected");
        console.log("Guard:", result.geetest_guard);
        console.log("Captcha:", result.geetest_captcha);
      }
    });
  } catch (error) {
    console.error("❌ Login verification test failed:", error);
  }
};
</script>

<style scoped>
.turnstile-test-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.test-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #495057;
  font-size: 18px;
}

.test-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #495057;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.submit-btn {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.config-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  font-weight: 600;
  color: #495057;
}

.control-group select {
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.reset-btn {
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background: #545b62;
}

.test-btn {
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #1e7e34;
}

.status-display {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-item strong {
  min-width: 100px;
  color: #495057;
}

.success {
  color: #28a745;
  font-weight: 600;
}

.error {
  color: #dc3545;
  font-weight: 600;
}

.token {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 12px;
}

@media (max-width: 768px) {
  .turnstile-test-page {
    padding: 15px;
  }

  .config-controls {
    grid-template-columns: 1fr;
  }
}
</style>
