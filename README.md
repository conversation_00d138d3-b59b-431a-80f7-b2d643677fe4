### Dependency Installation

```sh
pnpm install
```

### Local Development

# Start development server

pnpm run start:dev

# Start production environment locally

pnpm run start:prod

### Build for Specific Environments

```sh
# Build for development environment, output to dist
pnpm run build:dev

# Build for production environment, output to dist
pnpm run build:prod

# Build for all environments, output to distAll/${env}
pnpm run build
```

### UI Library

[vant4](https://vant-ui.github.io/vant/#/en-US/button)

### 字体使用指南

项目内置了 D-DIN、DINPro 和 Inter 三套字体，支持多种使用方式：

#### 1. 通过 CSS 类使用（推荐）

```vue
<template>
  <!-- 使用 D-DIN 字体 -->
  <h1 class="font-din font-bold">游戏标题</h1>

  <!-- 使用 Inter 字体 -->
  <p class="font-inter font-normal">描述文本</p>

  <!-- 使用 DINPro 字体 -->
  <div class="font-dinpro font-medium">金额显示</div>
</template>
```

#### 2. 通过 CSS 变量使用

```scss
.custom-title {
  font-family: var(--font-family-display); // D-DIN
}

.custom-text {
  font-family: var(--font-family-primary); // Inter
}

.custom-number {
  font-family: var(--font-family-mono); // DINPro
}
```

#### 3. 直接指定字体

```scss
.special-text {
  font-family: "D-DIN";
  // font-family: "Inter";
  font-weight: 700;
}
```

#### 可用的字体工具类

**字体系列：**

- `.font-inter` - Inter 字体
- `.font-din` - D-DIN 字体
- `.font-dinpro` - DINPro 字体

**字重类：**

- `.font-thin` (100)
- `.font-extralight` (200)
- `.font-light` (300)
- `.font-normal` (400)
- `.font-medium` (500)
- `.font-semibold` (600)
- `.font-bold` (700)
- `.font-extrabold` (800)
- `.font-black` (900)

#### 字体预加载

项目在启动时会自动预加载字体：

- **高优先级字体**：启动时立即加载（D-DIN Regular/Bold, Inter Regular/Medium）
- **中优先级字体**：启动后台加载（DINPro, Inter SemiBold/Bold）
- **低优先级字体**：按需后台加载（其他字重）

#### 字体文件位置

```
src/assets/fonts/
├── D-DIN/
│   ├── D-DIN.ttf
│   ├── D-DIN-Bold.ttf
│   ├── DINPro-Light.ttf
│   └── DINPro-Medium.ttf
└── Inter/
    ├── Inter-Regular-9.otf
    ├── Inter-Medium-8.otf
    ├── Inter-SemiBold-10.otf
    ├── Inter-Bold-4.otf
    └── ...
```

### 注意事项

1.  每次版本发布，记得修改 `/utils/Config.ts` 中的 `app_version` 字段
