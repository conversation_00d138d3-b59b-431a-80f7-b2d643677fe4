import { ref, onUnmounted } from "vue";
import { refreshToken } from "@/api/user";
import { useGlobalStore } from "@/stores/global";

// 全局状态管理
export class MayaTokenManager {
  private static instance: MayaTokenManager;
  private nextReqTime: number = 0;
  private timer: NodeJS.Timeout | null = null;
  private token = ref<string>("");

  static getInstance(): MayaTokenManager {
    if (!this.instance) {
      this.instance = new MayaTokenManager();
    }
    return this.instance;
  }

  /**
   * 设置Maya token刷新定时任务
   */
  startTokenRefreshSchedule(): void {
    this.stopTokenRefreshSchedule();
    // 设置下次请求时间为6分钟后
    this.nextReqTime = Date.now() + 6 * 60 * 1000;

    // 如果是Maya渠道，启动定时器
    if (this.isMayaChannel()) {
      // 2分钟后开始，每2分钟执行一次
      this.timer = setInterval(() => {
        this.refreshMayaToken();
      }, 2 * 60 * 1000);

      // 2分钟后执行第一次
      setTimeout(() => {
        this.refreshMayaToken();
      }, 2 * 60 * 1000);
    }
  }

  /**
   * 刷新Maya token
   */
  private async refreshMayaToken(): Promise<void> {
    if (this.nextReqTime < Date.now()) {
      try {
        const response = await refreshToken();

        if (response?.expire) {
          const expire = response.expire;
          if (typeof expire === "number") {
            this.nextReqTime = expire * 1000 + Date.now();
          } else if (typeof expire === "string") {
            this.nextReqTime = Number(expire) * 1000 + Date.now();
          }
        } else {
          // 如果没有返回expire，延后7分钟再试
          this.nextReqTime = this.nextReqTime + 7 * 60 * 1000;
        }
      } catch (error) {
        console.warn("Maya token refresh failed:", error);
        // 可以选择重新加载页面
        // window.location.reload()
      }
    }
  }

  /**
   * 停止token刷新定时任务
   */
  stopTokenRefreshSchedule(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.nextReqTime = 0;
  }

  /**
   * 设置token
   */
  setToken(token: string): void {
    this.token.value = token;
  }

  /**
   * 获取token
   */
  getToken(): string {
    return this.token.value;
  }

  private isMayaChannel(): boolean {
    return useGlobalStore().isMaya;
  }
}
