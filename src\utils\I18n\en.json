{"sendCodeMsgTip1": "The Number does not exist.", "sendCodeMsgTip2": "The format of Number is incorrect.", "sendCodeMsgTip3": "The Number has been blocked.", "sendCodeMsgTip4": "You need to bind your Number first.", "sendCodeMsgTip5": "The Number has already been bound.", "sendCodeMsgTip6": "SMS sending failed.", "sendCodeMsgTip7": "Verification code sent successfully.", "loginword1": "By Logging in You Accept That You Are", "loginword2": "and Agree to Our", "loginword3": "*T&C", "loginword4": "and", "loginword5": "*Privacy Policy", "loginword6": "Loading...Please Wait.", "loginword7": "Updating...Please Wait...", "loginword8": "Login Game, Please Wait...", "loginword9": "Login Failed", "loginword10": "<PERSON><PERSON> Canceled", "loginword11": "Loading Failed, Please Try Again.", "loginword12": "Could not connect to network, please try again", "loginword13": "Downloading...", "loginword14": "Update Failed, Please Try Again.", "loginword15": "The Server is Under Maintenance, Please Wait.", "loginword16": "Please Enter the Correct Account or Password.", "loginword17": "18+", "loginword18": "Online Players", "logintip1": "Play", "logintip2": "anytime", "logintip3": "&", "logintip4": "anywhere!", "logintip5": "Socialize", "logintip6": "while", "logintip7": "strategize!", "logintip8": "Trusted", "logintip9": "& enjoyed", "logintip10": "across", "logintip11": "India!", "logintip12": "Join to get up to ", "logintip13": "bonus", "logintip14": "<PERSON><PERSON>", "logintip15": "Or", "weekendword1": "After Adding Cash, You Have 3 Days to Play & Release the Bonus.", "workdayword1": "Take Each Deal to Further Reveal.", "workdayword2": "Bonus Validity：", "workdayword3": "Day", "workdayword4": "Daily Free Spin", "workdayword5": "Lucky Wheel has Refreshed", "bankbindword1": "Account Type:", "bankbindword2": "Name:", "bankbindword3": "Enter Your Name", "bankbindword4": "Email:", "bankbindword5": "Enter Your Email", "bankbindword6": "Address:", "bankbindword7": "Enter Your Address", "bankbindword8": "Bank Account:", "bankbindword9": "Bank Account", "bankbindword10": "Bank Card", "bankbindword11": "PAN NO.:", "bankbindword12": "Enter Your PAN NO.", "bankbindword13": "UPI", "bankbindword14": "UPI ID:", "bankbindword15": "Enter Your UPI ID", "bankbindword16": "IFSC Code:", "bankbindword17": "Enter Your IFSC Code", "mailword1": "Mails", "mailword2": "Suggestions", "mailword3": "Empty...", "mailword4": "Enter Your Suggestions...", "mailword5": "Collect", "mailword6": "Send", "phonebindword1": "Phone Number:", "phonebindword2": "Enter Your Phone Number", "phonebindword3": "Password:", "phonebindword4": "Enter Your Password", "phonebindword5": "OTP:", "phonebindword6": "Enter the code", "phonebindword7": "Register For Free", "phonebindword8": "Change Password", "phonebindword9": "Change", "phonebindword10": "OTP", "phonebindword11": "6-Digit Verification Code:", "gamelist1": "3Patti", "gamelist2": "Rummy", "gamelist3": "Poker", "gamelist4": "Joker", "gamelist5": "AK47", "gamelist7": "Tongits", "gamelist13": "Carrom", "gamelist14": "Disc Pool", "gamelist15": "Freestyle", "gamelist18": "Science", "gamelist19": "Culture", "gamelist20": "Random", "gamelist25": "Paulista", "gamelist27": "<PERSON><PERSON><PERSON>", "roomlistword1": "Cash", "roomlistword2": "Practice", "roomlistword3": "Name", "roomlistword4": "Max Players", "roomlistword5": "Boot Amount", "roomlistword6": "Min Entry", "roomlistword7": "Online", "roomlistword8": "<PERSON><PERSON>", "roomlistword9": "WinBet", "joinroom_text_1": "PLAY WITH FRIENDS", "joinroom_text_2": "JOIN THE TABLE", "joinroom_text_3": "Reset", "joinroom_text_4": "CREATE TABLE", "joinroom_text_5": "Invalid Table Number", "joinroom_text_6": "The Table is full.", "joinroom_text_7": "Error!You already in xx room of yy Peso", "createroom_text_1": "CREATE", "createroom_text_2": "Game Mode", "createroom_text_3": "Number of Players", "createroom_text_4": "Style", "createroom_text_5": "Bets", "createroom_text_6": "Paulista", "createroom_text_7": "Miner", "createroom_text_8": "Dirty Deck", "createroom_text_9": "Clean Deck", "createroom_text_10": "Minimum entry:", "createroom_text_11": "Files", "invitation_text_1": "I'm playing Truco, Table Number:", "invitation_text_2": "come win money with me! ! ", "invitation_text_3": "Copy the following and open it in your browser:\n", "invitation_text_4": "You are accepting a friend's invitation", "invitation_text_5": "Table Number: ", "invitation_text_6": "Join Now", "invitation_text_105001": "nvitation code error", "invitation_text_105002": "You have bound the invited user", "invitation_text_105003": "invitation code error", "invitation_text_105017": "The id of the inviter must be smaller than that of the invitee", "invitation_text_105018": "The device id cannot be the same", "invitation_text_105019": "You have exceeded the binding time", "invitation_text_105020": "Invitee must be online", "serviceword1": "If You Have Any Questions, Feel Free to Contact Us Every Day Between 8:00 am and 9:00 pm, Excluding National Holidays. Please Provide Your Game ID and Necessary Screenshots So We Can Better Assist You!", "settingword1": "Music", "settingword2": "Sound", "settingword3": "Vibration", "settingword4": "Language", "settingword5": "Phone", "settingword6": "Google", "settingword7": "Version", "settingword8": "Bound", "settingword9": "Bind", "shareword1": "Share this link With Your Firends.", "shopword1": "Cashback", "shopword2": "Bonus", "shopword3": "Total", "shopword4": "You Will Find Your Bonus in Your Bonus Account. After Every Gameplay, 10% of Your Loss (Max. One Boot Amount) Will be Covered by the Bonus in your Bonus Account.", "shopword5": "You will find your instant cashback in the in-app mail and bonus in your bonus account after every successful deposit. ", "shopword6": "10% of your loss  (max. one boot amount) will be covered by the bonus in your bonus account, added directly to your \"Cash Balance\" after every round of game.", "shopword7": "Recharge Tips:", "withdrawword1": "Withdraw", "withdrawword2": "Balance", "withdrawword3": "Details", "withdrawword4": "Bonus", "withdrawword5": "Withdrawable Balance", "withdrawword6": "Before Withdrawing From Your Game Account, Please Make Sure That Your Information is Entered Correctly.", "withdrawword7": "ACCOUNT", "withdrawword8": "ID", "withdrawword9": "Time", "withdrawword10": "Amount", "withdrawword11": "Status", "withdrawword12": "Balance in Your Cash Account", "withdrawword13": "Balance in Your Cash Withdrawable Account, This Includes All Your Winning Amount", "withdrawword14": "Balance You Can Use to Play Games", "withdrawword15": "Transaction Details", "withdrawword16": "Date", "withdrawword17": "Type", "withdrawword18": "Transactions", "withdrawword19": "Avaiable Bonus:", "withdrawword20": "You Don't Have Any Bonuses. Add Cash Now to Grab Your Bonus.", "withdrawword21": "Cash Balance", "withdrawword22": "Playable Balance", "withdrawword23": "Game", "withdrawword24": "Recharge", "withdrawword25": "Withdraw Refund", "withdrawword26": "Gift", "withdrawword27": "Transfer", "withdrawword28": "Send Mail", "withdrawword29": "Activity", "withdrawtipword0": "<PERSON><PERSON><PERSON> Tips:", "withdrawtipword1": "Please Check Your CPF:", "withdrawtipword2": "1.The minimum withdrawal amount is R$.100 and it might take up to 2~24 hours to arrive at your personal account.", "withdrawtipword3": "2.If your withdrawal request was failed, the money will be refunded to you via in-app mail, please check your bank informations carefully and submit your request again.", "withdrawtipword4": "3.If you encountered any more issues, please contact our customer service and send us your Game ID(not your nickname) & screenshots of your problems so we can better assist you.", "withdrawtipword5": "Please contact customer service whenever you encounter any withdrawal issue.", "withdrawtipword6": "1.Request at any time, and the payment will be processed in 24 hours between 9:00 to 18:00 on weekdays.", "withdrawtipword7": "2.The minimum amount should be higher than %d and be a multiple of %d.", "withdrawtipword8": "4.You can withdraw up to %d per transaction for no more than %d times a day.", "withdrawtipword9": "3.Feel free to contact our customer service anytime!", "withdrawtipword10": "<PERSON><PERSON><PERSON> Tips:", "withdrawtipword11": "How Bonus Works？", "phoneloginword1": "Register Or Login With Verification Code", "phoneloginword2": "Forgot Password?", "phoneloginword3": "Remember\nPassword", "phoneloginword4": "Login With Password", "phonezhuceword1": "Nickname:", "phonezhuceword2": "Enter Your Nickname", "ClassicLayword1": "Boot Amount:", "ClassicLayword2": "Max Blinds:", "ClassicLayword3": "Chaal Limit:", "ClassicLayword4": "Pot Limit:", "helpword1": "Trail or set (Three Cards of the Same Rank)", "helpword2": "Highest", "helpword3": "Lowest", "helpword4": "Pure Sequence (Cards of the Same Suit)", "helpword5": "Sequence (Cards of Different Suit)", "helpword6": "Color", "helpword7": "Pair (Two Cards of the Same Rank)", "helpword8": "High Card", "tipword1": "Payment Successful", "tipword2": "Payment Failed", "tipword3": "Submitted Successfully", "tipword4": "Please Enter a Correct Email Address.", "tipword5": "Please Enter a Nickname.", "tipword6": "Please Enter Your Email.", "tipword7": "Please Enter Your Address.", "tipword8": "Please Enter Your PAN NO.", "tipword9": "Please Enter Your IFSC Code.", "tipword10": "Please Enter Your UPI ID.", "tipword11": "Enter Your Bank Account", "tipword12": "To Proceeed, You Need to Bind a Mobile Phone Number.", "tipword13": "The Mobile Phone Number You Bound is:\n", "tipword14": "Binding Succeeded", "tipword15": "Binding Failed", "tipword16": "Not Enough Gold!", "tipword17": "Dear Customer, to Secure Your Account Safety, Please Link Your Game Account to a Mobile Phone Number. Happy Gaming and Many Wins!", "tipword18": "Coming soon...", "tipword19": "This Number Has Already Been Linked to an Existing Account, Please Bind with Another Number.", "tipword20": "Please Enter the Correct Password.", "tipword21": "<PERSON><PERSON>r, Please Enter the Correct OTP.", "tipword22": "Please Enter Your Phone Number.", "tipword23": "The Number is Linked to an Existing Account.", "tipword24": "Failed to <PERSON> Opt.", "tipword25": "Please Enter a Password.", "tipword26": "Please Enter the code.", "tipword27": "Nickname Modified Successfully!", "tipword28": "Avatar Modified Successfully!", "tipword29": "Modification Failed", "tipword30": "You Have Successfully Bound your Facebook account.", "tipword31": "Copy Succeeded", "tipword32": "Unable to Share, Please Install WhatsApp and Try Again.", "tipword33": "You Can Deposit Minimally @#0 in a Single Transaction.", "tipword34": "You Can Deposit up to @100000 in a Single Transaction.", "tipword35": "Successful <PERSON><PERSON><PERSON>", "tipword36": "<PERSON><PERSON><PERSON> Failed. Please try again later.", "tipword37": "Minimun amount should be higher than ", "tipword38": "You are Withdrawing ₹ ", "tipword39": ",This Action Will Result in a 4% Transfer Fee.The Actual Amount Arrived At Your Account Will be ₹ ", "tipword40": ".(Please Make Sure You Have Entered the Correct Bank Account to Ensure Successful Withdrawl.)", "tipword41": "Please Enter the Correct Phone Number.", "tipword42": "Please Get the OTP First.", "tipword43": "Invalid OTP", "tipword44": "Please Enter a Valid Password.", "tipword45": "You are Logged in on Another Device.", "tipword46": "Your Access is restricted, Please Contact Customer Service.", "tipword47": "Incorrect Password", "tipword48": "The Cell Phone Number is Inconsistent With the OTP.", "tipword49": "Please Enter the Correct Mobile Phone Number.", "tipword50": "The Number Has Already Been Used.", "tipword51": "Your account has been logged in another place. Please report to customer service if it is not your action.", "tipword52": "You are Disconnected, Please Re-login.", "tipword53": "Insufficient Chips", "tipword54": "Insufficient Balance", "tipword55": "Please Wait for Your Turn", "tipword56": "Starting the Next Round in ", "tipword57": "Matching Players, Please Wait", "tipword58": "You Have Left the Table Due to Inactivity, Please <PERSON><PERSON> to Join the Table.", "tipword59": "Failed to Send.", "tipword60": "Interval of 3 seconds", "tipword61": "Error, Please Try Again Later.", "tipword62": "No Suitable Room Available!", "tipword63": "The Phone Number is not Registered.", "tipword64": "Confirm to Use Jokers/Wild Cards.", "tipword65": "Waiting for Others to Declare...", "tipword66": "Group Your Cards in Sets/Sequences and Declare!", "tipword67": "Failed to Finish.", "tipword68": "<PERSON> Will <PERSON>e ", "tipword69": " Chips If You Exit Now.", "tipword70": "Count <PERSON>.", "tipword71": "In an effort to prevent cheating and fraudulent behaviors, new players who are waiting in the room cannot see the avatars and the nicknames of other players until the game starts. ", "tipword72": "Stand by. A min balance of %d is required to bet! ", "tipword73": "Game in Betting %d Second(s)", "tipword74": "Starting Game in %d Second(s)", "tipword75": "Game in billing %d Second(s)", "tipword76": "Betting limit reached!", "tipword77": "You will lose your bet if you exit now, confirm to exit?", "tipword78": "Waiting for next round in %d Second(s)", "tipword79": "The red disk must be covered", "tipword80": "Pot your pucks to win the game", "tipword81": "MATCH POINT", "tipword82": "Foul: The striker must not be potted", "tipword83": "You'll Lose If You Exit Now!", "tipword84": "Your Bet Has Been Made. Please Wait Until the Betting Ends to Prevent Losses.", "tipword85": "Betting Limit Reached in This Area!", "tipword86": "The Next Round Will Start in #0 Second(s)", "tipword87": "You Logged in via Mobile Phone, Would You Like to Switch to Facebook Login?", "tipword88": "Your Might Not Be Able to Save Your Current Information If You Log in via Facebook. Continue to Log In?", "tipword89": "Payment Processing...", "tipword90": "Payment Successful!", "tipword91": "Payment Error, Please Try Again.", "tipword92": "Telefone format error", "tipword93": "Email format error", "tipword94": "Dear player, your coins have exceeded the current room limit. Go to higher table to win more Golds!", "tipword95": "Please Enter Your User Name.", "tipword96": "Please Enter at least two characters", "tipword97": "Only letters and numbers are supported", "tipword98": "The current network is abnormal, please try again later", "tipword99": "You need to set up a withdrawal account first.", "tipword100": "There is currently no deposit channel available, please contact customer service", "tipword101": "There is currently no withdrawal channel available, please contact customer service", "tipword102": "<PERSON> cannot withdraw, please switch to other withdrawal methods.", "tipword103": "GCash cannot withdraw, please switch to other withdrawal methods.", "tipword104": "The third game is maintaining", "tipword105": "Unable to add account at this time, please contact customer service", "tipword106": "KYC Verification Certification Passed", "scriptword1": "Time Left: ", "scriptword2": "Time Left: end", "scriptword3": "Left ", "scriptword4": "Win", "scriptword5": "Lost", "scriptword6": "<PERSON><PERSON><PERSON><PERSON>", "scriptword7": "Registration", "scriptword8": "Released", "scriptword9": "Mail", "scriptword10": "Gift Package", "scriptword11": "Expired", "scriptword12": "System Gift", "scriptword13": "Succeded", "scriptword14": "In Review", "scriptword15": "Failed", "scriptword16": "Seen", "scriptword17": "Pack", "scriptword18": "Lose", "scriptword19": "Blind", "scriptword20": "Blindx2", "scriptword21": "<PERSON><PERSON>", "scriptword22": "Chaalx2", "scriptword23": "Side Show", "scriptword24": "Show", "scriptword25": " Ask for a Side Show", "scriptword26": "Boot Amount: ", "scriptword27": " Has Denied the Side Show!", "scriptword28": "Pending", "scriptword29": "Score:", "scriptword30": "Point Value ", "scriptword31": "Invalid", "scriptword32": "Pure Sequence", "scriptword33": "Impure Sequence", "scriptword34": "Set", "scriptword35": "Lobby", "scriptword36": "Switch Table", "scriptword37": "See", "roomword1": "Available Bonus", "code_204": "The Operation Cannot Be Performed at This Point", "code_206": "The Action Cannot be Performed at This Point", "php_code_400": "The Token Has Expired, Please Login Again.", "php_code_500": "Please refresh or try again shortly（500）", "php_code_600": "Your phone number has been registered.", "php_code_700": "The Facebook Account Has <PERSON> Used.", "php_code_101001": "The Cell Phone Number Cannot be Empty.", "php_code_101002": "The Device ID Cannot be Empty.", "php_code_101003": "The Verification Code Cannot be Empty.", "php_code_101004": "Your Verification Code Has <PERSON>, Please Check.", "php_code_101005": "Failed to Send the Verification Code.", "php_code_102001": "Please Enter Your Password.", "php_code_102002": "Wrong Format. Please Enter 8-15 Digits Numbers or Letters.", "php_code_102003": "Security verification error", "php_code_102004": "Incorrect Account or Password.", "php_code_102005": "The Phone Number Has Been registered.", "php_code_102006": "You have registered too many accounts. Please contact customer service.", "php_code_102007": "Incorrect Verification Code.", "php_code_102008": "The Device ID Cannot be Empty.", "php_code_102009": "AppPackageName Cannot be Empty.", "php_code_102010": "AppVersion Cannot be Empty.", "php_code_102011": "You Are Only Allowed to Change Your Nickname Once a Month.", "php_code_102012": "Facebook Login Failed.", "php_code_102013": "The current mobile phone number already exists", "php_code_102014": "You Can Only Link One Facebook Account.", "php_code_102015": "Login Failed, Please Contact Customer Service.", "php_code_102016": "The Inviter Does Not Exist.", "php_code_102017": "The Inviter Does Not Exist.", "php_code_102018": "The Inviter Does Not Exist.", "php_code_102019": "Incorrect Account or Codes", "php_code_102020": "Please register an account first", "php_code_102022": "Invalid access token.Please login again.", "php_code_102023": "Incorrect Codes", "php_code_102024": "You have registered too many accounts. Please contact customer service.", "php_code_102025": "Up to 5 accounts can be added", "php_code_102026": "The account has been bound to other users.", "php_code_102027": "Failed to set withdrawal password", "php_code_102028": "<PERSON><PERSON>wal password verification failed", "php_code_102029": "The account has been added to other player account. Please change another one.", "php_code_102030": "System error", "php_code_102031": "Your phone number has been registered", "php_code_102032": "Update failed", "php_code_102033": "Account cannot be changed", "php_code_102034": "Your account balance is not enough for the redemption request. Please select a lower amount or buy coins.", "php_code_102035": "Sorry, You can only redeem twice a day!", "php_code_102036": "Product not found", "php_code_102037": "Deduction failed", "php_code_102038": "Operation is too frequent!", "php_code_102039": "pay_code does not exist", "php_code_102040": "SMS sending failed", "php_code_102041": "This function is not available for this account", "php_code_102042": "You have logged in too many accounts. Please contact customer service.", "php_code_102043": "You are not allowed to access PlayZone if you are not yet 21 years old.", "php_code_102044": "Your account is restricted from logging in, please contact customer service.", "php_code_102045": "You have logged in too many accounts. Please contact customer service.", "php_code_103001": "The Email Cannot be Empty.", "php_code_103002": "The IFSC Code Cannot be Empty.", "php_code_103003": "The Bank Account Cannot be Empty.", "php_code_103004": "The Name Cannot be Empty.", "php_code_103005": "The PAN Card Cannot be Empty.", "php_code_103006": "The Cell Phone Number Cannot be Empty.", "php_code_103007": "Wrong Account <PERSON><PERSON>, Please Confirm and Re-enter.", "php_code_103008": "This PAN Card Has Been Used By Another Account.", "php_code_103009": "This Bank Account Has Already Been Used, Please Check Again.", "php_code_103010": "The AppPackageName Cannot be Empty.", "php_code_103011": "The AppVersion Cannot be Empty.", "php_code_103012": "Incorrect Recharge Amount, Please Check.", "php_code_103013": "<PERSON> may have problems, Please Try Again.", "php_code_103014": "System Error, Please Try Again.", "php_code_103017": "Please Add Your Bank Account First.", "php_code_103019": "The Cell Phone Number Cannot Be Empty.", "php_code_103020": "The Email Cannot Be Empty.", "php_code_103021": "PayChannel Cannot Be Empty.", "php_code_103022": "Please Enter Your Name.", "php_code_103023": "The Gift Bag Has Expired.", "php_code_103024": "Wrong Gift Bag Amount.", "php_code_103025": "Purchase to Reach the Upper Limit.", "php_code_103026": "The Gift Bags are Sold Out.", "php_code_103028": "You Have Requested Too Many Withdrawals Today, Please Try Again Tomorrow.", "php_code_103029": "Event Has Ended or Purchase Exceeded the Limit, Please Come Back Later.", "php_code_103030": "Incorrect Transfer Amount, Please Try Again", "php_code_103031": "Please Bind A Mobile Phone Number", "php_code_103032": "Recipient Does Not Exist. Please Verify the Information and Try Again", "php_code_103033": "Transfer Failed, Please Check Balance And Try Again", "php_code_103034": "Your account is suspended from login. Please try again in 24 hours.", "php_code_103035": "Your %d wallet has reached limit.Please contact customer service to buy coins.", "php_code_103036": "The verification code has expired, please resend.", "php_code_103037": "Your %d account has insufficient balance.", "php_code_103038": "The payment was unsuccessful due to an abnormality.Please try again later.", "php_code_103099": "", "php_code_104001": "The MailID Cannot be Empty.", "php_code_104002": "Invalid Request.", "php_code_104003": "<PERSON><PERSON><PERSON> Failed, Please Try Again.", "php_code_104004": "<PERSON><PERSON><PERSON> Failed, Please Try Again.", "php_code_105001": "The Invite Code Cannot be Empty.", "php_code_105002": "You Have Added An Inviter.", "php_code_105003": "The Inviter Does Not Exist.", "php_code_105004": "Invalid Request.", "php_code_105005": "You Can't Invite Yourself.", "php_code_105006": "Verify Your Phone Number", "php_code_104006": "Please Enter The Correct Recipient ID", "php_code_104007": "Not Enough Chips To Send Mails, Please Click \"OK\" to Purchase More.", "php_code_109001": "Daily Bonus Claimed, Claim Again Tomorrow!", "php_code_100010": "token is lose efficacy", "php_code_102000": "Account not exists", "php_code_102115": "Duplicate nickname", "php_code_102114": "Nickname is too long to exceed 12 characters", "php_code_102113": "less than 2 characters", "php_code_102120": "Your access to PlayZone has expired. Please close the page and enter again.", "php_code_102121": "Your application was submitted successfully!", "php_code_106005": "Your application was submitted Unsuccessful, Please try again later.", "andar_text_1": "Bet how many cards will be dealt on this round", "andar_text_2": "ANDAR", "andar_text_3": "JOKER", "andar_text_4": "BAHAR", "andar_text_5": "SIDE BETS", "andar_text_6": "History", "andar_text_7": "Rules", "andarhelp_text_1": "How to Play", "andarhelp_text_2": "    The dealer shuffles one deck of 52 cards picks one card randomly form the available deck and places if face up, which would be the Game Card, against which bets can be placed for that game.", "andarhelp_text_3": "    Players can choose to bet on either ANDAR or Bahar bet spots for the hand. A minimum bet amount is required to be placed which would be the defult selection when clicked on the bet spot.", "andarhelp_text_4": "Game Result", "andarhelp_text_5": "    If the number on the dealt card matches the value of the game card, the game ends.", "andarhelp_text_6": "Bet Types", "andarhelp_text_7": "    Players can choose to bet on either ANDAR or BAHAR spots. Once bets are placed, the player has to click on the confirm bet button to post the bets. ", "andarhelp_text_8": "Statistics", "andarhelp_text_9": "    Summary of game result for the last 100 games can be viewed here along with the percentage of win on both ANDAR or BAHAR sides. In addition , the winning percentage of the game card(dealer card) in last 100 games on ANDAR and BAHAR can also be viewed at the time of the betting.", "andarhelp_text_10": "Payouts", "andarhelp_text_11": "    Win on ANDAR side gets 90%;", "andarhelp_text_12": "    Win on BAHAR side gets 100%;", "andarhelp_text_13": "Andar and Bahar Side Bet Options", "andarhelp_text_14": "    To spice the game up there is a range fo side bets. The side can be played with or without a main hand bet. The objective is to pick the number of cards that will be dealt before a match with “Joker”card is made. The Payouts are more generous than the main game round, with the highest being 120:1, for 41 or more card dealt.", "andarhelp_text_15": "RESULT   1-5      6-10   11-15   16-25   26-30   31-35   36-40   40+", "andarhelp_text_16": "PAYOFF    x3.5    x4.5    x5.5     x4.5     x15      x25       x50      x120", "dragon_text_1": "Click to bet", "dragon_text_2": "Winning History", "dragon_text_3": "Dragon", "dragon_text_4": "Tiger", "dragon_text_5": "Tie", "dragon_help_text_2": "Games Rules", "dragon_help_text_3": "The dealer deals two cards each game, one for the <PERSON> and one for the <PERSON>. <PERSON><PERSON><PERSON> makes a bet to <PERSON> or <PERSON> which could draw the highest card.", "dragon_help_text_4": "Card from the smallest to the biggest are (ignoring the symbols):", "dragon_help_text_5": "A<2<3<4<5<6<7<8<9<10<J<Q<K", "dragon_help_text_6": "Paid for Dragon/Tiger: 1:1", "dragon_help_text_7": "Paid for tie: 1:8", "dragon_help_text_8": "For <PERSON><PERSON>,betting on the <PERSON> and <PERSON> will get half of the bet amount return back.", "wingo_text_1": "Waiting for nextRound in {0} Second(s)", "wingo_text_2": "Stand By. To Bet, a Minimum Balance of Rs.{0} is Required!", "wingo_text_3": "PLAYERS", "wingo_text_4": "Only Show The Top 30", "helpcell_text_1": "You have 15 seconds of betting time in each round . During the betting time, players can choose different betting areas to bet (a total of 12 betting areas, each betting area has different odds and different bonus limits).", "helpcell_text_2": "After the betting time is over (betting is no longer possible), the middle wheel and the pointer will start to rotate.", "helpcell_text_3": "When the wheel stops, the color and the animal pointed by the pointer ( both criteria must be met at the same time) are the winning area, and the corresponding reward will be obtained according to the player's bet.", "helpcell_text_4": "Betting Area", "helpcell_text_5": "After the betting starts, players can place bets in 12 betting areas, and each betting area has a maximum bet settlement.", "helpcell_text_6": "Special Variety", "helpcell_text_7": "Big Three", "helpcell_text_8": "After the turntable stops, all three colors of the WINNING ANIMAL will win (for example, if the winning bet is a red lion, the red lion, green lion, and the yellow lion are all winning bets, namely the Big Three )", "helpcell_text_9": "Big Four", "helpcell_text_10": "After the turntable stops, the four animals of the same color all win (for example, if  the winning bet is a red lion, the red lion, the red panda, the red monkey, and the red rabbit are all winning bet, namely the Big Four)", "helpcell_text_11": "After the turntable stops, on top of the current winning, another winning point will be generated (the new winning point cannot be identical to the previous winning point)", "helpcell_text_12": "Double", "helpcell_text_13": "After the turntable stops, the odds will be doubled in the current winning betting area (for example: if you win the red tiger 46 times and win a double at the same time, your actual win in this round is 92 times)", "helpcell_text_14": "There are 3 sets of odd tables in the Forest Dance. Please see the tables below:", "helpcell_text_15": "A set of Odds Table will be randomly selected for betting before each round.", "helpcell_text_16": "For example: when Odds Table 1 is selected for this round, the odds for Red lion will be 46 times, and so forth.", "helpcell_text_17": "Odds Table 1", "helpcell_text_18": "Animal", "helpcell_text_19": "Odds", "helpcell_text_20": "Red lion", "helpcell_text_21": "Red panda", "helpcell_text_22": "Red monkey", "helpcell_text_23": "Red rabbit", "helpcell_text_24": "Green lion", "helpcell_text_25": "Green panda", "helpcell_text_26": "Green monkey", "helpcell_text_27": "Green rabbit", "helpcell_text_28": "Yellow lion", "helpcell_text_29": "Yellow panda", "helpcell_text_30": "Yellow monkey", "helpcell_text_31": "Yellow rabbit", "helpcell_text_32": "Odds Table 2", "helpcell_text_33": "Odds Table 3", "helpcell_text_34": "The following is the maximum bet amount for different betting points in each area:", "helpcell_text_35": "lion", "helpcell_text_36": "panda", "helpcell_text_37": "rabbit", "helpcell_text_38": "monkey", "guess_text_1": "CORRECT", "guess_text_2": "Searching for Players", "guess_text_3": "Explore India Quiz", "guess_text_4": "Delhi lies on which river? How many countries border India? In what state is Bengaluru? Explore India’s vibrant culture and maths with this quiz.", "guess_text_5": "PLAY AGAIN", "bankbind_text_1": "Bank Details", "bankbind_text_2": "Enter Your Legal Name", "bankbind_text_3": "Enter Your Telefone", "bankbind_text_4": "Enter Your CPF", "bankbind_text_5": "Account Type:", "bankbind_text_6": "CPF", "bankbind_text_7": "Email", "bankbind_text_8": "Telefone(+55)", "bankbind_text_9": "Submit", "bankbind_text_10": "Confirm", "bankbind_text_11": "Binding Facebook", "bankbind_text_12": "Binding Phone No.", "bankbind_text_13": "Fill Mobile Number", "bankbind_text_14": "Add Bank Details", "phonebing_text_1": "Tips: Get R$5 By Binding Your Mobile Phone !", "profile_text_1": "Profile", "profile_text_2": "Copy", "profile_text_3": "Cancel", "profile_text_4": "Okay", "profile_text_5": "Join Now", "profile_text_6": "FRAME", "profile_text_7": "CARDBACK", "profile_text_8": "PROFILE", "profile_text_9": "<PERSON>ame", "profile_text_10": "choose a avatar", "sharing": "Sharing", "withdraw_text_1": "Claimed Time", "withdraw_text_2": "Bonus Credited", "withdraw_text_3": "Current Balance", "withdraw_text_4": "Expiration Date", "withdraw_text_5": "Please contact customer service whenever you encounter any withdrawal issue.", "withdraw_text_6": "Remember Password", "tips": "Tips", "gift_text_1": "Bonus Validity：1 Day", "gift_text_2": "Earn Now!", "gift_text_3": "Join <PERSON>", "gift_text_4": "Please Enter Your Promo Code", "gift_text_5": "PROMO CODE", "gift_text_6": "Redeem", "gift_text_7": "Buy Now", "gift_text_8": "Make it & Get:", "gift_text_9": "Time Activities", "emoji_text_1": "SIGNALS", "emoji_text_2": "Only in 2V2,and only seen by  teammate", "truco_help_text_1": "Regras Básicas", "truco_help_text_2": "Vira & Manilha", "truco_help_text_3": "Call Truco", "truco_help_text_4": "Mão de Onze", "truco_help_text_5": "Mão de Ferro", "truco_help_text_6": "Hidden Card", "truco_help_text_7": "Truco Paulista é um jogo jogado entre duas pessoas ou duas equipes. A primeira pessoa ou equipe que atingir 12 pontos é a vencedora. Os concorrentes disputam em melhor de três rodadas chamada Mão.", "truco_help_text_8": "No início de uma Mão, cada jogador recebe três cartas. Os jogadores são obrigados a jogar uma carta no início de cada rodada. O jogador com o maior valor da carta ganha a rodada.", "truco_help_text_9": "Qual é a Carta Mais Alta?", "truco_help_text_10": "A sequência de valores das cartas no Truco Paulista, jogando com o Baralho Sujo é:", "truco_help_text_11": "<PERSON><PERSON> baixa", "truco_help_text_12": "<PERSON><PERSON>", "truco_help_text_13": "No entanto, lembre-se que a carta mais alta de uma mão é sempre a \"MANILHA\".", "truco_help_text_14": "Jogo do <PERSON>ho", "truco_help_text_15": "*Se a 1ª rodada empatar, o vencedor da 2ª rodada ganha o ponto.", "truco_help_text_16": "*Se a 2ª rodada empatar, o vencedor da 1ª rodada ganha o ponto.", "truco_help_text_17": "*Se a 3ª rodada empatar, o vencedor da 1ª rodada ganha o ponto.", "truco_help_text_18": "*Se a 1ª e a 2ª rodadas empatarem, o vencedor da 3ª rodada ganha o ponto.", "truco_help_text_19": "*Se todas as três rodadas empatarem, ning<PERSON>m ganha o ponto.", "truco_help_text_20": "No truco, as \"<PERSON><PERSON><PERSON>\" são as 4 cartas mais valiosas do jogo. Para definir a \"Manilha\" em Truco Paulista, uma carta é virada na mesa no início de uma Mão. Este cartão é chamado de \"Vira\".", "truco_help_text_21": "As \"Manilhas\" são as cartas cujo valor é imediatamente superior à carta \"Vira\". Cada “Manilha” tem um nome relacionado ao seu naipe e estão na seguinte ordem crescente de valor: Ouros(Picafumo), Espadas(Espadilha), Copas（Escopeta) e Paus(ZAP). Esses nomes podem variar de acordo com a região onde O truco é jogado.", "truco_help_text_22": "*Por exemplo: \"Vira\"=7", "truco_help_text_23": "Como a carta de valor imediatamente maior é Q, o valor das cartas, da menor para a maior usando o baralho sujo, será:", "truco_help_text_24": "its value is always immediately higher than the “vira”.", "truco_help_text_25": "Como chamar Truco?", "truco_help_text_26": "*Truco é uma aposta que pode ser feita a qualquer momento do jogo.", "truco_help_text_27": "*A dupla oponente deve responder com uma das seguintes opções:", "truco_help_text_28": "Correr: <PERSON><PERSON><PERSON> o truco e a dupla adversária vence a rodada.", "truco_help_text_29": "Aceitar: <PERSON><PERSON> a aposta que será decidida no final da rodada.", "truco_help_text_30": "Truco 09/06/12: Aumenta a aposta e passa a vez para o par adversário decidir.", "truco_help_text_31": "If the opposing team passes,the team that called truco wins the points.", "truco_help_text_32": "Aposta", "truco_help_text_33": "Pontos", "truco_help_text_34": "Se não houver truco", "truco_help_text_35": "o vencedor da rodada ganhará 1 ponto", "truco_help_text_36": "Truco", "truco_help_text_37": "Aumentar para 6/9/12", "truco_help_text_38": "3 pontos", "truco_help_text_39": "6/9/12 pontos", "truco_help_text_40": "O que é \"Mão de Onze\"?", "truco_help_text_41": "*Diz-<PERSON> \"<PERSON>ão de Onze\" quando uma das duplas já tem 11 pontos. A dupla que tiver 11 pontos pode ver as cartas do parceiro e decidir se joga ou não a rodada.", "truco_help_text_42": "*Se não jogar a rodada, os adversários vencem a rodada e marcam 1 ponto.", "truco_help_text_43": "When a team reaches 11 points", "truco_help_text_44": "NóS", "truco_help_text_45": "ELES", "truco_help_text_46": "Como \"Mão de Ferro\"?", "truco_help_text_47": "*Diz-se \"<PERSON>ão de Ferro\" se ambas as duplas tiverem 11 pontos. É normal jogar uma carta. <PERSON><PERSON> as cartas da mesa permanecem viradas para baixo e você não pode chamar Truco. Os vencedores desta rodada serão os vencedores do jogo.", "truco_help_text_48": "When both teams have 11 points,", "truco_help_text_49": "All the cards on the table remain face down and you can't call Truco.", "truco_help_text_50": "CORRER", "truco_help_text_51": "*Os jogadores podem optar por não revelar uma carta, portanto, ela é jogada com a face voltada para baixo. Esta carta não tem valor e qualquer carta jogada pelo oponente a vencerá.", "truco_help_text_52": "*Para jogar uma carta virada para baixo, basta pressionar e segurar a carta por um momento e ela virará. Qualquer carta pode ser jogada como carta oculta, mas apenas durante a 2ª ou 3ª rodadas, nunca na primeira.", "truco_help_text_53": "TIP:Tap and hold to filp the card from  the second round onward", "hall_text_1": "Refer & Earn", "hall_text_2": "Lucky Shot", "hall_text_3": "VIP", "hall_text_4": "Red&Black", "hall_text_5": "Lucky Wheel", "hall_text_6": "Daily Login", "shop_text_1": "Get Chips", "shop_text_2": "Exit Game", "shop_text_3": "Log Out", "reward_text_1": "Daily Login Bonus", "reward_text_2": "1st Day", "reward_text_3": "2nd Day", "reward_text_4": "3rd Day", "reward_text_5": "7th+ Day", "reward_text_6": "4th Day", "reward_text_7": "5th Day", "reward_text_8": "6th Day", "reward_text_9": "Get More", "reward_text_10": "<PERSON><PERSON><PERSON>", "text_title_1": "Service", "text_title_2": "Setting", "text_title_3": "Spin", "rummy_text_1": "Sequence", "rummy_text_2": " Score = 0", "rummy_text_3": "Back", "rummy_text_4": "Drop", "rummy_text_5": "Finish", "rummy_text_6": "<PERSON><PERSON><PERSON>", "rummy_text_7": "Group", "rummy_text_8": "Discard", "rummy_text_9": "unknown", "rummy_text_10": "FINISH\nSLOT", "rummy_help_text_1": "Basic Rules", "rummy_help_text_2": "The game is played with 2 decks of cards.Arrange all 13 cards in sets of 2 and sequences of 3 or more cards", "rummy_help_text_3": "In the above case,all 10s function as jokers and can be used as any cards", "rummy_help_text_4": "Sets", "rummy_help_text_5": "Invalid Set/Sequence", "rummy_help_text_6": "How To Group", "rummy_help_text_7": "Tap on the cards you want to group", "rummy_help_text_8": "Tap on the 'Group' botton!", "rummy_help_text_9": "See them grouped", "rummy_help_text_10": "How To Discard", "rummy_help_text_11": "Tap on the card you wish to discard! Tap on the  'DISCAD' button!", "rummy_help_text_12": "How To Finish", "rummy_help_text_13": "Tap on the card you want to discard in order to finish the game!", "rummy_help_text_14": "Tap in the 'FINISH' button! The game is finished! Now you win!", "rummy_help_text_15": "How To Declare", "rummy_help_text_16": "Make sure all your possible sequence/sets are grouped together!", "rummy_help_text_17": "Player", "rummy_help_text_18": "Cards", "rummy_help_text_19": "Score", "rummy_help_text_20": "Continue", "rummy_help_text_21": "New Anti-Fraud Function", "rummy_help_text_22": "Pure Sequence(no joker)", "loadin_text_1": "Phone No.", "loadin_text_2": "Facebook", "loadin_text_3": "Play as a Guest", "loadin_text_4": "Classic", "tp_text_1": "Reject", "tp_text_2": "Accept", "tp_help_1": "Players will be dealt with two ordinary cards and one joker. Everyone will get one compulsory joker.", "tp_help_2": "Ranking of hands is still as per standard Teenpatti rules where Joker is replaced by a better card.", "tp_help_3": "In this 3-patti variation - A(♠♥♦♣), K(♠♥♦♣), 4(♠♥♦♣), 7(♠♥♦♣) of all suits are jokers or wild card", "tp_help_4": "A player with strongest 3 Patti hand will win the game. All other rules and betting remains the same", "tp_help_5": "Example: – let’s assume we get A♠-K♦-4♠, This will become A-A- A (as all three cards Ace, King, 4 are jokers)", "tp_help_6": "Example: – Let’s assume we get A♠-2♠-2♥ & opponent has Q♥-4♥-7♣, then the winner will be opponent. (Q-4- 7 = Q-Q- Q as 4 & 7 are joker, while we have 2-2- 2)", "truco_text_1": "Message", "truco_text_2": "VICTORY", "truco_text_3": "Round", "truco_text_4": "Return to table", "truco_text_5": "We", "truco_text_6": "They", "truco_text_7": "Round", "truco_text_8": "Points", "truco_text_9": "Show cards", "truco_text_10": "Rai<PERSON>", "truco_text_11": "Accept", "truco_text_12": "Run", "truco_text_13": "Table Information", "truco_text_14": "Amount", "truco_text_15": "Mode", "truco_text_16": "Players", "truco_text_17": "Deck", "truco_text_18": "Paulista", "truco_text_19": "Mineiro", "truco_text_20": "Dirty", "truco_text_21": "Clean", "truco_text_22": "1 VS 1", "truco_text_23": "2 VS 2", "truco_text_24": "Please click on the 'Ready' button to play.", "truco_text_25": "Ready", "truco_text_26": "Invite friends", "truco_text_27": "Matching players, please wait...", "updown7_text_1": "7UP Down is a simple dice game", "updown7_text_2": "Below 7", "updown7_text_3": "Equal 7", "updown7_text_4": "Above 7", "updown7_text_5": "BET", "updown7_text_6": "If you spend 100 to bet:", "updown7_text_7": "1. Bet on GREEN:", "updown7_text_8": "If the result shows1,3,7,9, you will get(100*2)=200", "updown7_text_9": "If the result shows 5, you will get (100*1.5)=150", "updown7_text_10": "2. Bet on RED:", "updown7_text_11": "If the result shows2,4,6,8, you will get(100*2)=200", "updown7_text_12": "If the result shows 0, you will get(100*1.5)=150", "updown7_text_13": "3. Bet on VIOLET:", "updown7_text_14": "if the result shows 0 or 5, you will get(100*4.5)450", "updown7_text_15": "4. Bet on NUMBERS:", "updown7_text_16": "if the result is the same as the number you selected, you will get(100*9)=900", "updown7_text_17": "When the dice result opened,gold dice bonus will randomly appears,and all the players betted in this round will win (win money = the amount he betted*sum of the two dices' points)", "updown7_text_18": "Gold Dice", "bonustipword1": "1.You can get bonus by purchasing promotions or participating in all kinds of limited time event.", "bonustipword2": "2.Pay attention that each bonus you got has an expiry date, please use it in time.", "bonustipword3": "3.10% of your loss  (max. one boot amount) will be covered by the bonus in your bonus account, added to your \"Cash Balance\" after every round of game.", "carrom_text_1": "1.The aim of the game is to get all your diskes into the holes.", "carrom_text_2": "2.The game will end only after the red disk is in the hole.", "carrom_text_3": "3.The red disk needs to be covered after it is in the hole, or else it will reappear on the table.", "carrom_text_4": "4.You can pocket the red disk anytime after you hit the first disk & before all the other disks are gone.", "carrom_text_5": "5.The number of diskes you have pocketed will be displayed next to your profile picture in the game.", "carrom_text_6": "PLAY AGAIN", "carrom_text_7": "TOTAL", "carrom_text_8": "The player will lose his turn when timed out. If timed out for more than 3 times, the player will lose the game.", "carrom_text_9": "1.The aim of the game is to get all your diskes into the holes.", "carrom_text_10": "2.The number of diskes you have pocketed will be displayed next to your profile picture in the game.", "carrom_text_11": "The player will lose his turn when timed out. If timed out for more than 3 times, the player will lose the game.", "carrom_text_12": "1.The first player to reach the goal score will win the game.", "carrom_text_13": "2.Pocketing disks of different colors will generate different scores.", "carrom_text_14": "3.The red disk needs to be covered after it is in the hole, or else it will reappear on the table.", "carrom_text_15": "4.The red disk (The queen) must hit a disk of color, the otherwise will be considered as foul play.", "carrom_text_16": "5.Violating the rules for over 5 times will be considered as \"Foul Lose\".", "carrom_text_17": "6.Scores will be calculated based on: the score difference between two parties * boot. Yielding will be resulting in maximum deduction of boot amount.", "carrom_text_18": "7.After completing the game, the losing party will score 0 and the winning party will win the double amount.", "carrom_text_19": "Overtime or Violating the rules will be considered as foul play, 5 foul plays will lead to losing the game.", "carrom_text_20": "Drag to position", "texas_text_0": "R$ #0", "texas_text_1": "Check", "texas_text_2": "Bet", "texas_text_3": "Call", "texas_text_4": "Rai<PERSON>", "texas_text_5": "Fold", "texas_text_6": "All In", "texas_text_7": "High Card", "texas_text_8": "One Pair", "texas_text_9": "Two Pairs", "texas_text_10": "Three of A Kind", "texas_text_11": "Straight", "texas_text_12": "Flush", "texas_text_13": "Full House", "texas_text_14": "Four of A Kind", "texas_text_15": "Straight Flush", "texas_text_16": "Royal Flush", "texas_text_18": "Stakes: #0/#1", "texas_text_19": "No.#0", "texas_text_20": "Min buy-in: R$ #0", "texas_text_21": "PACK", "texas_text_22": "Pot: ", "texas_text_23": "Pot", "texas_text_24": "Stand Up", "texas_text_25": "3/4 Pot", "texas_text_26": "1/2 Pot", "texas_text_27": "+1BB", "texas_text_28": "Fold", "texas_text_29": "Check/Fold", "texas_text_30": "Call Any", "texas_text_31": "The next hand your chips will replenish", "texas_text_32": "Confirm", "texas_text_33": "VPIP", "texas_text_34": "AF", "texas_text_35": "Total Games", "texas_help_1": "Play of the hand", "texas_help_2": "Following a shuffle of the cards, play begins with each player being dealt two cards face down, with the player in the small blind receiving the first card and the player in the button seat receiving the last card dealt. (As in most poker games, the deck is a standard 52-card deck containing no jokers.) These cards are the players' hole or pocket cards. These are the only cards each player will receive individually, and they will (possibly) be revealed only at the showdown, making Texas hold 'em a closed poker game.", "texas_help_3": "The hand begins with a \"pre-flop\" betting round, beginning with the player to the left of the big blind (or the player to the left of the dealer, if no blinds are used) and continuing clockwise. A round of betting continues until every player has folded, put in all of their chips, or matched the amount put in by all other active players. See betting for a detailed account. Note that the blinds are considered \"live\" in the pre-flop betting round, meaning that they are counted toward the amount that the blind player must contribute. If all players call around to the player in the big blind position, that player may either check or raise.", "texas_help_4": "After the pre-flop betting round, assuming there remain at least two players taking part in the hand, the dealer deals a flop: three face-up community cards. The flop is followed by a second betting round. This and all subsequent betting rounds begin with the player to the dealer's left and continue clockwise.", "texas_help_4-1": "After the flop betting round ends, a single community card (called the turn or fourth street) is dealt, followed by a third betting round. A final single community card (called the river or fifth street) is then dealt, followed by a fourth betting round and the showdown, if necessary. In the third and fourth betting rounds, the stakes double.", "texas_help_5": "In all casinos, the dealer will burn a card before the flop, turn, and river. Because of this burn, players who are betting cannot see the back of the next community card to come. This is done for traditional reasons, to avoid any possibility of a player knowing in advance the next card to be dealt due to its being marked.", "texas_help_6": "The showdown", "texas_help_7": "If a player bets and all other players fold, then the remaining player is awarded the pot and is not required to show their hole cards. If two or more players remain after the final betting round, a showdown occurs. On the showdown, each player plays the best poker hand they can make from the seven cards comprising their two-hole cards and the five community cards. A player may use both of their own two hole cards, only one, or none at all, to form their final five-card hand. If the five community cards form the player's best hand, then the player is said to be playing the board and can only hope to split the pot, because each other player can also use the same five cards to construct the same hand.", "texas_help_8": "If the best hand is shared by more than one player, then the pot is split equally among them, with any extra chips going to the first players after the button in clockwise order. It is common for players to have closely valued, but not identically ranked hands. Nevertheless, one must be careful in determining the best hand; if the hand involves fewer than five cards, (such as two pair or three of a kind), then kickers are used to settle ties (see the second example below). The card's numerical rank is of sole", "texas_help_9": "importance; suit values are irrelevant in hold 'em.", "texas_help_10": "Hand values", "texas_help_11": "Following table shows the possible hand values in increasing order.", "texas_help_12": "Highcard", "texas_help_13": "Simple value of the card. Lowest: 2 – Highest: Ace", "texas_help_14": "Pair", "texas_help_15": "Two cards with the same value", "texas_help_16": "Two pairs", "texas_help_17": "Two times two cards with the same value", "texas_help_18": "Three Of A Kind", "texas_help_19": "Three cards with the same value", "texas_help_20": "Straight", "texas_help_21": "Sequence of 5 cards in increasing value (<PERSON> can precede 2 and follow up <PERSON>)", "texas_help_22": "Flush", "texas_help_23": "5 cards of the same suit", "texas_help_24": "Full House", "texas_help_25": "Combination of three of a kind and a pair", "texas_help_26": "Four of a kind", "texas_help_27": "Four cards of the same value", "texas_help_28": "Straight Flush", "texas_help_29": "Straight of the same suit", "texas_help_30": "Royal Flush", "texas_help_31": "Straight flush from Ten to Ace", "texas_help_32": "Actions", "texas_help_33": "In a betting round,the player can perform one of the following actions", "texas_help_34": "Call", "texas_help_35": "If there has already been a bet,calling means that a player agrees to match the bet.In other words.if player <PERSON> bets 10,and player <PERSON> calls,player <PERSON> will have to match the 10 bet and put 10 into the pot.", "texas_help_36": "Check", "texas_help_37": "If there have been no bets in the current round,a player can check.Action then moves to the next player on the left.The check can be interpreted as a sort of pass:you remain in the game,but decide not to bet in the current round.", "texas_help_38": "Rai<PERSON>", "texas_help_39": "A player can increase the current size of the bet.To do this,the bet must exceed the last player’s bet by at least double.To continue competing for the pot,all the subsequent players must call or re-raise this bet,or fold", "texas_help_40": "Fold", "texas_help_41": "Folding means to refuse to compete for the current pot.If a player folds, the player’s cards no longer participate in the hand and cannot win in the current round.", "tongits_text_1": "Fight", "tongits_text_2": "Drop", "tongits_text_3": "Group", "tongits_text_4": "<PERSON><PERSON>", "tongits_text_5": "Fold", "tongits_text_6": "Challenge", "tongits_text_cardtype_1": "SPECIFIED CARD TYPE"}