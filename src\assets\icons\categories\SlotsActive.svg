<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_717_3345)">
<rect width="24" height="24" rx="10" fill="url(#paint0_linear_717_3345)"/>
</g>
<mask id="mask0_717_3345" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" rx="10" fill="url(#paint1_linear_717_3345)"/>
</mask>
<g mask="url(#mask0_717_3345)">
<path d="M27.5 16.5L16.5 5.5L8 19L16.5 27L27.5 16.5Z" fill="#E90800"/>
</g>
<mask id="mask1_717_3345" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
<path d="M16 0H0V16H16V0Z" fill="white"/>
</mask>
<g mask="url(#mask1_717_3345)">
</g>
<mask id="mask2_717_3345" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="4" y="4" width="16" height="16">
<path d="M20 4H4V20H20V4Z" fill="white"/>
</mask>
<g mask="url(#mask2_717_3345)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.4297 5.74667L17.7271 5.04666C17.6803 5 17.6335 5 17.5397 5C17.4929 5 17.3992 5.04666 17.3524 5.09333C17.3524 5.09333 16.3687 6.16666 15.0571 6.16666C14.4012 6.16666 13.9328 5.88667 13.4644 5.60669C12.9959 5.32664 12.4338 5 11.5906 5C10.4664 5 9.43587 5.70002 8.87374 6.11998V5.23331C8.87374 5.09333 8.78006 5 8.63953 5H7.23419C7.14051 5 7 5.09333 7 5.23331V11.3C7 11.44 7.14051 11.5333 7.23419 11.5333H8.63953C8.78006 11.5333 8.87374 11.44 8.87374 11.3V10.1334C8.87374 9.48002 9.15476 9.20004 9.8106 9.20004C9.99796 9.20004 10.2791 9.24669 10.6069 9.34002C11.1691 9.48002 11.9185 9.66666 12.8554 9.66666C13.4175 9.66666 13.7923 9.62001 14.1202 9.52668C13.6049 10.32 12.7149 11.58 11.8248 12.0467C10.4196 12.7933 8.87374 15.4066 8.87374 18.7667C8.87374 18.9067 9.01425 19 9.10793 19H15.1976C15.3381 19 15.4318 18.9067 15.4318 18.7667C15.4318 18.72 15.4318 18.6734 15.3849 18.6267C15.2913 18.3467 14.9634 16.9933 14.9634 15.7334C14.9634 14.2867 17.5866 7.00666 18.4297 6.07333C18.5234 5.98 18.5234 5.84 18.4297 5.74667Z" fill="#FF6961"/>
<g filter="url(#filter1_i_717_3345)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.4297 5.74667L16.7271 5.04666C16.6803 5 16.6335 5 16.5397 5C16.4929 5 16.3992 5.04666 16.3524 5.09333C16.3524 5.09333 15.3687 6.16666 14.0571 6.16666C13.4012 6.16666 12.9328 5.88667 12.4644 5.60669C11.9959 5.32664 11.4338 5 10.5906 5C9.46642 5 8.43587 5.70002 7.87374 6.11998V5.23331C7.87374 5.09333 7.78006 5 7.63953 5H6.23419C6.14051 5 6 5.09333 6 5.23331V11.3C6 11.44 6.14051 11.5333 6.23419 11.5333H7.63953C7.78006 11.5333 7.87374 11.44 7.87374 11.3V10.1334C7.87374 9.48002 8.15476 9.20004 8.8106 9.20004C8.99796 9.20004 9.27905 9.24669 9.60695 9.34002C10.1691 9.48002 10.9185 9.66666 11.8554 9.66666C12.4175 9.66666 12.7923 9.62001 13.1202 9.52668C12.6049 10.32 11.7149 11.58 10.8248 12.0467C9.41958 12.7933 7.87374 15.4066 7.87374 18.7667C7.87374 18.9067 8.01425 19 8.10793 19H14.1976C14.3381 19 14.4318 18.9067 14.4318 18.7667C14.4318 18.72 14.4318 18.6734 14.3849 18.6267C14.2913 18.3467 13.9634 16.9933 13.9634 15.7334C13.9634 14.2867 16.5866 7.00666 17.4297 6.07333C17.5234 5.98 17.5234 5.84 17.4297 5.74667Z" fill="white"/>
</g>
</g>
<path opacity="0.6" d="M11.3193 20.582L11.9004 20.9004L11.3193 21.2178L11.001 21.7998L10.6826 21.2178L10.1006 20.9004L10.6826 20.582L11.001 20L11.3193 20.582ZM4.30176 17.4395L5.23926 17.9521L4.30176 18.4648L3.78906 19.4023L3.27637 18.4648L2.33887 17.9521L3.27637 17.4395L3.78906 16.5029L4.30176 17.4395ZM22.1592 8.61426L22.7734 8.9502L22.1592 9.28613L21.8232 9.90039L21.4873 9.28613L20.873 8.9502L21.4873 8.61426L21.8232 8L22.1592 8.61426ZM5.18066 3.43555L5.69629 3.71777L5.18066 4L4.89844 4.51562L4.61621 4L4.10059 3.71777L4.61621 3.43555L4.89844 2.9209L5.18066 3.43555ZM16.835 2.44043L17.5791 2.84766L16.835 3.25488L16.4277 4L16.0205 3.25488L15.2754 2.84766L16.0205 2.44043L16.4277 1.69531L16.835 2.44043Z" fill="white"/>
<defs>
<filter id="filter0_i_717_3345" x="0" y="0" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.914203 0 0 0 0 0.0309151 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_3345"/>
</filter>
<filter id="filter1_i_717_3345" x="6" y="5" width="11.5" height="14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.729412 0 0 0 0 0.713726 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_3345"/>
</filter>
<linearGradient id="paint0_linear_717_3345" x1="0" y1="0" x2="24" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF7E3D"/>
<stop offset="1" stop-color="#FF3800"/>
</linearGradient>
<linearGradient id="paint1_linear_717_3345" x1="0" y1="0" x2="24" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF8A55"/>
<stop offset="1" stop-color="#FF6445"/>
</linearGradient>
</defs>
</svg>
