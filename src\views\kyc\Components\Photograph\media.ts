export const error = (text: any) => {
  console.error(text);
  throw new Error(text);
};

const createProcessedStream = (media: any, config: any, err: any) => {
  if (!media.canvasCtx || !media.mediaStream) return error("初始化失败");
  if (!media.video) return error("video is required");
  const canvas = document.getElementById("kyc-photograph-canvas") as HTMLCanvasElement;
  const dpr = window.devicePixelRatio || 1;
  // 根据设备像素比设置canvas尺寸
  canvas.width = media.video.clientWidth * dpr;
  canvas.height = media.video.clientHeight * dpr;

  // 提升画布绘制质量
  media.canvasCtx.scale(dpr, dpr);
  media.canvasCtx.imageSmoothingQuality = "high";
  media.canvasCtx.imageSmoothingEnabled = true;

  const processedStream = canvas.captureStream(30);
  if (config.isAudio) {
    const audioTracks = media.mediaStream.getAudioTracks();
    if (audioTracks.length > 0) {
      processedStream.addTrack(audioTracks[0]);
    }
  }
  const videoElement = document.createElement("video");
  videoElement.srcObject = new MediaStream(media.mediaStream.getVideoTracks());
  videoElement.onloadedmetadata = () => {
    videoElement.play().then(() => {
      drawVideoFrame(videoElement, media, config, err);
    });
  };

  return processedStream;
};
const drawVideoFrame = (v: any, media: any, config: any, err: any) => {
  if (!media.canvasCtx || !media.video) return;
  const scaleRatio = Math.max(
    media.video.clientWidth / v.videoWidth,
    media.video.clientHeight / v.videoHeight
  );

  // 根据缩放比例计算绘制时的宽度和高度
  const drawWidth = v.videoWidth * scaleRatio;
  const drawHeight = v.videoHeight * scaleRatio;

  // 计算图像在Canvas上的目标位置，使其居中
  const x = (media.video.clientWidth - drawWidth) / 2;
  const y = (media.video.clientHeight - drawHeight) / 2;

  media.canvasCtx.drawImage(v, x, y, drawWidth, drawHeight);
  media.animationFrameId = requestAnimationFrame(() => drawVideoFrame(v, media, config, err));
};

export const setupCamera = async (media: any, config: any, err: any) => {
  try {
    media.video = document.getElementById("kyc-photograph-video");
    const canvas: any = document.getElementById("kyc-photograph-canvas");
    let facingMode = "user";
    if (config.facingMode) facingMode = config.facingMode;
    // 获取原始媒体流
    media.mediaStream = await navigator.mediaDevices?.getUserMedia({
      video: {
        facingMode,
      },
      audio: config.isAudio,
    });
    // 初始化Canvas
    media.canvasCtx = canvas.getContext("2d", {
      alpha: false, // 关闭透明度提升渲染性能
      willReadFrequently: false, // 关闭频繁读取提升渲染性能
    });
    // 创建带水印的视频流
    media.canvasStream = createProcessedStream(media, config, err);
    // 显示处理后的视频
    media.video.srcObject = media.canvasStream;
  } catch (e: any) {
    return error("Error accessing media devices: " + e.message);
  }
};
