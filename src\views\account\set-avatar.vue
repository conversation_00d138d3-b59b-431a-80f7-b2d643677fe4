<script setup lang="ts">
defineOptions({ name: "SetAvatar" });

import { useGlobalStore } from "@/stores/global";
import { getAvatarList, updateAvatar } from "@/api/user";
import { ref } from "vue";
import { CHANEL_TERMINAL } from "@/utils/config/GlobalConstant";
import { showToast } from "vant";
import router from "@/router";
import { getServerSideImageUrl } from "@/utils/core/tools";

const globalStore = useGlobalStore();

const HEAF_TYPE = {
  MALE: 1,
  FEMALE: 2,
};

const DEFAULT_MALE_HEAD = [
  // male
  { avatar: "images/786d8c65d99336971d7c12d42b12203b.png" },
  { avatar: "images/d5360c4f29a174375b83beb535468cf5.png" },
  { avatar: "images/48386cd8ee0f61c8fc589b3e35d5215a.png" },
  { avatar: "images/1f89e2016f51a5e6279e52cf7965f1a5.png" },
];

const DEFAULT_FEMALE_HEAD = [
  // female
  { avatar: "images/be9171004a4c729473bac2e333768c0c.png" },
  { avatar: "images/48011316ef3f9ba10756849ae324144e.png" },
  { avatar: "images/07159c495a65de0117fb8f1cedd1fb36.png" },
  { avatar: "images/cdb8c7d0879ab87ffbacc158f92ebab4.png" },
];

const maleHeadList = ref([...DEFAULT_MALE_HEAD]);
const femaleHeadList = ref([...DEFAULT_FEMALE_HEAD]);
const selectedHead = ref("");

const fetchAvatarList = async () => {
  const res = await getAvatarList({
    terminal: CHANEL_TERMINAL[globalStore.channel],
  });
  const avatarListRes = res?.List || [];
  for (let i = 0; i < avatarListRes.length; i++) {
    const type = avatarListRes[i].class;
    if (type == HEAF_TYPE.MALE) {
      maleHeadList.value.push(...avatarListRes[i].avatars);
    }
    if (type == HEAF_TYPE.FEMALE) {
      femaleHeadList.value.push(...avatarListRes[i].avatars);
    }
  }
  // 根据 avatar 的值去重
  maleHeadList.value = maleHeadList.value.filter(
    (item, index, self) => index === self.findIndex((t) => t.avatar === item.avatar)
  );
  femaleHeadList.value = femaleHeadList.value.filter(
    (item, index, self) => index === self.findIndex((t) => t.avatar === item.avatar)
  );
};

const chooseAvatar = (item) => {
  selectedHead.value = item.avatar;
};

const handleUpdateAvatar = async (e) => {
  e.stopPropagation();
  if (!selectedHead.value) return;
  const res = await updateAvatar({
    avatar: selectedHead.value,
  });
  // 更新全局状态
  globalStore.updateUserInfo({ avatar: selectedHead.value });
  showToast("Done");
  router.push("/account");
};
</script>
<template>
  <ZPage backgroundColor="#F4F8FB" :request="fetchAvatarList">
    <div class="set-avatar-wrap">
      <div class="set-avatar">
        <div class="set-avatar-title">
          <h3>Male</h3>
          <div class="list">
            <div v-for="item in maleHeadList" :key="item.avatar">
              <div
                :class="[`list-item`, { active: item.avatar == selectedHead }]"
                @click="chooseAvatar(item)"
              >
                <ZImage :src="getServerSideImageUrl(item.avatar)" :class="`avatar-item`" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="set-avatar">
        <div class="set-avatar-title">
          <h3>Female</h3>
          <div class="list">
            <div v-for="item in femaleHeadList" :key="item.avatar">
              <div
                :class="[`list-item`, { active: item.avatar == selectedHead }]"
                @click="chooseAvatar(item)"
              >
                <ZImage :src="getServerSideImageUrl(item.avatar)" :class="`avatar-item`" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ZButton class="btn" @click="handleUpdateAvatar">Submit</ZButton>
  </ZPage>
</template>

<style scoped lang="scss">
.btn {
  position: fixed;
  bottom: 25px;
  left: 20px;
  right: 20px;
  width: calc(100% - 40px);
}

.set-avatar-wrap {
  padding: 0 12px 100px;
}

.set-avatar {
  padding-top: 20px;

  h3 {
    color: #222;
    /* 普通大标题 */
    font-family: Inter;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    padding: 10px 20px;
  }

  .list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    background-color: #fff;
    border-radius: 20px;
    gap: 10px;
    padding: 20px;

    .list-item {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 3px solid #fff;
      cursor: pointer;

      &.active {
        border-color: #ac1140;
      }

      .avatar-item {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
      }
    }
  }
}
</style>
