/**
 * 现代化 Banner 显示管理器
 * 使用 TypeScript 严格类型和函数式编程设计
 *
 * @example
 * ```typescript
 * const manager = new BannerDisplayManager();
 *
 * // 检查是否可以显示
 * const canShow = manager.canShow({ id: 'banner-1', maxShows: 3 }, 'user123');
 *
 * // 记录显示
 * manager.recordShow('banner-1', 'user123');
 *
 * // 批量过滤
 * const showableBanners = manager.filterShowable(banners, 'user123');
 * ```
 */

import { getLocalStorage, setLocalStorage, removeLocalStorage } from "@/utils/core/Storage";
import { getToday } from "@/utils/core/tools";

// 存储键前缀
const STORAGE_PREFIX = "POP_BANNER";

// Banner 显示记录
export interface BannerDisplayRecord {
  readonly count: number;
  readonly date: string;
  readonly maxShows: number;
}

// Banner 配置
export interface BannerConfig {
  readonly id: string | number;
  readonly maxShows: number; // 每日最大显示次数
  readonly [key: string]: any;
}

// Banner 统计信息
export interface BannerStats {
  readonly todayCount: number;
  readonly lastShowDate: string;
  readonly canShow: boolean;
  readonly remainingShows: number;
}

/**
 * 现代化 Banner 显示管理器
 * 使用组合而非继承，支持依赖注入
 */
export class BannerDisplayManager {
  private readonly storagePrefix: string;

  constructor(storagePrefix: string = STORAGE_PREFIX) {
    this.storagePrefix = storagePrefix;
  }

  /**
   * 生成存储键名
   */
  private createStorageKey(bannerId: string | number, userId?: string): string {
    const baseKey = `${this.storagePrefix}_${bannerId}`;
    return userId ? `${baseKey}_USER_${userId}` : `${baseKey}_GLOBAL`;
  }

  /**
   * 解析存储的显示记录
   */
  private parseDisplayRecord(value: string, defaultMaxShows: number = 0): BannerDisplayRecord {
    if (!value) {
      return { count: 0, date: "", maxShows: defaultMaxShows };
    }

    const [countStr, dateStr, maxShowsStr] = value.split("_");
    return {
      count: parseInt(countStr) || 0,
      date: dateStr || "",
      maxShows: parseInt(maxShowsStr) || defaultMaxShows,
    };
  }

  /**
   * 序列化显示记录
   */
  private serializeDisplayRecord(record: BannerDisplayRecord): string {
    return `${record.count}_${record.date}_${record.maxShows}`;
  }

  /**
   * 获取 Banner 的显示记录
   */
  public getDisplayRecord(
    bannerId: string | number,
    maxShows: number = 0,
    userId?: string
  ): BannerDisplayRecord {
    const key = this.createStorageKey(bannerId, userId);
    const value = getLocalStorage(key) || "";
    const record = this.parseDisplayRecord(value, maxShows);

    // 如果不是今天的记录，返回空记录
    const today = getToday();
    if (record.date && record.date !== today) {
      return { count: 0, date: today, maxShows };
    }

    return record;
  }

  /**
   * 记录 Banner 显示
   */
  public recordShow(bannerId: string | number, maxShows: number, userId?: string): number {
    const currentRecord = this.getDisplayRecord(bannerId, maxShows, userId);
    const today = getToday();

    const newRecord: BannerDisplayRecord = {
      count: currentRecord.count + 1,
      date: today,
      maxShows,
    };

    const key = this.createStorageKey(bannerId, userId);
    const value = this.serializeDisplayRecord(newRecord);
    setLocalStorage(key, value);

    return newRecord.count;
  }

  /**
   * 检查 Banner 是否可以显示
   */
  public canShow(banner: BannerConfig, userId?: string): boolean {
    const record = this.getDisplayRecord(banner.id, banner.maxShows, userId);
    const today = getToday();

    // 如果不是今天的记录，可以显示
    if (record.date !== today) {
      return true;
    }

    // 检查是否超过每日限制
    return record.count < banner.maxShows;
  }

  /**
   * 批量过滤可显示的 Banner
   */
  public filterShowable(banners: BannerConfig[], userId?: string): BannerConfig[] {
    return banners.filter((banner) => this.canShow(banner, userId));
  }

  /**
   * 检查是否有任何可显示的 Banner
   */
  public hasShowable(banners: BannerConfig[], userId?: string): boolean {
    if (!banners?.length) return false;
    return banners.some((banner) => this.canShow(banner, userId));
  }

  /**
   * 获取 Banner 统计信息
   */
  public getStats(bannerId: string | number, maxShows: number, userId?: string): BannerStats {
    const record = this.getDisplayRecord(bannerId, maxShows, userId);
    const today = getToday();
    const todayCount = record.date === today ? record.count : 0;

    return {
      todayCount,
      lastShowDate: record.date,
      canShow: todayCount < maxShows,
      remainingShows: Math.max(0, maxShows - todayCount),
    };
  }

  /**
   * 清除单个 Banner 数据
   */
  public clear(bannerId: string | number, userId?: string): void {
    const key = this.createStorageKey(bannerId, userId);
    removeLocalStorage(key);
  }

  /**
   * 清除所有 Banner 数据
   */
  public clearAll(userId?: string): void {
    const prefix = userId ? `USER_${userId}_${this.storagePrefix}` : `GLOBAL_${this.storagePrefix}`;

    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(prefix)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => removeLocalStorage(key));
  }

  /**
   * 重置特定 Banner 的今日计数
   */
  public resetBannerCount(bannerId: string | number, maxShows: number, userId?: string): void {
    const today = getToday();
    const resetRecord: BannerDisplayRecord = {
      count: 0,
      date: today,
      maxShows,
    };

    const key = this.createStorageKey(bannerId, userId);
    setLocalStorage(key, this.serializeDisplayRecord(resetRecord));
  }

  /**
   * 重置今日所有计数（用于测试或特殊情况）
   */
  public resetTodayCount(userId?: string): void {
    const prefix = userId ? `USER_${userId}_${this.storagePrefix}` : `GLOBAL_${this.storagePrefix}`;
    const today = getToday();

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(prefix)) {
        const value = getLocalStorage(key) || "";
        const [, dateStr] = value.split("_");

        // 只重置今天的记录
        if (dateStr === today) {
          // 从现有记录中获取 maxShows
          const [, , maxShowsStr] = value.split("_");
          const maxShows = parseInt(maxShowsStr) || 0;

          // 重置为今日零计数，保持数据格式一致
          const resetRecord: BannerDisplayRecord = {
            count: 0,
            date: today,
            maxShows,
          };
          setLocalStorage(key, this.serializeDisplayRecord(resetRecord));
        }
      }
    }
  }
}

export const bannerStorageManager = new BannerDisplayManager();
