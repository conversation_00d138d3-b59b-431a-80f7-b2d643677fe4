/**
 * 游戏筛选状态本地存储管理器
 * 专门用于 game-categories 和 casino-cate 页面的筛选状态管理
 */

export interface GameFiltersState {
  searchValue: string;
  selectedProviders: string[];
  providerDetails: Array<{
    id: string;
    provider: string;
  }>;
}

export class GameFiltersStorage {
  private static readonly STORAGE_KEY = "game_filters_state";
  private static readonly VALID_ROUTES = ["/game-categories", "/casino-cate"];

  // 用于触发响应式更新的回调函数列表
  private static updateCallbacks: Array<() => void> = [];

  /**
   * 获取筛选状态
   */
  static getFilters(): GameFiltersState {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          searchValue: parsed.searchValue || "",
          selectedProviders: Array.isArray(parsed.selectedProviders)
            ? parsed.selectedProviders
            : ["all"],
          providerDetails: Array.isArray(parsed.providerDetails) ? parsed.providerDetails : [],
        };
      }
    } catch (error) {
      console.warn("读取游戏筛选状态失败:", error);
    }

    // 返回默认状态
    return {
      searchValue: "",
      selectedProviders: ["all"],
      providerDetails: [],
    };
  }

  /**
   * 订阅状态变化
   */
  static subscribe(callback: () => void): () => void {
    this.updateCallbacks.push(callback);
    // 返回取消订阅函数
    return () => {
      const index = this.updateCallbacks.indexOf(callback);
      if (index > -1) {
        this.updateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有订阅者状态已更新（异步执行，避免阻塞）
   */
  private static notifyUpdate(): void {
    // 使用 setTimeout 异步执行通知，避免阻塞主线程
    setTimeout(() => {
      this.updateCallbacks.forEach((callback) => {
        try {
          callback();
        } catch (error) {
          console.error("状态更新回调执行失败:", error);
        }
      });
    }, 0);
  }

  /**
   * 保存筛选状态
   */
  static setFilters(filters: Partial<GameFiltersState>): void {
    try {
      const current = this.getFilters();
      const updated = {
        ...current,
        ...filters,
      };

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updated));

      // 通知订阅者状态已更新
      this.notifyUpdate();
    } catch (error) {
      console.error("保存游戏筛选状态失败:", error);
    }
  }

  /**
   * 更新搜索值
   */
  static setSearchValue(searchValue: string): void {
    this.setFilters({ searchValue });
  }

  /**
   * 更新厂商筛选
   */
  static setSelectedProviders(
    selectedProviders: string[],
    providerDetails?: Array<{ id: string; provider: string }>
  ): void {
    const filters: Partial<GameFiltersState> = { selectedProviders };
    if (providerDetails) {
      filters.providerDetails = providerDetails;
    }
    this.setFilters(filters);
  }

  /**
   * 清空筛选状态
   */
  static clearFilters(): void {
    this.setFilters({
      searchValue: "",
      selectedProviders: ["all"],
      providerDetails: [],
    });
  }

  /**
   * 完全删除存储的筛选状态
   */
  static removeFilters(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error("清除游戏筛选状态失败:", error);
    }
  }

  /**
   * 检查当前路由是否需要保持筛选状态
   */
  static shouldKeepFilters(currentPath: string): boolean {
    return this.VALID_ROUTES.some((route) => currentPath.startsWith(route));
  }

  /**
   * 根据当前路由决定是否清理状态
   */
  static checkAndCleanup(currentPath: string): void {
    if (!this.shouldKeepFilters(currentPath)) {
      this.removeFilters();
    }
  }

  /**
   * 从 URL 查询参数初始化筛选状态
   */
  static initFromQuery(query: Record<string, any>): void {
    const { search, providerIds } = query;
    const filters: Partial<GameFiltersState> = {};

    // 处理搜索参数
    if (search && typeof search === "string") {
      filters.searchValue = search;
    }

    // 处理厂商筛选参数
    if (providerIds) {
      const providers = Array.isArray(providerIds)
        ? providerIds.flatMap((id) => String(id).split(",")).filter((id) => id.trim())
        : String(providerIds)
            .split(",")
            .filter((id) => id.trim());

      if (providers.length > 0) {
        filters.selectedProviders = providers;
      }
    }

    // 如果有参数需要更新，则保存
    if (Object.keys(filters).length > 0) {
      this.setFilters(filters);
    }
  }
}
