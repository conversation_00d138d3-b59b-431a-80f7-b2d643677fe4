<template>
  <div class="bonus-container" @click="handleClick">
    <img src="@/assets/images/redEnvelope/BG.png" alt="" />
    <img class="bonus-wz scale-animate" src="@/assets/images/redEnvelope/WZ.png" alt="" />
    <img class="bonus-an scale-animate1" src="@/assets/images/redEnvelope/an.png" alt="" />
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore, POP_FORM } from "@/stores/autoPopMgr";

const autoPopMgrStore = useAutoPopMgrStore();

const handleClick = async () => {

  // 确保数据已加载
  if (!autoPopMgrStore.envelopeInfo || Object.keys(autoPopMgrStore.envelopeInfo).length === 0) {
    console.log('EnvelopeInfo 数据为空，重新获取数据');
    await autoPopMgrStore.getEnvelopeData();
  }

  autoPopMgrStore.showEnvelopeTip = true;
  autoPopMgrStore.sourceEnvelope = POP_FORM.CLICK;
};
</script>

<style scoped lang="scss">
.bonus-container {
  position: relative;
  width: 80px;
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  .bonus-wz {
    position: absolute;
    top: 22px;
    left: 23px;
    width: 35px;
  }

  .bonus-an {
    position: absolute;
    top: 30px;
    left: 30px;
    width: 20px;
  }
}

.scale-animate {
  animation: scalePulse 1.2s infinite ease-in-out;
}

@keyframes scalePulse {
  0% {
    transform: scale(1.15);
  }

  50% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.15);
  }
}

.scale-animate1 {
  animation: scalePulse1 1.2s infinite ease-in-out;
}

@keyframes scalePulse1 {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.15);
  }

  100% {
    transform: scale(1);
  }
}
</style>
