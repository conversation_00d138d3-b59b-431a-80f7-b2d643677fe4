<!-- 验证登录密码 -->
<template>
  <ZActionSheet
    v-model="visible"
    title="Verify Login Password"
    :showCancelButton="false"
    :onConfirm="handleConfirm"
    :onCancel="handleCancel"
  >
    <div class="dialog-content">
      <div class="phone-input">
        <div class="set-password-tip">
          <div>Input your password</div>
        </div>
        <ZPasswordInput2 v-model="loginPassword" />
        <div v-show="errorMessage" class="error-tip">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, onUnmounted } from "vue";
import { hasNumberAndCharacter } from "@/utils/core/tools";
import ZPasswordInput2 from "@/components/ZPasswordInput2/index.vue";
import { Md5 } from "@/utils/core/Md5";
import { setPWD } from "@/api/user";
import { showToast } from "vant";

const props = defineProps({
  // 显示弹窗
  showDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
  // 成功提示
  toastText: {
    type: String,
    default: "Payment password set successfully",
    required: false,
  },
  // 忘记密码
  phone: {
    type: String,
    default: "",
    required: false,
  },
  hideDialogOnVaildateSuccess: {
    type: String,
    default: "",
    required: true,
  },
});

// 全局状态
const globalStore = useGlobalStore();
const userInfo = ref(globalStore.userInfo);
// 响应式数据
const loginPassword = ref(""); // 登陆密码（第一次）
const errorMessage = ref("");
// 弹窗是否显示
const visible = ref(props.showDialog);

const resetData = () => {
  loginPassword.value = "";
  errorMessage.value = "";
};

watch(
  () => props.showDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      resetData();
    }
  }
);

// 监听 visible 变化，同步到父组件
watch(
  () => visible.value,
  (val) => {
    if (val !== props.showDialog) {
      emit("update:showDialog", val);
    }
  }
);

const emit = defineEmits(["update:showDialog", "complete"]);

const handleCancel = () => {
  emit("update:showDialog", false);
};

/**
 * 通用极验校验函数
 * @param loginType 极验类型
 * @param callback  校验成功后的回调
 */
const geetestValidate = (loginType: string, callback: Function) => {
  if (!loginType) return;

  GeetestMgr.instance.geetest_device(loginType, (successRes) => {
    if (successRes) {
      callback({
        geetest_guard: successRes.geetest_guard || "",
        userInfo: successRes.userInfo || "",
        geetest_captcha: successRes.geetest_captcha || "",
        buds: successRes.buds || "64",
      });
    }
  });
};

const vaildErrMsg = (inputValue) => {
  let err = "";
  if (!inputValue) {
    err = "Password can not be empty";
  } else if (
    inputValue.length < 8 ||
    inputValue.length > 20 ||
    !hasNumberAndCharacter(inputValue)
  ) {
    err = "The login password is 8~20 characters and must contain letters and numbers.";
  }
  return err;
};

const handleConfirm = () => {
  let errMsg = vaildErrMsg(loginPassword.value);
  errorMessage.value = errMsg;
  if (errMsg) return;
  // 关闭当前弹窗
  if (props.hideDialogOnVaildateSuccess) {
    emit("update:showDialog", false);
  }

  // 发出完成事件
  emit("complete");

  // 调用成功回调
  props.succCallBack && props.succCallBack(loginPassword.value);
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding-bottom: 20px;

  .set-password-tip {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;
    /* 171.429% */
  }

  // 密码步骤样式
  .phone-input {
    .error-tip {
      color: #e5110a;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style>
