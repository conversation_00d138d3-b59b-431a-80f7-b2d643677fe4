#!/usr/bin/env node

/**
 * 简化的打包上传脚本
 * 使用方法: node deploy-simple.js [环境]
 * 环境选项: dev, test, pre, prod (默认: test)
 */
const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// 配置
const CONFIG = {
  server: {
    user: "luyi",
    host: "**************",
    path: "/usr/share/nginx/demo",
  },
  environments: ["dev", "test", "pre", "prod"],
  buildCommands: {
    dev: "npm run build:dev",
    test: "npm run build:test",
    pre: "npm run build:pre",
    prod: "npm run build:prod",
  },
};

// 颜色输出
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m",
  bold: "\x1b[1m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, "red");
}

function logSuccess(message) {
  log(`✅ ${message}`, "green");
}

function logInfo(message) {
  log(`ℹ️  ${message}`, "blue");
}

function logWarning(message) {
  log(`⚠️  ${message}`, "yellow");
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = (bytes / Math.pow(k, i)).toFixed(1);
  return `${size} ${sizes[i]}`;
}

// 获取目录大小
function getDirSize(dirPath) {
  let totalSize = 0;

  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    if (stats.isFile()) {
      totalSize += stats.size;
    } else if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach((file) => {
        calculateSize(path.join(currentPath, file));
      });
    }
  }

  try {
    calculateSize(dirPath);
    return totalSize;
  } catch {
    return 0;
  }
}

// 构建项目
function buildProject(environment) {
  logInfo(`开始构建项目 (${environment} 环境)...`);

  const buildCommand = CONFIG.buildCommands[environment];
  if (!buildCommand) {
    logError(`未知环境: ${environment}`);
    process.exit(1);
  }

  try {
    execSync(buildCommand, { stdio: "inherit" });
    logSuccess("项目构建完成");
  } catch (error) {
    logError("项目构建失败");
    process.exit(1);
  }
}

// 检查构建结果
function checkBuildResult() {
  if (!fs.existsSync("dist")) {
    logError("构建失败，dist 目录不存在");
    process.exit(1);
  }

  const files = fs.readdirSync("dist");
  if (files.length === 0) {
    logError("构建失败，dist 目录为空");
    process.exit(1);
  }

  const totalSize = getDirSize("dist");
  logSuccess(`构建完成 - ${files.length} 个文件，总大小: ${formatFileSize(totalSize)}`);
}

// 上传到服务器
function uploadToServer() {
  logInfo("开始上传文件到服务器...");

  const { user, host, path: remotePath } = CONFIG.server;
  const rsyncCommand = `rsync -azvp --delete dist/ ${user}@${host}:${remotePath}/`;

  console.log("");
  logInfo(`📤 目标服务器: ${user}@${host}`);
  logInfo(`📁 目标路径: ${remotePath}/`);
  logInfo(`📦 本地路径: dist/`);
  console.log("");
  log(`执行命令: ${rsyncCommand}`, "cyan");
  console.log("");
  logWarning("⚠️  需要输入服务器密码");
  console.log("");

  try {
    execSync(rsyncCommand, { stdio: "inherit" });
    console.log("");
    logSuccess("文件上传成功！");
  } catch (error) {
    console.log("");
    logError("文件上传失败");

    if (error.message.includes("Permission denied")) {
      logError("权限被拒绝 - 请检查用户名和密码");
    } else if (error.message.includes("Connection refused")) {
      logError("连接被拒绝 - 请检查服务器地址");
    } else if (error.message.includes("Host key verification failed")) {
      logError("主机密钥验证失败");
      logInfo(`解决方法: ssh-keygen -R ${host}`);
    } else {
      logError(`错误详情: ${error.message}`);
    }

    console.log("");
    logWarning("💡 提示：配置 SSH 密钥可避免输入密码");
    logInfo(`配置方法: ssh-copy-id ${user}@${host}`);
    process.exit(1);
  }
}

// 主函数
function main() {
  const environment = process.argv[2] || "test";

  // 验证环境参数
  if (!CONFIG.environments.includes(environment)) {
    logError(`无效的环境参数: ${environment}`);
    logInfo(`使用方法: node deploy-simple.js [${CONFIG.environments.join("|")}]`);
    process.exit(1);
  }

  console.log("");
  log(`🚀 开始部署到 ${environment} 环境...`, "bold");
  console.log("");

  const startTime = Date.now();

  try {
    // 1. 构建项目
    buildProject(environment);

    // 2. 检查构建结果
    checkBuildResult();

    // 3. 上传到服务器
    uploadToServer();

    // 4. 完成
    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    console.log("");
    logSuccess(`🎉 部署完成！耗时 ${duration}s`);
    logInfo(`🌐 访问地址: http://${CONFIG.server.host}`);
  } catch (error) {
    logError(`部署失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行
main();
