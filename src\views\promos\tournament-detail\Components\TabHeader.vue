<template>
  <div class="modal-header">
    <div class="leaderboard-tabs">
      <div class="active-bg" :class="activeTab === tabs[0] ? 'active-left' : 'active-right'"></div>
      <button
        :class="{ active: activeTab === tabs[0] }"
        class="tab-btn"
        @click="$emit('update:activeTab', tabs[0])"
      >
        Game Distribution
      </button>
      <button
        :class="{ active: activeTab === tabs[1] }"
        class="tab-btn"
        @click="$emit('update:activeTab', tabs[1])"
      >
        Betting Summary
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  activeTab: string;
  tabs: string[];
}

defineProps<Props>();

defineEmits<{
  "update:activeTab": [tab: string];
}>();
</script>

<style scoped lang="scss">
.modal-header {
  position: relative;
  z-index: 1;
}

.leaderboard-tabs {
  display: flex;
  position: relative;
  width: 100%;
  height: 52px;
  background: linear-gradient(90deg, #194afc 10%, #7700ff 50%, #194afc 90%);
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  border: 0;
}

.active-bg {
  // width: 183px;
  width: 55%;
  position: absolute;
  z-index: 2;
  height: 52px;
  background: url("@/assets/images/tournament/activeTab-bg.png") no-repeat;
  background-size: 100% 100%;

  &.active-left {
    left: 0;
    top: 0;
    transform: scaleX(-1);
  }

  &.active-right {
    right: 0;
    top: 0;
  }
}

.tab-btn {
  position: relative;
  z-index: 3;
  border-radius: 16px 16px 0 0;
  line-height: 52px;
  flex: 1;
  border: none;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-weight: 500;
  background: transparent;
  outline: none;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    color: #333;
    font-weight: 600;
    border-radius: 0 0 0 20px;
  }

  &:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>
