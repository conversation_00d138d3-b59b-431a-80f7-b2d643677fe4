<template>
  <div class="example-container">
    <h2>PhoneNumberInput 组件示例</h2>
    
    <!-- 基础使用 -->
    <div class="example-section">
      <h3>1. 基础使用</h3>
      <PhoneNumberInput 
        v-model="basicPhone"
        label="Your phone number (09xx xxxx xxx)"
        placeholder="Enter phone number"
      />
      <p>当前值: {{ basicPhone }}</p>
    </div>

    <!-- 带验证的使用 -->
    <div class="example-section">
      <h3>2. 带验证功能</h3>
      <PhoneNumberInput 
        v-model="validatedPhone"
        label="Phone Number with Validation"
        :required="true"
        :validateOnInput="true"
        @validityChange="onValidityChange"
        :errorMessages="{
          required: '请输入手机号',
          invalid: '手机号格式不正确'
        }"
      />
      <p>当前值: {{ validatedPhone }}</p>
      <p>是否有效: {{ isValid ? '✅' : '❌' }}</p>
      <button :disabled="!isValid" @click="submitForm">
        提交表单
      </button>
    </div>

    <!-- 自动保存功能 -->
    <div class="example-section">
      <h3>3. 自动保存功能</h3>
      <PhoneNumberInput 
        v-model="autoSavePhone"
        label="Auto Save Phone"
        :autoSave="true"
        storageKey="demo_phone"
        placeholder="会自动保存到本地存储"
      />
      <p>当前值: {{ autoSavePhone }}</p>
    </div>

    <!-- 禁用状态 -->
    <div class="example-section">
      <h3>4. 禁用状态</h3>
      <PhoneNumberInput 
        v-model="disabledPhone"
        label="Disabled Phone Input"
        :disabled="true"
        placeholder="This is disabled"
      />
    </div>

    <!-- 使用组件方法 -->
    <div class="example-section">
      <h3>5. 组件方法调用</h3>
      <PhoneNumberInput 
        ref="phoneInputRef"
        v-model="methodPhone"
        label="Phone with Methods"
      />
      <div class="button-group">
        <button @click="validatePhone">验证</button>
        <button @click="clearPhone">清空</button>
        <button @click="focusPhone">聚焦</button>
        <button @click="checkValid">检查有效性</button>
      </div>
      <p>当前值: {{ methodPhone }}</p>
    </div>

    <!-- 在弹窗中使用 -->
    <div class="example-section">
      <h3>6. 弹窗中使用</h3>
      <button @click="showDialog = true">打开弹窗</button>
      
      <div v-if="showDialog" class="dialog-overlay" @click="showDialog = false">
        <div class="dialog-content" @click.stop>
          <h4>绑定手机号</h4>
          <PhoneNumberInput 
            v-model="dialogPhone"
            label="Your phone number (09xx xxxx xxx)"
            :required="true"
            :validateOnBlur="true"
            @validityChange="handleDialogValidityChange"
          />
          <div class="dialog-buttons">
            <button @click="showDialog = false">取消</button>
            <button 
              :disabled="!isDialogPhoneValid" 
              @click="confirmDialog"
            >
              确认
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PhoneNumberInput from './index.vue';

// 基础使用
const basicPhone = ref('');

// 带验证的使用
const validatedPhone = ref('');
const isValid = ref(false);

const onValidityChange = (valid: boolean) => {
  isValid.value = valid;
};

const submitForm = () => {
  console.log('提交表单，手机号:', validatedPhone.value);
};

// 自动保存功能
const autoSavePhone = ref('');

// 禁用状态
const disabledPhone = ref('9123456789');

// 使用组件方法
const phoneInputRef = ref();
const methodPhone = ref('');

const validatePhone = () => {
  phoneInputRef.value?.validate();
};

const clearPhone = () => {
  phoneInputRef.value?.clear();
};

const focusPhone = () => {
  phoneInputRef.value?.focus();
};

const checkValid = () => {
  const valid = phoneInputRef.value?.isValid();
  alert(`手机号是否有效: ${valid}`);
};

// 弹窗中使用
const showDialog = ref(false);
const dialogPhone = ref('');
const isDialogPhoneValid = ref(false);

const handleDialogValidityChange = (valid: boolean) => {
  isDialogPhoneValid.value = valid;
};

const confirmDialog = () => {
  console.log('确认绑定手机号:', dialogPhone.value);
  showDialog.value = false;
};
</script>

<style scoped lang="scss">
.example-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: Inter, sans-serif;

  h2 {
    color: #333;
    margin-bottom: 30px;
  }

  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #fafafa;

    h3 {
      color: #555;
      margin-bottom: 15px;
      font-size: 16px;
    }

    p {
      margin: 10px 0;
      color: #666;
      font-size: 14px;
    }

    button {
      padding: 8px 16px;
      margin: 5px 5px 5px 0;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      cursor: pointer;
      transition: all 0.2s;

      &:hover:not(:disabled) {
        background: #f0f0f0;
        border-color: #bbb;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .button-group {
      margin: 15px 0;
    }
  }

  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .dialog-content {
      background: white;
      padding: 30px;
      border-radius: 8px;
      min-width: 400px;
      max-width: 90vw;

      h4 {
        margin-bottom: 20px;
        color: #333;
      }

      .dialog-buttons {
        margin-top: 20px;
        text-align: right;

        button {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
