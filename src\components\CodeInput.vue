<template>
  <div class="code-input-container">
    <input
      :id="inputId"
      v-model="internalValue"
      type="number"
      inputmode="numeric"
      :placeholder="placeholder"
      :maxlength="maxLength"
      @input="handleInput"
      @keydown="handleKeyDown"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps({
  // 输入框ID，用于label绑定
  inputId: {
    type: String,
    default: "verCode",
  },
  // 双向绑定值
  modelValue: {
    type: String,
    default: "",
  },
  // 输入框占位符
  placeholder: {
    type: String,
    default: "Enter the code",
  },
  // 最大输入长度
  maxLength: {
    type: Number,
    default: 6,
  },
});

const emit = defineEmits(["update:modelValue", "input", "complete"]);

// 内部值管理
const internalValue = ref(props.modelValue);

// 同步外部值变化
watch(
  () => props.modelValue,
  (val) => {
    internalValue.value = val;
  }
);

// 处理输入
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;

  // 只保留数字
  value = value.replace(/\D/g, "");

  // 限制最大长度
  if (value.length > props.maxLength) {
    value = value.slice(0, props.maxLength);
  }

  // 更新内部值和外部绑定
  internalValue.value = value;
  emit("update:modelValue", value);
  emit("input", value);

  // 当输入达到最大长度时触发完成事件
  if (value.length === props.maxLength) {
    emit("complete", value);
  }
};

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 禁止非数字键（除了退格和删除）
  if (
    !/^\d$/.test(event.key) &&
    !["Backspace", "Delete", "ArrowLeft", "ArrowRight", "Tab"].includes(event.key)
  ) {
    event.preventDefault();
  }
};

// 清空输入
const clearInput = () => {
  internalValue.value = "";
  emit("update:modelValue", "");
  emit("input", "");
};
</script>

<style scoped lang="scss">
.code-input-container {
  position: relative;
  width: 100%;

  input {
    width: 100%;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 12px;
    padding-right: 40px; /* 预留删除按钮空间 */
    outline: none;
    background-color: #f4f7fd;
    font-size: 14px;

    &::placeholder {
      color: #999;
    }

    &:focus {
      border-color: #ac1140; /* 聚焦时高亮边框 */
    }
  }

  .clear-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;

    &:hover svg path {
      stroke: #ac1140; /* hover时变色 */
    }
  }
}
</style>
