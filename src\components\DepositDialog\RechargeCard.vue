<template>
  <div class="recharge-card" :class="cardClasses" :style="cardStyle" @click="handleClick">
    <!-- 图标区域 -->
    <div class="icon-wrapper">
      <WithdrawTypeIcon :type="item.name" :icon="item.icon" :width="iconSize" :height="iconSize" />
    </div>

    <!-- 名称区域 -->
    <div class="name-wrapper">
      <span class="method-name">{{ item.name }}</span>
    </div>

    <!-- 选中状态图标 -->
    <div class="check-wrapper">
      <CheckedUnCheckedIcon :type="item.name" :isChecked="isSelected" :size="checkIconSize" />
    </div>

    <!-- 选中状态指示器 -->
    <div v-if="isSelected" class="selected-indicator" />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useDepositStore } from "@/stores/deposit";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";

// 类型定义
interface RechargeItem {
  name: string;
  icon?: string;
  [key: string]: any;
}

interface Props {
  /** 充值项目数据 */
  item: RechargeItem;
}

const props = defineProps<Props>();

// Store
const depositStore = useDepositStore();
const { curRechangeMethods } = storeToRefs(depositStore);

// 常量
const CARD_THEMES = {
  gcash: {
    backgroundColor: "#4881ed",
    name: "Gcash",
    textColor: "#fff",
  },
  maya: {
    backgroundColor: "#01d46a",
    name: "Maya",
    textColor: "#fff",
  },
  default: {
    backgroundColor: "#01d46a",
    name: "Default",
    textColor: "#fff",
  },
} as const;
// 计算属性
const isSelected = computed(() => curRechangeMethods.value === props.item.name);

const cardTheme = computed(() => {
  return CARD_THEMES[props.item.name?.toLocaleLowerCase()] || CARD_THEMES.default;
});

const cardClasses = computed(() => ({
  "is-selected": isSelected.value,
  "is-gcash": props.item.name === CHANEL_TYPE.G_CASH,
  "is-maya": props.item.name === CHANEL_TYPE.MAYA,
  "is-clickable": !isSelected.value,
}));

const cardStyle = computed(() => ({
  backgroundColor: cardTheme.value.backgroundColor,
  color: cardTheme.value.textColor,
}));

const iconSize = computed(() => 36);
const checkIconSize = computed(() => 20);

// 方法
const handleClick = () => {
  if (isSelected.value) return;

  depositStore.setCurReChangeName(props.item.name);
};
</script>

<style lang="scss" scoped>
.recharge-card {
  width: 160px;
  height: 68px;
  font-size: 18px;
  text-align: center;
  border-radius: 20px;
  border: 1px solid #ddd;
  vertical-align: middle;
  display: flex;
  align-items: center;
  padding: 10px;
  color: #fff;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.is-selected {
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
  }

  .name-wrapper {
    flex: 1;
    text-align: left;

    .method-name {
      font-size: 16px;
    }
  }

  .check-wrapper {
    position: absolute;
    right: 10px;
    bottom: 6px;
  }

  .selected-indicator {
    display: none;
  }
}
</style>
