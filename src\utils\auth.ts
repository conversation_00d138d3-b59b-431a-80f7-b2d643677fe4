import { useGlobalStore, GLOBAL_STORE } from "@/stores/global";
import { getLocalStorage, setLocalStorage, removeLocalStorage } from "@/utils/core/Storage";

// 获取 token - 优先从 globalStore 获取，确保获取到最新值
export const getToken = (): string => {
  try {
    // 优先从 globalStore 获取最新的 token
    const globalStore = useGlobalStore();
    if (globalStore.token) {
      return globalStore.token;
    }

    // 如果 globalStore 中没有，则从 localStorage 获取
    const store = getLocalStorage(GLOBAL_STORE);
    if (!store) return "";
    return store?.token || "";
  } catch {
    return "";
  }
};

// 移除 token
export const removeToken = (): void => {
  const globalStore = useGlobalStore();
  globalStore.loginOut();
};
