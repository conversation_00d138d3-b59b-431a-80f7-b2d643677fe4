<template>
  <div class="wallet-reward-btn" @click="handleClick" :style="cssVars">
    <!-- 加载状态 -->
    <div v-if="!imagesLoaded" class="loading-indicator">
      <div class="loading-spinner"></div>
    </div>

    <!-- 图片序列动画 -->
    <div
      v-show="imagesLoaded"
      ref="animationContainer"
      class="frame-animation"
      :class="{ playing: isAnimating }"
    >
      <img
        v-for="(imageSrc, index) in imageList"
        :key="`${props.state}-${index}`"
        :src="imageSrc"
        :class="{ active: currentFrame === index }"
        class="frame-image"
        alt=""
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";

// 导入不同状态的图片
const importImages = (state = "claim") => {
  const images = [];
  for (let i = 0; i <= 36; i++) {
    const frameNumber = i.toString().padStart(2, "0");
    images.push(
      new URL(`../../../assets/icons/${state}/1_${frameNumber}.png`, import.meta.url).href
    );
  }
  return images;
};

// Props
const props = defineProps({
  // 动画状态
  state: {
    type: String,
    default: "standby",
    validator: (value) => ["claim", "standby"].includes(value),
  },
  // 是否启用动画
  enableAnimation: {
    type: Boolean,
    default: true,
  },
  defaultSize: {
    type: Number,
    default: 0,
  },
  // 动画配置
  animationConfig: {
    type: Object,
    default: () => ({
      width: 24, // 动画宽度
      height: 24, // 动画高度
      fps: 12, // 帧率
      loop: true, // 是否循环
      autoplay: false, // 是否自动播放
    }),
  },
});

// Emits
const emit = defineEmits(["click", "animation-start", "animation-end"]);

// 响应式数据
const animationContainer = ref(null);
const isAnimating = ref(false);
const currentFrame = ref(0);
const animationTimer = ref(null);
const imagesLoaded = ref(false);
const currentImageList = ref([]);

// 计算属性
const imageList = computed(() => {
  return importImages(props.state);
});

const cssVars = computed(() => {
  const { width, height } = props.defaultSize
    ? {
        width: props.defaultSize,
        height: props.defaultSize,
      }
    : props.animationConfig;
  return {
    "--animation-width": `${width}px`,
    "--animation-height": `${height}px`,
  };
});

// 播放帧动画
const playFrameAnimation = () => {
  if (!props.enableAnimation || !imagesLoaded.value) return;

  const { fps, loop } = props.animationConfig;
  const frameInterval = 1000 / fps; // 每帧间隔时间
  const totalFrames = imageList.value.length;

  // 确保从第一帧开始
  currentFrame.value = 0;
  isAnimating.value = true;
  emit("animation-start");

  // 使用 requestAnimationFrame 来确保更平滑的动画
  let lastTime = 0;
  let frameCount = 0;

  const animate = (currentTime) => {
    if (!isAnimating.value) return;

    if (currentTime - lastTime >= frameInterval) {
      frameCount++;
      currentFrame.value = frameCount % totalFrames;

      if (!loop && frameCount >= totalFrames) {
        stopAnimation();
        return;
      }

      lastTime = currentTime;
    }

    animationTimer.value = requestAnimationFrame(animate);
  };

  animationTimer.value = requestAnimationFrame(animate);
};

// 停止动画
const stopAnimation = () => {
  if (animationTimer.value) {
    cancelAnimationFrame(animationTimer.value);
    animationTimer.value = null;
  }
  isAnimating.value = false;
  currentFrame.value = 0;
  emit("animation-end");
};

// 暂停动画
const pauseAnimation = () => {
  if (animationTimer.value) {
    cancelAnimationFrame(animationTimer.value);
    animationTimer.value = null;
  }
  isAnimating.value = false;
};

// 处理点击事件
const handleClick = () => {
  emit("click");
  //   点击停止动画
  /*  if (props.enableAnimation) {
    if (isAnimating.value) {
      pauseAnimation();
    } else {
      playFrameAnimation();
    }
  } */
};

// 监听状态变化
watch(
  () => props.state,
  async (newState, oldState) => {
    if (newState !== oldState) {
      console.log(`状态从 ${oldState} 切换到 ${newState}`);

      // 停止当前动画
      stopAnimation();

      // 重置加载状态
      imagesLoaded.value = false;
      currentFrame.value = 0;

      // 重新加载新状态的图片
      await preloadImages();
    }
  }
);

// 预加载所有图片
const preloadImages = async () => {
  const promises = imageList.value.map((src) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
      img.src = src;
    });
  });

  try {
    await Promise.all(promises);
    imagesLoaded.value = true;
    console.log("所有图片预加载完成");

    // 如果设置了自动播放，在图片加载完成后播放
    if (props.animationConfig.autoplay) {
      playFrameAnimation();
    }
  } catch (error) {
    console.error("图片预加载失败:", error);
  }
};

// 生命周期
onMounted(() => {
  preloadImages();
});

onUnmounted(() => {
  stopAnimation();
});

// 暴露方法
defineExpose({
  playAnimation: playFrameAnimation,
  stopAnimation,
  pauseAnimation,
});
</script>

<style scoped lang="scss">
.wallet-reward-btn {
  position: relative;
  display: inline-block;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;

  // 悬停效果
  &:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
  }

  // 激活效果
  &:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  // 加载指示器
  .loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--animation-width, 24px);
    height: var(--animation-height, 24px);

    .loading-spinner {
      width: 50%;
      height: 50%;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  // 帧动画容器
  .frame-animation {
    position: relative;
    width: var(--animation-width, 24px);
    height: var(--animation-height, 24px);
    overflow: hidden;

    .frame-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      opacity: 0;
      visibility: hidden;
      transform: translateZ(0); // 启用硬件加速
      backface-visibility: hidden; // 避免闪烁
      -webkit-backface-visibility: hidden;

      &.active {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}

// 动画定义
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .wallet-reward-btn {
    &:hover {
      transform: none;
    }

    &:active {
      transform: none;
    }

    .frame-animation .frame-image {
      transition: none;
    }
  }
}
</style>
