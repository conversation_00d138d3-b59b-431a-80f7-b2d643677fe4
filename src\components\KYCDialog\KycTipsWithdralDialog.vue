<template>
  <ZDialog
    v-model="showKycWithdralDialog"
    title="KYC Verification"
    :show-close="true"
    confirm-text="Continue"
    cancel-text="Verify Now"
    :on-confirm="handleConfirm"
    :on-cancel="handleCancel"
    :showCancelButton="showCancelBtn"
    :showConfirmButton="showConfirmBtn"
  >
    <div class="content">
      <div class="title">
        <span class="text">{{ descTitle }}</span>
      </div>
      <div class="desc">Complete your KYC before withdrawal.</div>
    </div>
  </ZDialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from "vue";
import { storeToRefs } from "pinia";
import { useWithdrawStore } from "@/stores/withdraw";
import { useKycMgrStore } from "@/stores/kycMgr";

const withdrawStore = useWithdrawStore();
const kycMgrStore = useKycMgrStore();

const { showKycWithdralDialog, riskWithdrawalInfo, riskWithdrawalCallBack } =
  storeToRefs(withdrawStore);

// 状态0未认证 1已认证 2审核中 3拒绝
const descTitle = computed(() => {
  if (riskWithdrawalInfo.value?.kyc_step == 0 || riskWithdrawalInfo.value?.kyc_step == 100) {
    return "Your account is not yet fully verified!";
  } else if (riskWithdrawalInfo.value?.kyc_step == 2) {
    return "We are reviewing your application.";
  } else if (riskWithdrawalInfo.value?.kyc_step == 3) {
    return "Review failed!";
  }
  return "";
});

// Verify Now
const showCancelBtn = computed(() => {
  if (riskWithdrawalInfo.value?.kyc_status == 1) {
    return true;
  }
  return false;
});
// Continue
const showConfirmBtn = computed(() => {
  if (riskWithdrawalInfo.value?.kyc_status == 2) {
    if (riskWithdrawalInfo.value?.kyc_step == 100) {
      return false;
    }
  }
  return true;
});

const handleConfirm = async () => {
  withdrawStore.showKycWithdralDialog = false;
  kycMgrStore.checkPreconditions();
};
const handleCancel = () => {
  withdrawStore.showKycWithdralDialog = false;
  if (riskWithdrawalCallBack.value) {
    riskWithdrawalCallBack.value();
  }
};
</script>

<style lang="scss" scoped>
.content {
  .title {
    text-align: center;
    margin-bottom: 12px;
    .text {
      font-size: 18px;
      color: #222;
      font-weight: 700;
      color: #ac1140;
      background: #fcf3f5;
      padding: 5px 15px;
      border-radius: 30px;
    }
  }
  .desc {
    font-size: 14px;
    color: #222;
    text-align: center;
  }
}
</style>
