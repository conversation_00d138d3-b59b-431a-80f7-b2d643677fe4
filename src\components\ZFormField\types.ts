/**
 * ZFormField 组件类型定义
 */

// 输入框类型
export type InputType = 
  | 'text' 
  | 'number' 
  | 'digit' 
  | 'tel' 
  | 'email' 
  | 'password' 
  | 'search' 
  | 'url'
  | 'textarea';

// 输入模式
export type InputMode = 
  | 'text' 
  | 'numeric' 
  | 'decimal' 
  | 'tel' 
  | 'email' 
  | 'url' 
  | 'search';

// 输入限制类型
export type InputRestriction = 
  | 'none'          // 无限制
  | 'alphanumeric'  // 字母和数字
  | 'alphabetic'    // 只允许字母
  | 'numeric'       // 只允许数字
  | 'phone'         // 手机号格式
  | 'email'         // 邮箱字符
  | 'chinese'       // 中文字符
  | 'english'       // 英文字符
  | 'noSpaces'      // 不允许空格
  | 'noSpecial';    // 不允许特殊字符

// 验证规则接口
export interface ValidationRule {
  /** 是否必填 */
  required?: boolean;
  /** 正则表达式验证 */
  pattern?: RegExp;
  /** 最小值（数字类型） */
  min?: number;
  /** 最大值（数字类型） */
  max?: number;
  /** 最小长度 */
  minLength?: number;
  /** 最大长度 */
  maxLength?: number;
  /** 自定义验证函数 */
  validator?: (value: string) => boolean | string;
  /** 错误提示信息 */
  message?: string;
  /** 验证触发时机 */
  trigger?: 'input' | 'blur' | 'change';
}

// 表单字段配置接口
export interface FormFieldConfig {
  /** 字段名称 */
  name: string;
  /** 字段标签 */
  label: string;
  /** 字段类型 */
  type?: InputType;
  /** 输入限制 */
  restriction?: InputRestriction;
  /** 占位符 */
  placeholder?: string;
  /** 是否必填 */
  required?: boolean;
  /** 验证规则 */
  rules?: ValidationRule[];
  /** 最大长度 */
  maxLength?: number;
  /** 最小长度 */
  minLength?: number;
  /** 默认值 */
  defaultValue?: string | number;
  /** 帮助文本 */
  helpText?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否只读 */
  readonly?: boolean;
}

// 验证结果接口
export interface ValidationResult {
  /** 是否验证通过 */
  isValid: boolean;
  /** 错误信息 */
  message?: string;
  /** 字段名称 */
  field?: string;
}

// 表单验证结果接口
export interface FormValidationResult {
  /** 是否全部验证通过 */
  isValid: boolean;
  /** 错误信息列表 */
  errors: ValidationResult[];
  /** 第一个错误字段 */
  firstErrorField?: string;
}

// 输入过滤器函数类型
export type InputFilter = (value: string) => string;

// 常用的输入过滤器
export const InputFilters = {
  /** 转换为大写 */
  toUpperCase: (value: string) => value.toUpperCase(),
  
  /** 转换为小写 */
  toLowerCase: (value: string) => value.toLowerCase(),
  
  /** 移除空格 */
  removeSpaces: (value: string) => value.replace(/\s/g, ''),
  
  /** 移除特殊字符 */
  removeSpecialChars: (value: string) => value.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, ''),
  
  /** 只保留数字 */
  numbersOnly: (value: string) => value.replace(/[^0-9]/g, ''),
  
  /** 只保留字母 */
  lettersOnly: (value: string) => value.replace(/[^a-zA-Z]/g, ''),
  
  /** 格式化手机号 */
  formatPhone: (value: string) => {
    const numbers = value.replace(/[^0-9]/g, '');
    if (numbers.length <= 3) return numbers;
    if (numbers.length <= 6) return `${numbers.slice(0, 3)}-${numbers.slice(3)}`;
    return `${numbers.slice(0, 3)}-${numbers.slice(3, 6)}-${numbers.slice(6, 10)}`;
  },
  
  /** 格式化银行卡号 */
  formatBankCard: (value: string) => {
    const numbers = value.replace(/[^0-9]/g, '');
    return numbers.replace(/(.{4})/g, '$1 ').trim();
  },
};

// 常用的验证规则
export const ValidationRules = {
  /** 必填验证 */
  required: (message = 'This field is required'): ValidationRule => ({
    required: true,
    message,
  }),
  
  /** 邮箱验证 */
  email: (message = 'Please enter a valid email address'): ValidationRule => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message,
  }),
  
  /** 手机号验证 */
  phone: (message = 'Please enter a valid phone number'): ValidationRule => ({
    pattern: /^[0-9]{10}$/,
    message,
  }),
  
  /** 密码强度验证 */
  strongPassword: (message = 'Password must contain letters and numbers'): ValidationRule => ({
    pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/,
    message,
  }),
  
  /** 长度范围验证 */
  length: (min: number, max: number, message?: string): ValidationRule => ({
    minLength: min,
    maxLength: max,
    message: message || `Length must be between ${min} and ${max} characters`,
  }),
  
  /** 数字范围验证 */
  range: (min: number, max: number, message?: string): ValidationRule => ({
    min,
    max,
    message: message || `Value must be between ${min} and ${max}`,
  }),
  
  /** 自定义正则验证 */
  pattern: (regex: RegExp, message: string): ValidationRule => ({
    pattern: regex,
    message,
  }),
};

// 输入限制映射
export const InputRestrictionMap: Record<InputRestriction, RegExp> = {
  none: /.*/,
  alphanumeric: /[^a-zA-Z0-9]/g,
  alphabetic: /[^a-zA-Z]/g,
  numeric: /[^0-9]/g,
  phone: /[^0-9]/g,
  email: /[^a-zA-Z0-9@._-]/g,
  chinese: /[^\u4e00-\u9fa5]/g,
  english: /[^a-zA-Z]/g,
  noSpaces: /\s/g,
  noSpecial: /[^a-zA-Z0-9\u4e00-\u9fa5]/g,
};

// 组件事件类型
export interface ZFormFieldEvents {
  'update:modelValue': (value: string | number) => void;
  'input': (value: string) => void;
  'blur': (event: Event) => void;
  'focus': (event: Event) => void;
  'clear': () => void;
  'validate': (isValid: boolean, message?: string) => void;
}

// 组件实例方法类型
export interface ZFormFieldMethods {
  /** 手动验证 */
  validate: () => boolean;
  /** 清除验证状态 */
  clearValidation: () => void;
  /** 聚焦输入框 */
  focus: () => void;
  /** 失焦输入框 */
  blur: () => void;
}

// 组件 Props 类型
export interface ZFormFieldProps {
  /** 双向绑定的值 */
  modelValue?: string | number;
  /** 输入框类型 */
  type?: InputType;
  /** 输入模式 */
  inputmode?: InputMode;
  /** 标签文本 */
  label?: string;
  /** 占位符 */
  placeholder?: string;
  /** 最大长度 */
  maxLength?: number;
  /** 最小长度 */
  minLength?: number;
  /** 是否必填 */
  required?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /** 是否可清除 */
  clearable?: boolean;
  /** 是否显示字数统计 */
  showWordLimit?: boolean;
  /** 是否显示错误信息 */
  showErrorMessage?: boolean;
  /** 帮助文本 */
  helpText?: string;
  /** 输入限制类型 */
  restriction?: InputRestriction;
  /** 自定义验证规则 */
  rules?: ValidationRule[];
  /** 是否在输入时验证 */
  validateOnInput?: boolean;
  /** 是否在失焦时验证 */
  validateOnBlur?: boolean;
  /** 自定义输入过滤器 */
  inputFilter?: InputFilter;
}
