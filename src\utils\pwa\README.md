# PWA 模块

这个模块提供了完整的 PWA (Progressive Web App) 功能支持。

## 功能特性

### 🚀 核心功能
- **Service Worker 管理**: 自动注册、更新检测、离线缓存
- **安装提示管理**: 处理浏览器的安装提示事件
- **安装状态检测**: 检测应用是否已安装为 PWA
- **弹窗控制**: 智能的弹窗显示逻辑和频率控制

### 📱 支持检测
- Service Worker 支持
- Web App Manifest 支持
- Standalone 模式检测
- 安装提示事件支持

## API 文档

### 初始化函数

#### `initPWA()`
初始化 PWA 功能，包含 Service Worker 注册和安装提示管理。

```typescript
import { initPWA } from '@/utils/pwa';

const pwaResult = initPWA();
// 返回: { updateSW, getDeferredPrompt, clearDeferredPrompt }
```

#### `checkPWASupport()`
检查当前环境的 PWA 支持情况。

```typescript
import { checkPWASupport } from '@/utils/pwa';

const support = checkPWASupport();
// 返回: { serviceWorker, manifest, standalone, beforeInstallPrompt }
```

### 安装相关函数

#### `isPWAInstalled()`
检测应用是否已安装为 PWA。

```typescript
import { isPWAInstalled } from '@/utils/pwa';

if (isPWAInstalled()) {
  console.log('应用已安装为 PWA');
}
```

#### `getDeferredPrompt()`
获取当前的安装提示事件。

```typescript
import { getDeferredPrompt } from '@/utils/pwa';

const prompt = getDeferredPrompt();
if (prompt) {
  // 可以触发安装
  await prompt.prompt();
}
```

#### `clearDeferredPrompt()`
清理安装提示引用。

```typescript
import { clearDeferredPrompt } from '@/utils/pwa';

clearDeferredPrompt();
```

### 弹窗控制函数

#### `shouldShowPWAPrompt()`
检查是否应该显示 PWA 安装弹窗。

```typescript
import { shouldShowPWAPrompt } from '@/utils/pwa';

if (shouldShowPWAPrompt()) {
  // 显示安装引导弹窗
}
```

#### `recordPWAPromptShown()`
记录一次弹窗显示。

```typescript
import { recordPWAPromptShown } from '@/utils/pwa';

recordPWAPromptShown();
```

#### `resetPWAPromptCount()`
重置弹窗计数（调试用）。

```typescript
import { resetPWAPromptCount } from '@/utils/pwa';

resetPWAPromptCount();
```

#### `getPWAPromptStats()`
获取当前弹窗统计信息。

```typescript
import { getPWAPromptStats } from '@/utils/pwa';

const stats = getPWAPromptStats();
// 返回: { date, count }
```

## 使用示例

### 在 main.ts 中初始化
```typescript
import { initPWA, checkPWASupport } from '@/utils/pwa';

// 初始化 PWA 功能
initPWA();

// 开发环境下检查支持情况
if (import.meta.env.DEV) {
  checkPWASupport();
}
```

### 在组件中使用
```typescript
import { 
  getDeferredPrompt, 
  clearDeferredPrompt, 
  recordPWAPromptShown 
} from '@/utils/pwa';

// 触发安装
const installPWA = async () => {
  const prompt = getDeferredPrompt();
  if (prompt) {
    await prompt.prompt();
    const result = await prompt.userChoice;
    
    if (result.outcome === 'accepted') {
      console.log('用户接受安装');
    }
    
    clearDeferredPrompt();
  }
};

// 记录弹窗显示
recordPWAPromptShown();
```

## 配置说明

### 弹窗显示规则
- 每天最多显示 3 次
- 已安装的应用不显示
- 自动重置每日计数

### 存储键
- `PWA_COUNT_STATS`: 弹窗统计数据

## 注意事项

1. **HTTPS 要求**: PWA 功能需要在 HTTPS 环境下运行
2. **浏览器支持**: 不同浏览器对 PWA 的支持程度不同
3. **安装提示**: 浏览器会根据用户行为决定是否触发安装提示
4. **开发环境**: 开发环境下某些功能可能受限

## 类型定义

详细的类型定义请参考 `types.ts` 文件。
