/**
 * 最简单的构建配置
 * 移除所有复杂的代码分割，确保稳定性
 */

import type { BuildOptions } from "vite";

/**
 * 获取最简单的构建配置
 */
export function getSimpleBuildConfig(isDrop: boolean): BuildOptions {
  return {
    // 设置最终构建的浏览器兼容目标
    target: "es2015",
    // 使用 esbuild 压缩，比 terser 更快
    minify: "esbuild",
    // 构建后是否生成 source map 文件
    sourcemap: false,
    // CSS 兼容目标
    cssTarget: "chrome80",
    // 启用/禁用 gzip 压缩大小报告（禁用以提升构建速度）
    reportCompressedSize: false,
    // chunk 大小警告的限制（以 kbs 为单位）
    chunkSizeWarningLimit: 2000, // 2MB，宽松限制
    // 自定义底层的 Rollup 打包配置
    rollupOptions: {
      // 静态资源分类打包
      output: {
        // 入口文件名
        entryFileNames: "js/[name]-[hash].js",
        // 代码分割后的文件名
        chunkFileNames: "js/[name]-[hash].js",
        // 静态资源文件名（按类型分类）
        assetFileNames: (assetInfo) => {
          // 使用 names 数组的第一个元素，避免弃用警告
          const name =
            (assetInfo.names && assetInfo.names[0]) || (assetInfo as any).name || "unknown";

          // 字体文件
          if (/\.(woff|woff2|eot|ttf|otf)$/i.test(name)) {
            return "fonts/[name]-[hash].[ext]";
          }
          // 图片文件
          if (/\.(png|jpe?g|gif|svg|webp|ico|bmp)$/i.test(name)) {
            return "images/[name]-[hash].[ext]";
          }
          // CSS 文件
          if (/\.css$/i.test(name)) {
            return "css/[name]-[hash].[ext]";
          }
          // 其他文件
          return "assets/[name]-[hash].[ext]";
        },
        // 完全不使用手动代码分割
        // manualChunks: undefined, // 让 Vite 自动处理
      },
      // 外部依赖，不打包进 bundle（如果需要 CDN）
      external: [],
      // 使用最保守的 Tree Shaking 配置
      treeshake: {
        // 保留所有副作用，确保不会意外移除代码
        moduleSideEffects: true,
        propertyReadSideEffects: true,
        unknownGlobalSideEffects: true,
      },
    },
    // esbuild 压缩选项
    esbuild: {
      // 移除 console 和 debugger（如果需要）
      drop: isDrop ? ["console", "debugger"] : [],
      // 压缩选项
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true,
      // 目标环境
      target: "es2015",
    },
  };
}

/**
 * 获取最简单的依赖预构建配置
 */
export function getSimpleOptimizeDeps() {
  return {
    // 强制预构建的依赖
    include: ["vue", "vue-router", "pinia", "vant", "axios"],
    // 排除预构建的依赖
    exclude: [],
    // 预构建时的 esbuild 选项
    esbuildOptions: {
      target: "es2015",
    },
  };
}

/**
 * 获取最简单的 CSS 配置
 */
export function getSimpleCSSConfig() {
  return {
    // 开启 CSS 代码分割
    codeSplit: true,
    // CSS 预处理器选项
    preprocessorOptions: {
      scss: {
        // 避免重复导入
        additionalData: ``,
        // 压缩输出
        outputStyle: "compressed" as const,
        // 字符集
        charset: false,
      },
    },
  };
}
