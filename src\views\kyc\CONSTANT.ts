export const BRANCH_ENUM = ["NUSTAR Cebu"];

export enum KYC_STEP {
  KYC_STEP_NAME = 0, //提交名字页面
  KYC_STEP_ADDRESS, //提交地址页面
  KYC_STEP_PHOTO, //上传照片页面
  KYC_STEP_MEDIA, //提交媒体账号
}

export const S_INCOME_ENUM = [
  "Salary",
  "Business Proceeds",
  "Allowance",
  "Pension",
  "Remittance",
  "Donation",
  "Inheritance",
  "Government Aid",
  "Loan Proceeds",
  "Interest on Savings, Placements, Investments",
  "Commission",
  "Online Business",
  "Self Employed",
];

export const S_TYPE_ID_ENUM = [
  "Driver’s License",
  "UMID",
  "Postal ID",
  "Passports",
  "SSS ID",
  "PRC ID",
  "HDMF(Pag-IBIG ID)",
  "National ID",
  "ePhil ID",
  "Other ID",
];

export const S_TYPE_ID = [
  "Driver’s License",
  "UMID",
  "Postal ID",
  "Passports",
  "SSS ID",
  "PRC ID",
  "HDMF(Pag-IBIG ID)",
  "National ID",
  "ePhil ID",
  "Other ID",
];

export const S_MEDIA_TYPE = ["Viber", "Telegram", "Facebook Messenger", "Line", "WhatsApp"];
export const NATIONALLITY_ENUM = [
  { nationality: "Filipino", country: "Philippines" },
  { nationality: "Afghan", country: "Afghanistan" },
  { nationality: "Albanian", country: "Angola" },
  { nationality: "Algerian", country: "Anguilla" },
  { nationality: "American", country: "Albania" },
  { nationality: "Andorran", country: "Andorra" },
  { nationality: "Angolan", country: "Netherlands Antilles" },
  { nationality: "Antarctic", country: "United Arab Emirates" },
  { nationality: "Antiguan", country: "Argentina" },
  { nationality: "Argentian/Argentinean", country: "Armenia" },
  { nationality: "Armenian", country: "American Samoa" },
  { nationality: "Aruban", country: "Antarctica" },
  { nationality: "Australian", country: "French Southern Territories Gabon " },
  { nationality: "Austrian", country: "Antigua" },
  { nationality: "Azerbajani", country: "Australia" },
  { nationality: "Bahamian", country: "Austria" },
  { nationality: "Bahraini", country: "Azerbaijian" },
  { nationality: "Bangladeshi", country: "Burundi" },
  { nationality: "Barbadian", country: "Belgium" },
  { nationality: "Belgian", country: "Benin" },
  { nationality: "Belizian", country: "Burkina Faso" },
  { nationality: "Belorussian", country: "Bangladesh" },
  { nationality: "Beninese", country: "Bulgaria" },
  { nationality: "Bermudan", country: "Bahrain" },
  { nationality: "Bhutanese", country: "Bahamas" },
  { nationality: "Bolivian", country: "Bosnia and Herzegovina" },
  { nationality: "Bosnian", country: "Belarus" },
  { nationality: "Botswanan", country: "Belize" },
  { nationality: "Brazilian", country: "Bermuda" },
  { nationality: "British", country: "Bolivia (Plurinational State of)" },
  { nationality: "Bruneian", country: "Brazil" },
  { nationality: "Bulgarian", country: "Barbados" },
  { nationality: "Burkinabe", country: "Brunei Darussalam" },
  { nationality: "Burmese", country: "Bhutan" },
  { nationality: "Burundain", country: "Bouvet Island" },
  { nationality: "Cambodian", country: "Botswana" },
  { nationality: "Cameroonian", country: "Central African Republic" },
  { nationality: "Canadian", country: "Canada" },
  { nationality: "Cape Verdean", country: "Coccs(Keeling) Islands" },
  { nationality: "Central African", country: "Switzerland" },
  { nationality: "Chadian", country: "Chile" },
  { nationality: "Chilean", country: "China" },
  { nationality: "Chinese", country: "Côte d'Ivoire" },
  { nationality: "Colombian", country: "Cameroon" },
  { nationality: "Comoran", country: "Congo" },
  { nationality: "Congolese", country: "Cook Islands" },
  { nationality: "Costa Rican", country: "Colombia" },
  { nationality: "Croatian", country: "Comoros" },
  { nationality: "Cuban", country: "Cape Verde" },
  { nationality: "Cypriot", country: "Costa Rica" },
  { nationality: "Czech", country: "China Taiwan" },
  { nationality: "Danish", country: "Cuba" },
  { nationality: "Dijboutian", country: "Christmas Island" },
  { nationality: "Dominican", country: "Cayman Islands" },
  { nationality: "Dutch", country: "Cyprus" },
  { nationality: "East Timorese", country: "Czech Republic" },
  { nationality: "Ecuadorean", country: "Germany" },
  { nationality: "Egyptian", country: "Djibouti" },
  { nationality: "Eritrean", country: "Dominica" },
  { nationality: "Estonian", country: "Denmark" },
  { nationality: "Ethiopian", country: "Dominican Republic" },
  { nationality: "Fijian", country: "Algeria" },
  { nationality: "Finnish", country: "Ecuador" },
  { nationality: "French", country: "Egypt" },
  { nationality: "Gabonese", country: "Eritrea" },
  { nationality: "Gambian", country: "Western Sahara" },
  { nationality: "Georgian", country: "Spain" },
  { nationality: "German", country: "Estonia" },
  { nationality: "Ghanaian", country: "Ethiopia" },
  { nationality: "Gibraltan", country: "Finland" },
  { nationality: "Greek", country: "Fiji" },
  { nationality: "Guatemalan", country: "Falkland Islands (Malvinas)" },
  { nationality: "Guinean", country: "France" },
  { nationality: "Guinean", country: "Faeroe Islands" },
  { nationality: "Guyanese", country: "Micronesia (Federated States of)" },
  { nationality: "Haitian", country: "Gabon" },
  { nationality: "Honduran", country: "United Kingdom" },
  { nationality: "Hungarian", country: "Georgia" },
  { nationality: "Icelandic", country: "Ghana" },
  { nationality: "Indian", country: "Gibraltar" },
  { nationality: "Indonesian", country: "Guinea" },
  { nationality: "Iranian", country: "Guadeloupe" },
  { nationality: "Iraqi", country: "Gambia" },
  { nationality: "Irish", country: "Guinea-Bissau" },
  { nationality: "Israeli", country: "Equatorial Guinea" },
  { nationality: "Italian", country: "Greece" },
  { nationality: "Jamacian", country: "Grenada" },
  { nationality: "Japanese", country: "Greenland" },
  { nationality: "Jordanian", country: "Guatemala" },
  { nationality: "Kazakh", country: "French Guiana" },
  { nationality: "Kenyan", country: "Guam" },
  { nationality: "Kuwaiti", country: "Guyana" },
  { nationality: "Laotian", country: "Hong Kong" },
  { nationality: "Latvian", country: "Heard Islands and McDonald Islands" },
  { nationality: "Lebanese", country: "Honduras" },
  { nationality: "Liberian", country: "Croatia" },
  { nationality: "Libyan", country: "Haiti" },
  { nationality: "Liechtensteiner", country: "Hungary" },
  { nationality: "Lithuanian", country: "Indonesia" },
  { nationality: "Luxemburger", country: "India" },
  { nationality: "Macedonian", country: "British Indian Ocean Territory" },
  { nationality: "Madagascan", country: "Ireland" },
  { nationality: "Malaysian", country: "Iran (Islamic Republic of)" },
  { nationality: "Maldivian", country: "Iraq" },
  { nationality: "Malian", country: "Iceland" },
  { nationality: "Maltese", country: "Israel" },
  { nationality: "Malawian", country: "Italy" },
  { nationality: "Mauritian", country: "Jamaica" },
  { nationality: "Mexican", country: "Jordan" },
  { nationality: "Micronesian", country: "Japan" },
  { nationality: "Moldovan", country: "Kazakhstan" },
  { nationality: "Monacan", country: "Kenya" },
  { nationality: "Mongolian", country: "Kyrgyzstan" },
  { nationality: "Montserratian", country: "Cambodia" },
  { nationality: "Moroccan", country: "Kiribati" },
  { nationality: "Mosotho", country: "Saint Kitts and Nevis" },
  { nationality: "Mozabican", country: "Republic of Korea" },
  { nationality: "Nambian", country: "Kuwait" },
  { nationality: "Nauruan", country: "Lao People's Democratic Republic" },
  { nationality: "Nepalese", country: "Lebanon" },
  { nationality: "New Caledonian", country: "Liberia" },
  { nationality: "New Zealander", country: "Libyan Arab Jamahiriya" },
  { nationality: "Nicaraguan", country: "Saint Lucia" },
  { nationality: "Nigerian", country: "Liechtenstein" },
  { nationality: "Nigerien", country: "Sri Lanka" },
  { nationality: "North Korean", country: "Lesotho" },
  { nationality: "Norwegian", country: "Lithuania" },
  { nationality: "Omani", country: "Luxembourg" },
  { nationality: "Pakistani", country: "Latvia" },
  { nationality: "Panamanian", country: "Morocco" },
  { nationality: "Papua New Guinean", country: "Monaco" },
  { nationality: "Paraguyan", country: "Republic of Moldova" },
  { nationality: "Peruvian", country: "Madagascar" },
  { nationality: "Polish", country: "Maldives" },
  { nationality: "Portugese", country: "Mexico" },
  { nationality: "Puerto Rican", country: "Marshall Islands" },
  { nationality: "Romanian", country: "Mali" },
  { nationality: "Russian", country: "Malta" },
  { nationality: "Rwandan", country: "Myanmar" },
  { nationality: "Samoan", country: "Mongolia" },
  { nationality: "Saint Lucian", country: "Northern Mariana Islands" },
  { nationality: "Salvadorean", country: "Mozambique" },
  { nationality: "Saudi Arabian", country: "Mauritania" },
  { nationality: "Senegalese", country: "Montserrat" },
  { nationality: "Seychellois", country: "Martinique" },
  { nationality: "Sierra Leonian", country: "Mauritius" },
  { nationality: "Singaporean", country: "Malawi" },
  { nationality: "Slovakian", country: "Malaysia" },
  { nationality: "Slovenian", country: "Mayotte" },
  { nationality: "Somalian", country: "Namibia" },
  { nationality: "South African", country: "New Caledonia" },
  { nationality: "South Korean", country: "Niger" },
  { nationality: "Spanish", country: "Norfolk Island" },
  { nationality: "Sri Lankan", country: "Nigeria" },
  { nationality: "Sudanese", country: "Nicaragua" },
  { nationality: "Surinamese", country: "Niue" },
  { nationality: "Swazi", country: "Netherlands" },
  { nationality: "Swedish", country: "Norway" },
  { nationality: "Swiss", country: "Nepal" },
  { nationality: "Syrian", country: "Nauru" },
  { nationality: "Taiwanese", country: "New Zealand" },
  { nationality: "Tajik", country: "Oman" },
  { nationality: "Tanzanian", country: "Pakistan" },
  { nationality: "Thai", country: "Panama" },
  { nationality: "Togolese", country: "Pitcairn" },
  { nationality: "Tongan", country: "Peru" },
  { nationality: "Trinidadian / Tobagan", country: "Palau" },
  { nationality: "Tunisian", country: "Papua New Guinea" },
  { nationality: "Turkish", country: "Poland" },
  { nationality: "Turkmen", country: "Puerto Rico" },
  { nationality: "Tuvaluan", country: "DP Republic of Korea" },
  { nationality: "Ugandan", country: "Portugal" },
  { nationality: "Ukrainian", country: "Paraguay" },
  { nationality: "Uruguayan", country: "The State of Palestine" },
  { nationality: "Uzbek", country: "French Polynesia" },
  { nationality: "Vanuatuan", country: "Qatar" },
  { nationality: "Venezeulan", country: "Réunion" },
  { nationality: "Vietnamese", country: "Romania" },
  { nationality: "Western Samoan", country: "Russian Federation" },
  { nationality: "Yemeni", country: "Rwanda" },
  { nationality: "Yugoslavian", country: "Saudi Arabia" },
  { nationality: "Zairean", country: "Sudan" },
  { nationality: "Zambian", country: "Senegal" },
  { nationality: "Zimbabwean", country: "Singapore" },
  { nationality: "", country: "Saint Helena" },
  { nationality: "", country: "Svalbard and Jan Mayen Islands" },
  { nationality: "", country: "Solomon Islands" },
  { nationality: "", country: "Sierra Leone" },
  { nationality: "", country: "El Salvador" },
  { nationality: "", country: "San Marino" },
  { nationality: "", country: "Somalia" },
  { nationality: "", country: "Saint Pierre and Miquelon" },
  { nationality: "", country: "Sao Tome and Principe" },
  { nationality: "", country: "Suriname" },
  { nationality: "", country: "Slovakia" },
  { nationality: "", country: "Slovenia" },
  { nationality: "", country: "Sweden" },
  { nationality: "", country: "Swaziland" },
  { nationality: "", country: "Seychelles" },
  { nationality: "", country: "Syrian Arab Republic" },
  { nationality: "", country: "Turks and Caicos Islands" },
  { nationality: "", country: "Chad" },
  { nationality: "", country: "Togo" },
  { nationality: "", country: "Thailand" },
  { nationality: "", country: "Tajikistan" },
  { nationality: "", country: "Tokelau" },
  { nationality: "", country: "Turkmenistan" },
  { nationality: "", country: "Timor-Leste" },
  { nationality: "", country: "Tonga" },
  { nationality: "", country: "Trinidad and Tobago" },
  { nationality: "", country: "Tunisia" },
  { nationality: "", country: "Turkey" },
  { nationality: "", country: "Tuvalu" },
  { nationality: "", country: "United Republic of Tanzania" },
  { nationality: "", country: "Uganda" },
  { nationality: "", country: "Ukraine" },
  { nationality: "", country: "Uruguay" },
  { nationality: "", country: "United States of America" },
  { nationality: "", country: "Uzbekistan" },
  { nationality: "", country: "Holy See" },
  { nationality: "", country: "Saint Vincent and the Grenadines" },
  { nationality: "", country: "Venezuela (Bolivarian Republic of)" },
  { nationality: "", country: "British Virgin Islands" },
  { nationality: "", country: "United States Virgin Islands" },
  { nationality: "", country: "Viet Nam" },
  { nationality: "", country: "Vanuatu" },
  { nationality: "", country: "Wallis and Futuna Islands" },
  { nationality: "", country: "Samoa" },
  { nationality: "", country: "Yemen" },
  { nationality: "", country: "South Africa" },
  { nationality: "", country: "Democratic Republic of the Congo" },
  { nationality: "", country: "Zambia" },
  { nationality: "", country: "Zimbabwe" },
];
