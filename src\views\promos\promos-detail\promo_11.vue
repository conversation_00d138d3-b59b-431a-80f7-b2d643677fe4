<template>
  <XPage backgroundColor="#214831" :navBarStyle="{ backgroundColor: '#214831', color: '#fff' }">
    <div class="promo-detail">
      <h2 class="title">GENERAL MECHANICS</h2>
      <ul class="mechanics-list">
        <li>
          From 10:00 PM – 1:00 AM the next day, if a NUSTAR member's net loss meets the criteria,
          the NUSTAR member will receive the following cashback the next day.
        </li>
        <li>
          To participate in this promotion, NUSTAR members must have ₱100 deposits.<br />
          <span class="example">
            Ex. Feb 21 10:00 PM to Feb. 22 1:00 AM Player has a net Loss of 5,000. To get the 100
            bonus NUSTAR members must have a deposit total of 100 on Feb 21.
          </span>
        </li>
      </ul>

      <div class="table-container">
        <van-row>
          <van-col span="7">Net Loss Amount (PHP)</van-col>
          <van-col span="9">Cashback<br />(Non-VIP Member)</van-col>
          <van-col span="8">Cashback<br />(VIP Member)</van-col>
        </van-row>
        <van-row v-for="item in list" :key="item">
          <van-col span="7">{{ item.amount }}</van-col>
          <van-col span="9">{{ item.cashback }}</van-col>
          <van-col span="8">{{ item.vipCashback }}</van-col>
        </van-row>
      </div>

      <div class="net-loss-section">
        Net Loss Calculation:
        <div class="net-loss-desc">
          Net Loss = Total Valid Bets during the event period – Total Loss Payout.<br />
        </div>
        <div class="example">Ex. Player valid bet is 50,000 and loss payout is 29,000<br /></div>
        <div class="net-loss-desc">
          Computation: 50,000 (Valid Bet) – 29,000 (Loss Payout) = 21,000 (Net Loss)<br />
          NUSTAR Member will receive 500 Bonus.
        </div>
      </div>

      <ol class="mechanics-list" start="3">
        <li>
          Bonuses will be distributed automatically to eligible NUSTAR member's accounts by 4 PM on
          the same day.
        </li>
        <li>There is NO wagering requirement for withdrawal.</li>
      </ol>
    </div>
    <ZFootPng />
  </XPage>
</template>
<script setup>
const list = [
  {
    amount: "1,000",
    cashback: "₱10",
    vipCashback: "₱10",
  },
  {
    amount: "5,000",
    cashback: "₱100",
    vipCashback: "₱100",
  },
  {
    amount: "10,000",
    cashback: "₱300",
    vipCashback: "₱500",
  },
  {
    amount: "50,000",
    cashback: "₱2,500",
    vipCashback: "₱3,000",
  },
];
</script>

<style lang="scss" scoped>
.promo-detail {
  position: relative;
  margin: 0 auto;
  padding: 50px 12px 10px;
  background: linear-gradient(to bottom, #3d6e51, #001107 80%);
  color: #fff;
  font-family: "Inter", "D-DIN";
  font-size: 14px;
}

.title {
  color: #ffd700;
  margin-bottom: 18px;
  font-size: 18px;
  font-weight: 600;
}

.mechanics-list {
  margin-left: 18px;
  margin-bottom: 18px;
  line-height: 1.7;
  list-style: auto;
}

.mechanics-list li {
  margin-bottom: 12px;
}

.example {
  display: block;
  margin-top: 4px;
  font-size: 0.97em;
}

.table-container {
  margin-bottom: 18px;
  border: 1px solid #fff;
  border-radius: 12px;
  text-align: center;
  font-size: 12px;
  &:deep(.van-row) {
    border-bottom: 1px solid #fff;
    &:last-child {
      border: 0;
    }
    .van-col {
      padding: 8px 0;
      &:first-child {
        border-right: 1px solid #fff;
      }
      &:last-child {
        border-left: 1px solid #fff;
      }
    }
  }
}

.net-loss-section {
  margin-bottom: 18px;
}

.net-loss-desc {
  margin-top: 6px;
  margin-left: 12px;
  font-size: 1em;
}

.footer-bar {
  position: absolute;
  bottom: 0px;
  left: 0;
  margin-top: 20px;
  width: 100%;
  background: #000000;

  img {
    width: 100%;
  }
}
</style>
