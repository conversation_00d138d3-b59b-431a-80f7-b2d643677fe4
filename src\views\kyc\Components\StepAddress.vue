<script setup lang="ts">
import { ref, watch } from "vue";
import { useKycStore } from "@/stores/kyc";
import { S_INCOME_ENUM } from "@/views/kyc/CONSTANT";

const kycStore = useKycStore();

const { fullFormData, isSameCurrentAddress, isGovemmentOfficial } = storeToRefs(kycStore);

// 永久地址字段的引用
const permanentAddressRef = ref();

const updateStore = (field: keyof typeof fullFormData.value, value: any) => {
  kycStore.updateDetailFormData(field, value);
};

// 监听永久地址值的变化，清除验证错误
watch(
  () => fullFormData.value.permanent_address,
  (newValue) => {
    if (newValue && permanentAddressRef.value) {
      // 当永久地址有值时，清除验证错误
      permanentAddressRef.value.clearValidation();
    }
  }
);

// 监听复选框状态变化
watch(
  () => isSameCurrentAddress.value,
  (isChecked) => {
    if (isChecked && permanentAddressRef.value) {
      // 当选中"Same As Current Address"时，清除验证错误
      permanentAddressRef.value.clearValidation();
    }
  }
);
</script>
<template>
  <div class="form">
    <div class="address_tip">
      <ZIcon color="#FF936F" type="icon-warn" :size="16"></ZIcon>
      The address must contain complete information (including room number/apartment number) For
      example: Hyatt Lake, 1808, 7364 Jermain Way, Al Barsha
    </div>

    <ZFormField
      label="Place of Birth"
      v-model="fullFormData.place_of_birth"
      placeholder="Please enter your place of birth"
      :maxLength="200"
      @input="(value) => updateStore('place_of_birth', value)"
    ></ZFormField>
    <ZFormField
      label="Current Address"
      v-model="fullFormData.current_address"
      placeholder="Please enter your current address"
      :maxLength="200"
      @input="(value) => updateStore('current_address', value)"
    ></ZFormField>

    <ZFormField
      ref="permanentAddressRef"
      label="Permanent Address"
      v-model="fullFormData.permanent_address"
      placeholder="Please enter your permanent address"
      :maxLength="200"
      @input="(value) => updateStore('permanent_address', value)"
    >
      <van-checkbox
        class="same-address"
        @change="kycStore.handleSameAddressChecked"
        v-model="isSameCurrentAddress"
        checked-color="#AC1240"
        icon-size="16px"
        >Same As Current Address</van-checkbox
      >
    </ZFormField>
    <ZFormField
      label="Nature of Work"
      v-model="fullFormData.work"
      placeholder="Please enter your nature of work"
      :maxLength="200"
      @input="(value) => updateStore('work', value)"
    ></ZFormField>
    <div class="form-item">
      <label for="income">Source of Income</label>
      <ZSelect
        title="Source of Income"
        :modelValue="fullFormData.income"
        :selectList="S_INCOME_ENUM"
        @confirm="(e) => kycStore.handleSelectConfirm('income', e)"
        placeholder="Please select your source of income"
      >
      </ZSelect>
    </div>
    <div class="footer">
      <van-checkbox v-model="isGovemmentOfficial" checked-color="#AC1240" icon-size="16px"
        >Same As I am not a govemment official or employee connected directly with the operation of
        the Goverment or any of it's agencies, member of the Armed Forces of the Philippines,
        including the Army, Navy, Air Force, or the Philippine National Police.</van-checkbox
      >
    </div>
  </div>
</template>

<style lang="scss" scoped>
.footer {
  .van-checkbox {
    align-items: flex-start;

    &:deep(.van-checkbox__label) {
      color: #999;
      font-family: Inter;
    }
  }
}

.form {
  .address_tip {
    color: var(--ff-936-f, #ff936f);
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    background-color: #fffaf8;
    padding: 8px 12px;
    display: flex;
    padding: 8px 12px;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 8px;
    gap: 6px;
    margin-bottom: 10px;
    /* 150% */
  }
  .same-address {
    font-family: Inter;

    &:deep(.van-checkbox__label) {
      color: #666;
      font-family: Inter;
      font-size: 14px;
    }
  }

  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #ff4757;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}
</style>
