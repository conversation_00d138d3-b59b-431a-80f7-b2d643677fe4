import { showToast } from "vant";
import { confirmWithdrawPassword } from "@/api/withdrawal";

export const verifyLoginPassword = async (options) => {
  const { params = {}, successCallBack = () => {}, failCallBack = () => {} } = options;
  const response = await confirmWithdrawPassword({ ...params });
  const { code, msg } = response;
  if (code === 200 || code === 0) {
    successCallBack?.(response);
  } else {
    msg && showToast(msg);
    failCallBack?.(response);
  }
};
