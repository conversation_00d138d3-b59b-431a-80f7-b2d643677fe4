<!-- 添加提现账户前置弹窗：手机号、支付密码绑定 -->
<template>
  <div>
    <ZActionSheet
      v-model="visible"
      title="Preconditions"
      :showConfirmButton="false"
      :showCancelButton="false"
      :onCancel="handleCancel"
    >
      <div class="content">
        <div class="warning-tips">
          <ZIcon type="icon-warn" class="tips_icon" color=""></ZIcon>
          <div class="warning-tips-text">
            You have not set the following information, please set it in the following order.
          </div>
        </div>
        <div class="preconditions-content">
          <div v-for="item in list" :key="item.type">
            <div class="item" @click="() => handleItem(item)">
              <div class="left">
                <div class="item-icon">
                  <ZIcon :class="item.iconClass" color="" />
                </div>
                <div class="item-text" :class="{ ispass: bindingInfo[item.type] }">
                  {{ item.text }}
                </div>
              </div>
              <div class="right">
                <ZIcon type="icon-duihao" v-if="bindingInfo[item.type]" color=""></ZIcon>
                <ZIcon v-else type="icon-qianjin" color=""></ZIcon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ZActionSheet>
    <!-- 登录密码设置 -->
    <SetLoginPasswordDialog
      v-model:showNextDialog="showLoginPasswordDialog"
      :succCallBack="handleLoginPasswordSuccess"
    >
    </SetLoginPasswordDialog>
    <!-- 绑定、更改手机号 -->
    <SetChangePhoneDialog
      v-model:showNextDialog="showChangePhoneDialog"
      :verifyType="PN_VERIFY_TYPE.SetPhoneNumber"
      :succCallBack="handlePhoneChangeSuccess"
    >
    </SetChangePhoneDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from "vue";
import { PN_VERIFY_TYPE } from "./types";
import { useGlobalStore } from "@/stores/global";
import SetChangePhoneDialog from "@/components/ZVerifyDialog/SetChangePhoneDialog.vue";
import SetLoginPasswordDialog from "@/components/ZVerifyDialog/SetLoginPasswordDialog.vue";
// import SetPaymentPasswordDialog from "@/components/ZVerifyDialog/SetPaymentPasswordDialog.vue";

const globalStore = useGlobalStore();

const showLoginPasswordDialog = ref(false);
const showChangePhoneDialog = ref(false);

const list = ref([
  {
    type: "phone",
    iconClass: "icon-dianhua",
    text: "Set Phone Number",
  },
  // {
  //   type: "withdrawPassword",
  //   iconClass: "icon-anquan",
  //   text: "Set Payment Password",
  // },
  {
    type: "loginPassword",
    iconClass: "icon-anquan",
    text: "Set Login Password",
  },
]);

const bindingInfo = computed(() => {
  const globalStore = useGlobalStore();
  return {
    phone: !!globalStore.userInfo?.phone,
    loginPassword: !!globalStore.userInfo?.login_password,
  };
});

const props = defineProps({
  // 显示弹窗
  showDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
});

const visible = ref(props.showDialog);

watch(
  () => props.showDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      // 弹窗关闭时重置子弹窗状态
      resetChildDialogs();
    }
  }
);

// 监听 visible 变化，同步到父组件
watch(
  () => visible.value,
  (val) => {
    if (val !== props.showDialog) {
      emit("update:showDialog", val);
    }
  }
);

const emit = defineEmits(["update:showDialog"]);

// 重置子弹窗状态
const resetChildDialogs = () => {
  // showLoginPasswordDialog.value = false;
  showChangePhoneDialog.value = false;
};

const handleCancel = () => {
  emit("update:showDialog", false);
};

/**
 * 处理登录密码设置成功
 */
const handleLoginPasswordSuccess = () => {
  console.log("✅ 登录密码设置成功");
  // 检查是否所有前置条件都已满足
  checkAndCompleteIfReady();
};

/**
 * 处理手机号设置成功
 */
const handlePhoneChangeSuccess = () => {
  console.log("手机号设置成功");
  // 检查是否所有前置条件都已满足
  checkAndCompleteIfReady();
};

/**
 * 检查所有前置条件是否已满足，如果满足则完成整个流程
 */
const checkAndCompleteIfReady = () => {
  const userInfo = globalStore.userInfo;
  const allConditionsMet = userInfo.phone && userInfo.login_password;

  if (allConditionsMet) {
    console.log("✅ 所有前置条件已满足，关闭弹窗并执行回调");
    // 关闭主弹窗
    emit("update:showDialog", false);
    // 执行成功回调
    props.succCallBack && props.succCallBack(true);
  } else {
    console.log("⚠️ 还有前置条件未满足，保持弹窗开启");
    // 如果还有条件未满足，保持主弹窗开启，让用户继续设置
    props.succCallBack && props.succCallBack(false);
  }
};

const handleItem = async (item) => {
  const userInfo = globalStore.userInfo;
  if (!userInfo.phone && item.type == "phone") {
    //用户未设置手机号
    handleCancel();
    // 使用 nextTick 确保当前弹窗关闭后再显示新弹窗
    await nextTick();
    showChangePhoneDialog.value = true;
    return;
  } else if (!userInfo.login_password && item.type == "loginPassword") {
    // 未设置支付密码
    handleCancel();
    await nextTick();
    showLoginPasswordDialog.value = true;
    return;
  }
};
</script>
<style lang="scss" scoped>
.content {
  padding-bottom: 20px;

  .warning-tips {
    background-color: #feeeb9;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    padding: 8px;
    gap: 4px;

    .tips_icon {
      font-size: 20px;
      color: #ffd850;
    }

    .warning-tips-text {
      color: #6c5711;
    }
  }

  .preconditions-content {
    padding-top: 10px;

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;

      .left {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;

        .item-icon {
          width: 40px;
          height: 40px;
          background-color: #f8f8f8;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .ispass {
          color: #12be6a;
        }
      }

      .right {
        .icon-duihao {
          color: #12be6a;
        }

        .icon-qianjin {
          color: #c0c0c0;
        }
      }
    }
  }
}
</style>
