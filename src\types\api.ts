/**
 * API 相关类型定义
 */

import type { Game, Category, Provider, LiveGame } from "./store";

// ==================== 通用 API 响应类型 ====================

/**
 * 通用 API 响应结构
 */
export interface ApiResponse<T = any> {
  /** 响应状态码 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data: T;
  /** 其他字段 */
  [key: string]: any;
}

/**
 * 分页响应结构
 */
export interface PaginatedResponse<T = any> {
  /** 数据列表 */
  list: T[];
  /** 总数 */
  total: number;
  /** 当前页 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
}

// ==================== 游戏相关 API 类型 ====================

/**
 * 游戏配置 API 响应
 */
export interface GameConfigApiResponse {
  /** 游戏分类列表 */
  game_type: Category[];
  /** 第三方厂商列表 */
  third_company: Provider[];
  /** 直播游戏列表 */
  live_game: LiveGame[];
  /** 登录配置 */
  login_conf: LoginConfigData;
  /** 单次充值最大金额 */
  maximum_single_recharge: number;
  /** 其他配置项 */
  [key: string]: any;
}

/**
 * 登录配置数据
 */
export interface LoginConfigData {
  /** Facebook登录开关 */
  login_facebook: number;
  /** Google登录开关 */
  login_google: number;
  /** 密码登录开关 */
  login_password: number;
  /** 其他登录配置 */
  [key: string]: any;
}

/**
 * 游戏列表 API 请求参数
 */
export interface GameListParams {
  /** 是否为新游戏 */
  is_new?: number;
  /** 厂商ID */
  provider_id?: number;
  /** 游戏类型 */
  game_type?: string;
  /** 页码 */
  page?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 其他参数 */
  [key: string]: any;
}

/**
 * 游戏列表 API 响应
 */
export type GameListApiResponse = Game[];

/**
 * 更新列表 API 响应
 */
export interface UpdateListApiResponse {
  /** 维护中的游戏ID列表 */
  maintenance_list: number[];
  /** 隐藏的游戏ID列表 */
  hide_list: number[];
}

// ==================== 厂商相关 API 类型 ====================

/**
 * 厂商状态枚举
 */
export enum ProviderStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1
}

/**
 * 厂商扩展信息
 */
export interface ProviderExtended extends Provider {
  /** 图片URL */
  imageUrl?: string;
  /** 图片加载错误标识 */
  imageError?: boolean;
  /** 本地图片路径 */
  localImagePath?: string;
}

// ==================== 游戏操作相关 API 类型 ====================

/**
 * 游戏启动参数
 */
export interface GameLaunchParams {
  /** 游戏ID */
  game_id: number;
  /** 第三方游戏ID */
  third_game_id?: string;
  /** 厂商ID */
  company_id?: number;
  /** 其他参数 */
  [key: string]: any;
}

/**
 * 游戏启动响应
 */
export interface GameLaunchResponse {
  /** 游戏URL */
  game_url?: string;
  /** 游戏HTML内容 */
  game_html?: string;
  /** 是否需要外跳 */
  go_handle?: boolean;
  /** 其他数据 */
  [key: string]: any;
}

// ==================== 错误相关类型 ====================

/**
 * API 错误响应
 */
export interface ApiErrorResponse {
  /** 错误码 */
  code: number;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: any;
  /** 错误时间戳 */
  timestamp?: number;
}

/**
 * 网络错误类型
 */
export enum NetworkErrorType {
  /** 超时 */
  TIMEOUT = 'TIMEOUT',
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 服务器错误 */
  SERVER_ERROR = 'SERVER_ERROR',
  /** 客户端错误 */
  CLIENT_ERROR = 'CLIENT_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// ==================== 请求配置类型 ====================

/**
 * 请求配置
 */
export interface RequestConfig {
  /** 请求超时时间 */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 重试间隔 */
  retryDelay?: number;
  /** 是否显示加载状态 */
  showLoading?: boolean;
  /** 是否显示错误提示 */
  showError?: boolean;
  /** 自定义错误处理 */
  errorHandler?: (error: any) => void;
}

// ==================== 缓存相关类型 ====================

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 缓存键 */
  key: string;
  /** 缓存时间（毫秒） */
  ttl: number;
  /** 是否启用缓存 */
  enabled: boolean;
}

/**
 * 缓存项
 */
export interface CacheItem<T = any> {
  /** 缓存数据 */
  data: T;
  /** 缓存时间戳 */
  timestamp: number;
  /** 过期时间戳 */
  expireTime: number;
}

// ==================== 工具类型 ====================

/**
 * 可选的 API 参数
 */
export type OptionalApiParams<T> = Partial<T>;

/**
 * API 方法类型
 */
export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * API 端点配置
 */
export interface ApiEndpoint {
  /** 端点路径 */
  path: string;
  /** HTTP 方法 */
  method: ApiMethod;
  /** 是否需要认证 */
  requireAuth?: boolean;
  /** 缓存配置 */
  cache?: CacheConfig;
}

// ==================== 常量定义 ====================

/**
 * API 状态码
 */
export const API_STATUS_CODES = {
  /** 成功 */
  SUCCESS: 200,
  /** 成功（备用） */
  SUCCESS_ALT: 0,
  /** 未授权 */
  UNAUTHORIZED: 401,
  /** 禁止访问 */
  FORBIDDEN: 403,
  /** 未找到 */
  NOT_FOUND: 404,
  /** 服务器错误 */
  SERVER_ERROR: 500,
} as const;

/**
 * 默认请求配置
 */
export const DEFAULT_REQUEST_CONFIG: RequestConfig = {
  timeout: 10000,
  retries: 3,
  retryDelay: 1000,
  showLoading: true,
  showError: true,
};

/**
 * 默认缓存配置
 */
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  key: '',
  ttl: 5 * 60 * 1000, // 5分钟
  enabled: true,
};
