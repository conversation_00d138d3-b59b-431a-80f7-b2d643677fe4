<template>
  <div class="verification-code-wrapper">
    <input
      :id="inputId"
      v-model="inputValue"
      class="verification-code-input"
      :class="{ error: errorMessage }"
      :maxlength="maxLength"
      :placeholder="placeholder"
      type="number"
      inputmode="numeric"
      :disabled="disabled"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
    />
    <div v-show="errorMessage" class="error-message">{{ errorMessage }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onUnmounted } from "vue";

interface Props {
  modelValue?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  validateOnInput?: boolean;
  validateOnBlur?: boolean;
  errorMessages?: {
    required: string;
    minLength: string;
    invalid: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "Enter the code",
  disabled: false,
  required: true,
  minLength: 6,
  maxLength: 6,
  validateOnInput: false,
  validateOnBlur: false,
  errorMessages: () => ({
    required: "Verification code is required",
    // minLength: "Please enter a complete verification code",
    minLength: "Code Error,Please Try Again",
    invalid: "Invalid verification code format",
  }),
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
  input: [value: string];
  blur: [value: string];
  focus: [value: string];
  complete: [value: string];
  validityChange: [isValid: boolean];
}>();

const inputValue = ref(props.modelValue);
const errorMessage = ref("");
const inputId = `verification-code-input-${Math.random().toString(36).substring(2, 11)}`;

// 计算属性：验证码是否有效
const isValid = computed(() => {
  if (!inputValue.value) return !props.required;

  // 检查是否只包含数字
  if (!/^\d+$/.test(inputValue.value)) return false;

  // 检查长度
  return inputValue.value.length >= props.minLength && inputValue.value.length <= props.maxLength;
});

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal;
    if (props.validateOnInput) {
      validateCode();
    }
  }
);

// 监听有效性变化
watch(
  () => isValid.value,
  (newValid) => {
    emit("validityChange", newValid);
  },
  { immediate: true }
);

// 处理输入
const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  let value = target.value;

  // 只保留数字
  value = value.replace(/\D/g, "");

  // 限制最大长度
  if (value.length > props.maxLength) {
    value = value.slice(0, props.maxLength);
  }

  inputValue.value = value;
  target.value = value;

  // 清除错误信息
  if (errorMessage.value) {
    errorMessage.value = "";
  }

  emit("update:modelValue", value);
  emit("input", value);

  // 实时验证
  if (props.validateOnInput) {
    validateCode();
  }

  // 检查是否完成输入
  if (value.length === props.maxLength) {
    emit("complete", value);
  }
};

// 处理失焦事件
const handleBlur = () => {
  emit("blur", inputValue.value);

  if (props.validateOnBlur) {
    validateCode();
  }
};

// 处理聚焦事件
const handleFocus = () => {
  emit("focus", inputValue.value);
  // 聚焦时清除错误信息
  errorMessage.value = "";
};

// 验证验证码
const validateCode = () => {
  if (!inputValue.value) {
    if (props.required) {
      errorMessage.value = props.errorMessages.required;
      return false;
    } else {
      errorMessage.value = "";
      return true;
    }
  }

  if (inputValue.value.length < props.minLength) {
    errorMessage.value = props.errorMessages.minLength;
    return false;
  }

  if (!/^\d+$/.test(inputValue.value)) {
    errorMessage.value = props.errorMessages.invalid;
    return false;
  }

  errorMessage.value = "";
  return true;
};

// 清除输入
const clearInput = () => {
  inputValue.value = "";
  errorMessage.value = "";
  emit("update:modelValue", "");
  emit("input", "");
  emit("validityChange", !props.required);
};

// 聚焦到输入框
const focusInput = () => {
  const input = document.getElementById(inputId) as HTMLInputElement;
  input?.focus();
};

// 手动显示错误
const showError = (message: string) => {
  errorMessage.value = message;
};

// 清除错误
const clearError = () => {
  errorMessage.value = "";
};

// 组件销毁时清理
onUnmounted(() => {
  errorMessage.value = "";
  inputValue.value = "";
});

// 暴露方法给父组件
defineExpose({
  validate: validateCode,
  clear: clearInput,
  focus: focusInput,
  isValid: () => isValid.value,
  showError,
  clearError,
});
</script>

<style scoped lang="scss">
.verification-code-wrapper {
  width: 100%;
  flex: 1;
  height: 100%;

  .verification-code-input {
    flex: 1;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    background: transparent;
    font-size: 18px;
    font-weight: 700;
    color: #222;
    font-family: D-DIN, sans-serif;
    line-height: normal;
    transition: border-color 0.3s ease;

    &::placeholder {
      color: #c0c0c0;
      font-family: Inter, sans-serif;
      font-size: 14px;
      font-weight: 400;
      line-height: normal;
    }

    &:disabled {
      color: #999;
      cursor: not-allowed;
    }

    // 错误状态
    &.error {
      color: #ac1140;
      border-color: #ac1140;
    }

    // 移除数字输入框的箭头
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &[type="number"] {
      -moz-appearance: textfield;
      appearance: textfield;
    }
  }

  .error-message {
    margin-top: 4px;
    font-size: 14px;
    color: #ac1140;
    font-family: Inter, sans-serif;
    line-height: 1.2;
    width: 100%;
  }
}
</style>
