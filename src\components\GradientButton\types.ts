// 按钮组件相关类型定义

export interface GradientButtonProps {
  /** 按钮文本 */
  text?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示加载状态 */
  loading?: boolean;
}

export interface GradientButtonEmits {
  /** 点击事件 */
  click: [event: MouseEvent];
}

// 按钮主题配置
export interface ButtonTheme {
  /** 主要渐变色 */
  primaryGradient: string;
  /** 边框渐变色 */
  borderGradient: string;
  /** 悬停时的渐变色 */
  hoverGradient: string;
  /** 文字颜色 */
  textColor: string;
  /** 禁用时的渐变色 */
  disabledGradient: string;
}

// 默认主题
export const defaultTheme: ButtonTheme = {
  primaryGradient: "linear-gradient(90deg, #FFBD55 0%, #FF572A 100%)",
  borderGradient: "linear-gradient(85deg, #FFF0BF 0%, #FFE1C3 50%, #FFD3C8 100%)",
  hoverGradient: "linear-gradient(135deg, #FFE4B5 0%, #FFB347 30%, #FF6B35 70%, #FF4500 100%)",
  textColor: "#ffffff",
  disabledGradient: "linear-gradient(135deg, #D3D3D3 0%, #A9A9A9 100%)",
};

// 按钮样式配置
export interface ButtonStyleConfig {
  padding: string;
  fontSize: string;
  minHeight: string;
  fontFamily: string;
  fontWeight: string;
  lineHeight: string;
  letterSpacing: string;
}

export const defaultStyleConfig: ButtonStyleConfig = {
  padding: "12px 12px",
  fontSize: "16px",
  minHeight: "48px",
  fontFamily: "Inter",
  fontWeight: "700",
  lineHeight: "100%",
  letterSpacing: "0px",
};
