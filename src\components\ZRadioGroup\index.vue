<template>
  <div class="z-radio-group" :class="{ 'is-disabled': disabled }">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { provide, ref, watch, computed } from 'vue';

// 定义组件 props
const props = defineProps({
  modelValue: {
    type: [String, Number, Bo<PERSON>an] as PropType<string | number | boolean | null>,
    default: null,
  },
  name: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);

// 创建内部状态
const currentValue = ref(props.modelValue);

// 提供上下文给子组件
const provideValue = {
  name: props.name,
  modelValue: currentValue,
  disabled: props.disabled,

  // 处理单选框选中事件
  changeEvent: (value: string | number | boolean) => {
    if (props.disabled) return;
    currentValue.value = value;
    emit('update:modelValue', value);
  },
};

provide('ZRadioGroupContext', provideValue);

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  currentValue.value = newVal;
});
</script>

<style scoped>
.z-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
