# ZFormField 通用表单组件

基于 `van-field` 封装的通用表单输入组件，支持多种输入限制、验证规则和自定义配置。

## 功能特性

- ✅ **输入限制**：支持英文、数字、字母数字等多种输入限制
- ✅ **长度控制**：支持最大/最小长度限制
- ✅ **验证规则**：内置常用验证规则，支持自定义验证
- ✅ **实时验证**：支持输入时验证和失焦验证
- ✅ **无障碍支持**：完整的标签和 ARIA 属性
- ✅ **样式统一**：与项目设计系统保持一致

## 基础用法

```vue
<template>
  <!-- 基础文本输入 -->
  <ZFormField
    v-model="form.name"
    label="姓名"
    placeholder="请输入姓名"
    required
  />

  <!-- 手机号输入 -->
  <ZFormField
    v-model="form.phone"
    label="手机号"
    placeholder="请输入手机号"
    restriction="phone"
    :max-length="10"
    required
  />

  <!-- 只允许英文和数字 -->
  <ZFormField
    v-model="form.username"
    label="用户名"
    placeholder="请输入用户名"
    restriction="alphanumeric"
    :max-length="20"
    required
  />
</template>

<script setup>
import { ref } from 'vue';

const form = ref({
  name: '',
  phone: '',
  username: ''
});
</script>
```

## 输入限制类型

### restriction 属性

| 值 | 说明 | 示例 |
|---|---|---|
| `none` | 无限制（默认） | 任意字符 |
| `alphanumeric` | 只允许英文字母和数字 | `abc123` |
| `alphabetic` | 只允许英文字母 | `abcDEF` |
| `numeric` | 只允许数字 | `123456` |
| `phone` | 手机号格式（数字，最大10位） | `9123456789` |
| `email` | 邮箱字符 | `<EMAIL>` |

```vue
<template>
  <!-- 只允许英文和数字 -->
  <ZFormField
    v-model="username"
    label="用户名"
    restriction="alphanumeric"
    placeholder="只能输入英文字母和数字"
  />

  <!-- 只允许数字 -->
  <ZFormField
    v-model="amount"
    label="金额"
    restriction="numeric"
    placeholder="只能输入数字"
  />

  <!-- 手机号 -->
  <ZFormField
    v-model="phone"
    label="手机号"
    restriction="phone"
    placeholder="请输入10位手机号"
  />
</template>
```

## 验证规则

### 内置验证

```vue
<template>
  <!-- 必填 + 长度限制 -->
  <ZFormField
    v-model="password"
    label="密码"
    type="password"
    :min-length="8"
    :max-length="20"
    required
    placeholder="8-20位密码"
  />
</template>
```

### 自定义验证规则

```vue
<template>
  <ZFormField
    v-model="email"
    label="邮箱"
    :rules="emailRules"
    placeholder="请输入邮箱地址"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref } from 'vue';

const email = ref('');

const emailRules = [
  {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址'
  },
  {
    validator: (value) => {
      if (value.includes('test')) {
        return '邮箱不能包含test';
      }
      return true;
    }
  }
];

const handleValidate = (isValid, message) => {
  console.log('验证结果:', isValid, message);
};
</script>
```

## 高级用法

### 自定义输入过滤器

```vue
<template>
  <!-- 自动转换为大写 -->
  <ZFormField
    v-model="code"
    label="验证码"
    :input-filter="toUpperCase"
    placeholder="输入验证码"
  />
</template>

<script setup>
const code = ref('');

const toUpperCase = (value) => {
  return value.toUpperCase();
};
</script>
```

### 插槽使用

```vue
<template>
  <ZFormField
    v-model="amount"
    label="金额"
    placeholder="请输入金额"
  >
    <!-- 左侧图标 -->
    <template #leftIcon>
      <ZIcon name="money" />
    </template>

    <!-- 右侧按钮 -->
    <template #button>
      <ZButton size="small" @click="clearAmount">清除</ZButton>
    </template>
  </ZFormField>
</template>
```

## API 参考

### Props

| 参数 | 类型 | 默认值 | 说明 |
|---|---|---|---|
| `modelValue` | `string \| number` | `''` | 双向绑定的值 |
| `type` | `InputType` | `'text'` | 输入框类型 |
| `label` | `string` | `''` | 标签文本 |
| `placeholder` | `string` | `''` | 占位符 |
| `maxLength` | `number` | - | 最大长度 |
| `minLength` | `number` | - | 最小长度 |
| `required` | `boolean` | `false` | 是否必填 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `readonly` | `boolean` | `false` | 是否只读 |
| `clearable` | `boolean` | `true` | 是否可清除 |
| `restriction` | `InputRestriction` | `'none'` | 输入限制类型 |
| `rules` | `ValidationRule[]` | `[]` | 自定义验证规则 |
| `validateOnInput` | `boolean` | `false` | 是否在输入时验证 |
| `validateOnBlur` | `boolean` | `true` | 是否在失焦时验证 |
| `inputFilter` | `(value: string) => string` | - | 自定义输入过滤器 |

### Events

| 事件名 | 参数 | 说明 |
|---|---|---|
| `update:modelValue` | `value: string \| number` | 值变化时触发 |
| `input` | `value: string` | 输入时触发 |
| `blur` | `event: Event` | 失焦时触发 |
| `focus` | `event: Event` | 聚焦时触发 |
| `clear` | - | 清除时触发 |
| `validate` | `isValid: boolean, message?: string` | 验证时触发 |

### Slots

| 插槽名 | 说明 |
|---|---|
| `leftIcon` | 左侧图标 |
| `rightIcon` | 右侧图标 |
| `button` | 右侧按钮 |

### Methods

| 方法名 | 参数 | 返回值 | 说明 |
|---|---|---|---|
| `validate` | - | `boolean` | 手动验证 |
| `clearValidation` | - | - | 清除验证状态 |
| `focus` | - | - | 聚焦输入框 |
| `blur` | - | - | 失焦输入框 |

## 样式定制

组件使用项目统一的设计系统，支持通过 CSS 变量进行定制：

```scss
.z-form-field {
  // 自定义样式
  --field-background: #f7f8fa;
  --field-border-radius: 999px;
  --field-padding: 12px 20px;
  --field-font-size: 14px;
  --error-color: #ff4757;
}
```

## 最佳实践

1. **统一使用**：项目中所有表单输入都使用此组件
2. **合理验证**：根据业务需求选择合适的验证时机
3. **错误提示**：提供清晰的错误提示信息
4. **无障碍**：确保标签和输入框正确关联
5. **性能优化**：避免在输入时进行复杂验证

## 迁移指南

### 从原生 input 迁移

```vue
<!-- 原来的写法 -->
<input
  v-model="phone"
  type="number"
  maxlength="10"
  placeholder="请输入手机号"
/>

<!-- 新的写法 -->
<ZFormField
  v-model="phone"
  label="手机号"
  restriction="phone"
  placeholder="请输入手机号"
  required
/>
```

### 从 van-field 迁移

```vue
<!-- 原来的写法 -->
<van-field
  v-model="username"
  label="用户名"
  placeholder="请输入用户名"
  :rules="[{ required: true, message: '请输入用户名' }]"
/>

<!-- 新的写法 -->
<ZFormField
  v-model="username"
  label="用户名"
  placeholder="请输入用户名"
  restriction="alphanumeric"
  required
/>
```
