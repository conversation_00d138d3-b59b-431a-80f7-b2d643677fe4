<template>
  <div class="scrollbar-demo">
    <h2>滚动条样式演示</h2>
    
    <!-- 默认滚动条 -->
    <div class="demo-section">
      <h3>默认滚动条</h3>
      <div class="scroll-container default-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，用于演示默认滚动条样式效果。</p>
        </div>
      </div>
    </div>
    
    <!-- 细滚动条 -->
    <div class="demo-section">
      <h3>细滚动条</h3>
      <div class="scroll-container thin-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，用于演示细滚动条样式效果。</p>
        </div>
      </div>
    </div>
    
    <!-- 蓝色主题滚动条 -->
    <div class="demo-section">
      <h3>蓝色主题滚动条</h3>
      <div class="scroll-container blue-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，用于演示蓝色主题滚动条样式效果。</p>
        </div>
      </div>
    </div>
    
    <!-- 绿色主题滚动条 -->
    <div class="demo-section">
      <h3>绿色主题滚动条</h3>
      <div class="scroll-container green-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，用于演示绿色主题滚动条样式效果。</p>
        </div>
      </div>
    </div>
    
    <!-- 紫色主题滚动条 -->
    <div class="demo-section">
      <h3>紫色主题滚动条</h3>
      <div class="scroll-container purple-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，用于演示紫色主题滚动条样式效果。</p>
        </div>
      </div>
    </div>
    
    <!-- 隐藏滚动条 -->
    <div class="demo-section">
      <h3>隐藏滚动条（保持滚动功能）</h3>
      <div class="scroll-container hidden-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，滚动条被隐藏但仍可滚动。</p>
        </div>
      </div>
    </div>
    
    <!-- 悬停显示滚动条 -->
    <div class="demo-section">
      <h3>悬停显示滚动条</h3>
      <div class="scroll-container hover-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，悬停时显示滚动条。</p>
        </div>
      </div>
    </div>
    
    <!-- 圆角滚动条 -->
    <div class="demo-section">
      <h3>圆角滚动条</h3>
      <div class="scroll-container rounded-scrollbar">
        <div class="content">
          <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，用于演示圆角滚动条样式效果。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这是一个纯演示组件，不需要逻辑
</script>

<style scoped>
.scrollbar-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h3 {
  margin-bottom: 10px;
  color: #333;
  font-size: 16px;
}

.scroll-container {
  height: 150px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow-y: auto;
  background: #fafafa;
}

.content {
  padding: 15px;
}

.content p {
  margin: 8px 0;
  line-height: 1.5;
  color: #666;
}

/* 默认滚动条样式 */
.default-scrollbar {
  /* 使用全局默认样式 */
}

/* 演示说明文字 */
.scrollbar-demo h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 24px;
}
</style>
