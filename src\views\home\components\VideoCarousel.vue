<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted, onUnmounted, onActivated, onDeactivated } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";
import flvjs from "flv.js";
import Hls from "hls.js";
import { jumpGame } from "@/utils/JumpGame";
import homeVideoBase64 from "@/assets/constants/homeVideoBase64";
import { getServerSideImageUrl } from "@/utils/core/tools";

const gameStore = useGameStore();
const { liveGame } = storeToRefs(gameStore);

const videos = computed(() => liveGame.value || []);
const getVideos = computed(() => {
  return videos.value.slice(0, 3);
});

const swipeRef = ref<any>(null);
const currentIndex = ref(0);
const currentDuration = ref(3000);
let autoplayTimer: ReturnType<typeof setTimeout> | null = null;

const videoRefs = ref<(HTMLVideoElement | null)[]>([]);
const videoLoaded = ref<boolean[]>([]);
const videoLoadedOnce = ref<boolean[]>([]);
const flvPlayers = ref<Record<number, flvjs.Player>>({});
const hlsPlayers = ref<Record<number, Hls>>({});


const isInitializing = ref(true);

// 标记是否正在滑动（从按下到抬起的整个过程）
const isSliding = ref(false);
// 标记是否完成了一次有效滑动（已切换轮播项）
const hasSlid = ref(false);

function getVideoFormat(url: string): string {
  if (!url) return '';
  const urlWithoutParams = url.split('?')[0];
  const extension = urlWithoutParams.split('.').pop()?.toLowerCase() || '';
  return extension;
}

// 初始化 videoRefs
function initVideoRefs() {
  const length = getVideos.value.length;
  videoRefs.value = new Array(length).fill(null);
  videoLoaded.value = new Array(length).fill(false);
  videoLoadedOnce.value = new Array(length).fill(false);
}

// 初始化播放器
function initPlayer(videoElement: HTMLVideoElement, url: string, index: number) {
  const format = getVideoFormat(url);
  if (format === "flv" && flvjs.isSupported()) {
    const flvPlayer = flvjs.createPlayer({ type: "flv", url, isLive: true });
    flvPlayer.attachMediaElement(videoElement);
    flvPlayer.load();
    flvPlayer.play();
    flvPlayer.on('loadedmetadata', () => {
      videoLoaded.value[index] = true;
      videoLoadedOnce.value[index] = true;
    });
    flvPlayers.value[index] = flvPlayer;
  } else if (format === "m3u8") {
    if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      videoElement.src = url;
      videoElement.addEventListener("loadeddata", () => {
        videoLoaded.value[index] = true;
        videoLoadedOnce.value[index] = true;
      }, { once: true });
      videoElement.load();
    } else if (Hls.isSupported()) {
      const hls = new Hls();
      hls.loadSource(url);
      hls.attachMedia(videoElement);
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        videoLoaded.value[index] = true;
        videoLoadedOnce.value[index] = true;
        videoElement.play();
      });
      hlsPlayers.value[index] = hls;
    } else {
      videoElement.src = url;
      videoElement.load();
    }
  } else {
    videoElement.src = url;
    videoElement.addEventListener("loadeddata", () => {
      videoLoaded.value[index] = true;
      videoLoadedOnce.value[index] = true;
    }, { once: true });
    videoElement.load();
  }
}

// 销毁所有播放器
function cleanupPlayers() {
  Object.values(flvPlayers.value).forEach(player => {
    try {
      player?.pause();
      player?.unload();
      player?.detachMediaElement();
      player?.destroy();
    } catch { }
  });
  flvPlayers.value = {};
  Object.values(hlsPlayers.value).forEach(hls => {
    try {
      hls?.destroy();
    } catch { }
  });
  hlsPlayers.value = {};
}

// 自动播放
function startAutoplay() {
  stopAutoplay();
  autoplayTimer = setTimeout(() => {
    swipeRef.value?.next?.();
    startAutoplay();
  }, currentDuration.value);
}
function stopAutoplay() {
  if (autoplayTimer) {
    clearTimeout(autoplayTimer);
    autoplayTimer = null;
  }
}

// 轮播变化
function onChange(index: number) {
  currentIndex.value = index;
  const video = getVideos.value[index];
  currentDuration.value = video?.duration ? video.duration * 1000 : 3000;
  preloadAdjacentVideos(index);
  // 核心：滑动切换后，标记为有效滑动
  hasSlid.value = true;
}

// 预加载当前和相邻视频
function preloadAdjacentVideos(index: number) {
  const total = getVideos.value.length;
  if (total === 0) return;
  [index, (index + 1) % total, (index - 1 + total) % total].forEach(i => {
    loadSingleVideo(i);
  });
}

// 加载单个视频
function loadSingleVideo(index: number) {
  const videoElement = videoRefs.value[index];
  const video = getVideos.value[index];
  if (!videoElement || !video?.url || videoLoadedOnce.value[index]) return;
  initPlayer(videoElement, video.url, index);
}

// 设置 video ref
function setVideoRef(el: any, index: number) {
  if (el && el instanceof HTMLVideoElement) {
    videoRefs.value[index] = el;
    nextTick(() => {
      if ([currentIndex.value, (currentIndex.value + 1) % getVideos.value.length, (currentIndex.value - 1 + getVideos.value.length) % getVideos.value.length].includes(index)) {
        loadSingleVideo(index);
      }
    });
  }
}

// 页面激活时自动恢复播放
onActivated(() => {
  startAutoplay();
  getVideos.value.forEach((video, idx) => {
    const el = videoRefs.value[idx];
    if (el && el.src) {
      el.play?.();
    }
  });
});

// 页面离开时暂停所有视频
onDeactivated(() => {
  stopAutoplay();
  getVideos.value.forEach((video, idx) => {
    const el = videoRefs.value[idx];
    el?.pause?.();
  });
  cleanupPlayers();
});

// 监听游戏数据变化
watch(
  liveGame,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      isInitializing.value = false;
      nextTick(() => {
        initVideoRefs();
        if (getVideos.value.length > 0) {
          const firstVideo = getVideos.value[0];
          currentDuration.value = firstVideo?.duration ? firstVideo.duration * 1000 : 3000;
          preloadAdjacentVideos(0);
          startAutoplay();
        }
      });
    } else if (newValue !== null) {
      isInitializing.value = false;
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (liveGame.value && liveGame.value.length > 0) {
    isInitializing.value = false;
    initVideoRefs();
    nextTick(() => {
      if (getVideos.value.length > 0) {
        const firstVideo = getVideos.value[0];
        currentDuration.value = firstVideo?.duration ? firstVideo.duration * 1000 : 3000;
        preloadAdjacentVideos(0);
        startAutoplay();
      }
    });
  }
});

onUnmounted(() => {
  cleanupPlayers();
  stopAutoplay();
});


const jumpTo = async (game: any) => {
  // 若处于滑动过程中，或完成了有效滑动，均忽略点击
  if (isSliding.value || hasSlid.value) {
    console.log("忽略滑动触发的点击事件");
    // 延迟重置有效滑动标记（避免影响下次点击）
    setTimeout(() => {
      hasSlid.value = false;
    }, 100);
    return;
  }
  // 非滑动状态，正常跳转
  jumpGame({ id: game.game_id });
};
</script>

<template>
  <div>
    <div v-if="isInitializing" class="banner">
      <img :src="homeVideoBase64" class="carousel" />
    </div>
    <van-swipe v-else-if="getVideos.length > 0" class="carousel" :autoplay="0" indicator-color="white" ref="swipeRef"
      :loop="true" @change="onChange" @touchstart="isSliding = true; hasSlid = false" @touchend="isSliding = false">
      <van-swipe-item v-for="(video, index) in getVideos" :key="index" class="carousel-container"
        @click="jumpTo(video)">
        <div class="video-description">{{ video.game_description }}</div>
        <div class="video-live">
          <span class="live-icon">▶</span>
          <span class="live-text">Live</span>
        </div>
        <div class="play-btn">Play Now</div>
        <ZImage v-if="!videoLoadedOnce[index] && video.cover_picture" :src="getServerSideImageUrl(video.cover_picture)"
          class="cover-image" alt="封面图" type="video" />
        <video :style="{ opacity: videoLoaded[index] ? 1 : 0 }" :ref="(el) => setVideoRef(el, index)"
          class="video-element" :muted="true" playsinline webkit-playsinline x5-playsinline x5-video-player-type="h5"
          :autoplay="true" :loop="true" :poster="getServerSideImageUrl(video.cover_picture)"></video>
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<style scoped lang="scss">
.carousel {
  width: 100%;
  height: 190px;
  position: relative;
  border-radius: 20px;
  overflow: hidden;

  .video-description {
    position: absolute;
    top: 10px;
    left: 10px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    z-index: 9;
  }

  .play-btn {
    position: absolute;
    bottom: 10px;
    right: 10px;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    z-index: 9;

    background: #4881ed;
    line-height: 1;
    padding: 10px 12px;
    border-radius: 15px;
    font-weight: bold;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
  }

  .video-live {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    z-index: 9;

    display: inline-block;
    padding: 4px 10px;
    align-items: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    border-radius: 100px;
    color: #94ff70;

    .live-icon {
      display: inline-block;
      margin-right: 4px;
    }

    .live-text {
      /* 预留样式空间 */
      color: inherit;
    }
  }

  .carousel-container {
    display: flex;
    height: 100%;
    transition: transform 0.5s ease;
    position: relative;
    background-color: #e3e3e3;

    .cover-image,
    .video-element {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      top: 0;
      left: 0;
      pointer-events: none;
      /* 核心！让鼠标事件穿透子元素，传递给 van-swipe */
    }
  }
}
</style>
