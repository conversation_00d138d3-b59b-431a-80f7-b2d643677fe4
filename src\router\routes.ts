/**
 * 路由配置
 */

import type { RouteRecordRaw } from "vue-router";

export const routes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/home",
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () =>
      import(
        /* webpackChunkName: "views/system/404", webpackPrefetch: true */ "@/views/system/404.vue"
      ),
  },
  // 启动页路由
  {
    path: "/splash",
    name: "Splash",
    component: () =>
      import(
        /* webpackChunkName: "views/system/splashPage", webpackPrefetch: true */ "@/views/system/splashPage.vue"
      ),
    meta: {
      requiresAuth: false,
      title: "Loading...",
    },
  },
  {
    path: "/login",
    name: "Login",
    component: () =>
      import(
        /* webpackChunkName: "views/login/index", webpackPrefetch: true */ "@/views/login/index.vue"
      ),
    meta: { requiresAuth: false },
  },
  {
    path: "/home",
    name: "Home",
    component: () =>
      import(
        /* webpackChunkName: "views/home/<USER>", webpackPrefetch: true */ "@/views/home/<USER>"
      ),
    meta: { requiresAuth: false, showTabBar: true },
  },
  {
    path: "/promos/:tab?",
    name: "PromosAndTournament",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/index", webpackPrefetch: true */ "@/views/promos/index.vue"
      ),

    meta: { showTabBar: true, keepAlive: true, requiresAuth: false },
  },

  {
    path: "/news",
    name: "News",
    component: () =>
      import(
        /* webpackChunkName: "views/news/index", webpackPrefetch: true */ "@/views/news/index.vue"
      ),
    meta: { requiresAuth: false, showTabBar: true },
  },
  {
    path: "/news/:id",
    name: "NewsDetail",
    component: () =>
      import(
        /* webpackChunkName: "views/news/detail", webpackPrefetch: true */ "@/views/news/detail.vue"
      ),
    meta: { title: "News Detail", requiresAuth: false },
  },
  {
    path: "/account",
    name: "Account",
    component: () =>
      import(
        /* webpackChunkName: "views/account/index", webpackPrefetch: true */ "@/views/account/index.vue"
      ),
    meta: { showTabBar: true },
  },
  {
    path: "/game-categories",
    name: "GameCategories",
    component: () =>
      import(
        /* webpackChunkName: "views/game-categories/index", webpackPrefetch: true */ "@/views/game-categories/index.vue"
      ),
    meta: { showTabBar: false },
  },

  {
    path: "/casino-cate",
    name: "CasinoCate",
    component: () =>
      import(
        /* webpackChunkName: "views/game-categories/casino-cate", webpackPrefetch: true */ "@/views/game-categories/casino-cate.vue"
      ),
  },
  {
    path: "/message",
    name: "Message",
    component: () =>
      import(
        /* webpackChunkName: "views/message/index", webpackPrefetch: true */ "@/views/message/index.vue"
      ),
    meta: { title: "Inbox" },
  },
  {
    path: "/message/:id",
    name: "MessageDetail",
    component: () =>
      import(
        /* webpackChunkName: "views/message/detail", webpackPrefetch: true */ "@/views/message/detail.vue"
      ),
    meta: { title: "Inbox Detail" },
  },
  {
    path: "/account/set-avatar",
    name: "SetAvatar",
    component: () =>
      import(
        /* webpackChunkName: "views/account/set-avatar", webpackPrefetch: true */ "@/views/account/set-avatar.vue"
      ),
    meta: { title: "Choose Avatar" },
  },
  {
    path: "/account/security-center",
    name: "SafePrivacy",
    component: () =>
      import(
        /* webpackChunkName: "views/account/security-center", webpackPrefetch: true */ "@/views/account/security-center.vue"
      ),
    meta: { title: "Security Center" },
  },
  {
    path: "/account/bet-order",
    name: "BetOrder",
    component: () =>
      import(
        /* webpackChunkName: "views/account/bet/index", webpackPrefetch: true */ "@/views/account/bet/index.vue"
      ),
    meta: { title: "Bet Order" },
  },
  {
    path: "/account/bet-detail",
    name: "BetDetail",
    component: () =>
      import(
        /* webpackChunkName: "views/account/bet/detail", webpackPrefetch: true */ "@/views/account/bet/detail.vue"
      ),
    meta: { title: "Bet Details" },
  },
  {
    path: "/account/transactions/:tab",
    name: "Transactions",
    component: () =>
      import(
        /* webpackChunkName: "views/account/transactions/index", webpackPrefetch: true */ "@/views/account/transactions/index.vue"
      ),
    meta: { title: "Transaction" },
  },
  {
    path: "/account/transactions",
    redirect: "/account/transactions/Deposit",
  },
  {
    path: "/account/transactions/combinelist/:id",
    name: "TransactionsCombineList",
    component: () =>
      import(
        /* webpackChunkName: "views/account/transactions/combine-list", webpackPrefetch: true */ "@/views/account/transactions/combine-list.vue"
      ),
    meta: { title: "Transaction", keepAlive: true },
  },
  {
    path: "/account/transactions/detail",
    name: "TransactionsDetail",
    component: () =>
      import(
        /* webpackChunkName: "views/account/transactions/detail", webpackPrefetch: true */ "@/views/account/transactions/detail.vue"
      ),
    meta: { title: "Transactions Detail" },
  },
  {
    path: "/account/withdraw-account",
    name: "WithdrawAccount",
    component: () =>
      import(
        /* webpackChunkName: "views/account/withdraw-account/index", webpackPrefetch: true */ "@/views/account/withdraw-account/index.vue"
      ),
    meta: { title: "Withdrawal Account" },
  },
  {
    path: "/account/withdraw-account/edit",
    name: "WithdrawAccountEdit",
    component: () =>
      import(
        /* webpackChunkName: "views/account/withdraw-account/edit", webpackPrefetch: true */ "@/views/account/withdraw-account/edit.vue"
      ),
    meta: { title: "Enter your accounts details" },
  },
  {
    path: "/protocal/terms-of-use",
    name: "TermsOfUse",
    component: () =>
      import(
        /* webpackChunkName: "views/protocal/terms-of-use", webpackPrefetch: true */ "@/views/protocal/terms-of-use.vue"
      ),
    meta: { title: "Terms of Use NUSTAR", requiresAuth: false },
  },
  {
    path: "/protocal/privacy-policy",
    name: "Privacypolicy",
    component: () =>
      import(
        /* webpackChunkName: "views/protocal/privacy-policy", webpackPrefetch: true */ "@/views/protocal/privacy-policy.vue"
      ),
    meta: { title: "Privacy Policy", requiresAuth: false },
  },
  {
    path: "/account/vip",
    name: "vip",
    component: () =>
      import(
        /* webpackChunkName: "views/account/vip/index", webpackPrefetch: true */ "@/views/account/vip/index.vue"
      ),
    meta: {
      title: "VIP",
      requiresFirstCheck: true,
      returnTo: "/account/vip",
      firstVisitRoute: "/account/vip-introduction",
    },
  },
  {
    path: "/account/vip-introduction",
    name: "vipIntroduction",
    component: () =>
      import(
        /* webpackChunkName: "views/account/vip/Introduction", webpackPrefetch: true */ "@/views/account/vip/Introduction.vue"
      ),
    meta: { title: "VIP Details", isFirstVisitPage: true },
  },
  {
    path: "/bonus-wallet",
    name: "BonusWallet",
    component: () =>
      import(
        /* webpackChunkName: "views/reward-wallet/index", webpackPrefetch: true */ "@/views/reward-wallet/index.vue"
      ),
    meta: { title: "My Bonuses", requiresAuth: true },
  },
  {
    path: "/system/maintenance",
    name: "SystemMaintenance",
    component: () =>
      import(
        /* webpackChunkName: "views/system/maintenance", webpackPrefetch: true */ "@/views/system/maintenance.vue"
      ),
    meta: { title: "System Maintenance", requiresAuth: false },
  },
  {
    path: "/system/safari-instructions",
    name: "safariInstructions",
    component: () =>
      import(
        /* webpackChunkName: "views/system/safari-instructions", webpackPrefetch: true */ "@/views/system/safari-instructions.vue"
      ),
    meta: { title: "Instructions", requiresAuth: false },
  },
  {
    path: "/promos/promo_0",
    name: "Promo0",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_0", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_0.vue"
      ),
  },
  {
    path: "/promos/promo_1",
    name: "Promo1",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_1", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_1.vue"
      ),
    meta: { title: "REGISTER TO GET FREE ₱15" },
  },
  {
    path: "/promos/promo_2",
    name: "Promo2",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_2", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_2.vue"
      ),
    meta: { title: "Deposit Now,Enjoy ₱100 Bonus!" },
  },
  {
    path: "/promos/promo_3",
    name: "Promo3",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_3", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_3.vue"
      ),
    meta: { title: "Best JILI PROMO in PH" },
  },
  {
    path: "/promos/promo_4",
    name: "Promo4",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_4", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_4.vue"
      ),
    meta: {
      title: "GET UNLIMITED CASHBACK",
      // requiresFirstCheck: true, 在页面内判断
      returnTo: "/promos/promo_4",
      firstVisitRoute: "/account/vip-introduction",
    },
  },
  {
    path: "/promos/promo_5",
    name: "Promo5",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_5", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_5.vue"
      ),
    meta: { title: "Casino Leaderboard" },
  },
  {
    path: "/promos/promo5_tip",
    name: "Promo5Tip",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo5_tip", webpackPrefetch: true */ "@/views/promos/promos-detail/promo5_tip.vue"
      ),
    meta: { title: "Casino Leaderboard" },
  },
  {
    path: "/promos/promo_7",
    name: "Promo7",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_7", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_7.vue"
      ),
    meta: { title: "JILI Leaderboard" },
  },
  {
    path: "/promos/promo7_tip",
    name: "Promo7Tip",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo7_tip", webpackPrefetch: true */ "@/views/promos/promos-detail/promo7_tip.vue"
      ),
    meta: { title: "Leaderboard" },
  },
  {
    path: "/promos/promo_8",
    name: "Promo8",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_8", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_8.vue"
      ),
    meta: { title: "Valentine" },
  },
  {
    path: "/promos/promo_9",
    name: "Promo9",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_9", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_9.vue"
      ),
    meta: { title: "Weekly Payday" },
  },
  {
    path: "/promos/promo9_tip",
    name: "Promo9Tip",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo9_tip", webpackPrefetch: true */ "@/views/promos/promos-detail/promo9_tip.vue"
      ),
    meta: { title: "Weekly Payday" },
  },
  {
    path: "/promos/promo_10",
    name: "Promo10",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_10", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_10.vue"
      ),
    meta: { title: "PP Daily Wins" },
  },
  {
    path: "/promos/promo_11",
    name: "Promo11",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_11", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_11.vue"
      ),
    meta: { title: "Late Night Cashback" },
  },
  {
    path: "/promos/promo_12",
    name: "Promo12",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_12", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_12.vue"
      ),
    meta: { title: "FC FREE SPIN" },
  },
  {
    path: "/promos/promo_14",
    name: "Promo14",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_14", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_14.vue"
      ),
    meta: { title: "YELLOW BAT" },
  },
  {
    path: "/promos/promo_15",
    name: "Promo15",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_15", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_15.vue"
      ),
    meta: { title: "JILI FREE SPIN" },
  },
  {
    path: "/promos/promo_16",
    name: "Promo16",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_16", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_16.vue"
      ),
    meta: { title: "FC FREE SPIN" },
  },
  {
    path: "/promos/tournament-activePage",
    name: "TournamentActivePage",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/tournament-detail/activePage", webpackPrefetch: true */ "@/views/promos/tournament-detail/activePage.vue"
      ),
  },

  {
    path: "/promo-webview",
    name: "PromoView",
    component: () =>
      import(
        /* webpackChunkName: "views/promos/promos-detail/promo_webview", webpackPrefetch: true */ "@/views/promos/promos-detail/promo_webview.vue"
      ),
  },
  {
    path: "/kyc/full-form",
    name: "KycFullForm",
    component: () =>
      import(
        /* webpackChunkName: "views/kyc/full-form", webpackPrefetch: true */ "@/views/kyc/full-form.vue"
      ),
    meta: { title: "Personal Information" },
  },
  {
    path: "/kyc/simple-form",
    name: "KycSimpleForm",
    component: () =>
      import(
        /* webpackChunkName: "views/kyc/simple-form", webpackPrefetch: true */ "@/views/kyc/simple-form.vue"
      ),
    meta: { title: "Personal Information" },
  },
  {
    path: "/kyc/detail",
    name: "KycDetail",
    component: () =>
      import(
        /* webpackChunkName: "views/kyc/detail", webpackPrefetch: true */ "@/views/kyc/detail.vue"
      ),
    // meta: { title: "KYC Details" },
    meta: { title: "Personal Details(KYC)" },
  },
  {
    path: "/kyc/success",
    name: "KycSuccess",
    component: () =>
      import(
        /* webpackChunkName: "views/kyc/success", webpackPrefetch: true */ "@/views/kyc/success.vue"
      ),
    meta: { title: "Success" },
  },
  {
    path: "/game-webview",
    name: "GameWebView",
    component: () =>
      import(
        /* webpackChunkName: "views/game-webview/index", webpackPrefetch: true */ "@/views/game-webview/index.vue"
      ),
    meta: { title: "Nustar" },
  },
];
