/**
 * 现代化首充红包显示管理器
 * 统一管理首充红包的显示次数、条件判断等逻辑
 */

import { getLocalStorage, setLocalStorage, removeLocalStorage } from "@/utils/core/Storage";
import { getToday } from "@/utils/core/tools";

// 存储键前缀
const STORAGE_PREFIX = "ENVELOPE";

// 默认配置
const DEFAULT_MAX_DAILY_SHOWS = 3; // 每日最大显示次数

// 首充红包显示记录
export interface EnvelopeDisplayRecord {
  readonly count: number;
  readonly date: string;
  readonly maxShows: number;
}

// 首充红包配置
export interface EnvelopeConfig {
  readonly isFirstRecharge: boolean;
  readonly isGuideStarted: boolean;
  readonly userId: string; // 必须有 userId，因为只有登录用户才能看到
}

// 首充红包统计信息
export interface EnvelopeStats {
  readonly todayCount: number;
  readonly lastShowDate: string;
  readonly canShow: boolean;
  readonly remainingShows: number;
}

/**
 * 现代化首充红包显示管理器
 */
export class EnvelopeDisplayManager {
  private readonly storagePrefix: string;
  private readonly defaultMaxShows: number;

  constructor(
    storagePrefix: string = STORAGE_PREFIX,
    defaultMaxShows: number = DEFAULT_MAX_DAILY_SHOWS
  ) {
    this.storagePrefix = storagePrefix;
    this.defaultMaxShows = defaultMaxShows;
  }

  /**
   * 生成存储键名
   */
  private createStorageKey(userId: string): string {
    return `${this.storagePrefix}_USER_${userId}`;
  }

  /**
   * 获取默认的最大显示次数
   */
  public getDefaultMaxShows(): number {
    return this.defaultMaxShows;
  }

  /**
   * 解析存储的显示记录
   */
  private parseDisplayRecord(
    value: string,
    defaultMaxShows: number = this.defaultMaxShows
  ): EnvelopeDisplayRecord {
    if (!value) {
      return { count: 0, date: "", maxShows: defaultMaxShows };
    }

    const [countStr, dateStr, maxShowsStr] = value.split("_");
    return {
      count: parseInt(countStr) || 0,
      date: dateStr || "",
      maxShows: parseInt(maxShowsStr) || defaultMaxShows,
    };
  }

  /**
   * 序列化显示记录
   */
  private serializeDisplayRecord(record: EnvelopeDisplayRecord): string {
    return `${record.count}_${record.date}_${record.maxShows}`;
  }

  /**
   * 获取首充红包的显示记录
   */
  public getDisplayRecord(userId: string): EnvelopeDisplayRecord {
    const key = this.createStorageKey(userId);
    const value = getLocalStorage(key) || "";
    const record = this.parseDisplayRecord(value, this.defaultMaxShows);

    // 如果不是今天的记录，返回空记录
    const today = getToday();
    if (record.date && record.date !== today) {
      return { count: 0, date: today, maxShows: this.defaultMaxShows };
    }

    return record;
  }

  /**
   * 记录首充红包显示
   */
  public recordShow(userId: string): number {
    const currentRecord = this.getDisplayRecord(userId);
    const today = getToday();

    const newRecord: EnvelopeDisplayRecord = {
      count: currentRecord.count + 1,
      date: today,
      maxShows: this.defaultMaxShows,
    };

    const key = this.createStorageKey(userId);
    const value = this.serializeDisplayRecord(newRecord);
    setLocalStorage(key, value);

    return newRecord.count;
  }

  /**
   * 检查首充红包是否可以显示
   */
  public canShow(config: EnvelopeConfig): boolean {
    // 基础条件检查
    if (!config.isFirstRecharge || !config.isGuideStarted) {
      return false;
    }

    const record = this.getDisplayRecord(config.userId);
    const today = getToday();

    // 如果不是今天的记录，可以显示
    if (record.date !== today) {
      return true;
    }

    // 检查是否超过每日限制
    return record.count < this.defaultMaxShows;
  }

  /**
   * 获取首充红包统计信息
   */
  public getStats(userId: string): EnvelopeStats {
    const record = this.getDisplayRecord(userId);
    const today = getToday();
    const todayCount = record.date === today ? record.count : 0;

    return {
      todayCount,
      lastShowDate: record.date,
      canShow: todayCount < this.defaultMaxShows,
      remainingShows: Math.max(0, this.defaultMaxShows - todayCount),
    };
  }

  /**
   * 清除首充红包数据
   */
  public clear(userId: string): void {
    const key = this.createStorageKey(userId);
    removeLocalStorage(key);
  }

  /**
   * 清除所有首充红包数据（管理员功能）
   */
  public clearAll(): void {
    const prefix = `USER_`;
    const suffix = `_${this.storagePrefix}`;

    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(prefix) && key?.includes(suffix)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => removeLocalStorage(key));
  }

  /**
   * 重置今日计数
   */
  public resetTodayCount(userId: string): void {
    const today = getToday();
    const resetRecord: EnvelopeDisplayRecord = {
      count: 0,
      date: today,
      maxShows: this.defaultMaxShows,
    };

    const key = this.createStorageKey(userId);
    setLocalStorage(key, this.serializeDisplayRecord(resetRecord));
  }

  /**
   * 清理过期数据（定期清理任务）
   */
  public cleanupExpiredData(): void {
    const today = getToday();
    const prefix = `USER_`;
    const suffix = `_${this.storagePrefix}`;

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(prefix) && key?.includes(suffix)) {
        const value = getLocalStorage(key) || "";
        const [, dateStr] = value.split("_");

        // 如果不是今天的数据，清除过期记录
        if (dateStr && dateStr !== today) {
          removeLocalStorage(key);
        }
      }
    }
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(userId: string): {
    storageKey: string;
    record: EnvelopeDisplayRecord;
    stats: EnvelopeStats;
  } {
    const key = this.createStorageKey(userId);
    const record = this.getDisplayRecord(userId);
    const stats = this.getStats(userId);

    return {
      storageKey: key,
      record,
      stats,
    };
  }
}

// 创建默认实例
export const envelopeDisplayManager = new EnvelopeDisplayManager();
