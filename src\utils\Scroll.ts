/**
 * 滚动相关工具函数
 */

/**
 * 滚动到页面顶部
 * @param options 滚动选项
 */
export interface ScrollToTopOptions {
  /** 滚动行为，默认 'smooth' */
  behavior?: 'smooth' | 'instant' | 'auto';
  /** 延迟时间（毫秒），默认 0 */
  delay?: number;
  /** 自定义滚动目标选择器 */
  customTargets?: string[];
}

export const scrollToTop = (options: ScrollToTopOptions = {}) => {
  const {
    behavior = 'smooth',
    delay = 0,
    customTargets = []
  } = options;

  const executeScroll = () => {
    try {
      // 默认滚动目标选择器（按优先级排序）
      const defaultTargets = [
        '.categories-container',           // 游戏分类容器
        '.van-tabs__content',             // van-tabs 内容区域
        '.page-container',                // 通用页面容器
        '.van-tab-panel',                 // tab 面板
        'main',                           // 主内容区域
        '#app'                            // 应用根容器
      ];

      // 合并自定义目标和默认目标
      const scrollTargets = [...customTargets, ...defaultTargets];

      let scrolled = false;
      
      // 尝试找到可滚动的容器
      for (const selector of scrollTargets) {
        const container = document.querySelector(selector) as HTMLElement;
        if (container && container.scrollTop > 0) {
          container.scrollTo({
            top: 0,
            behavior
          });
          scrolled = true;
          break;
        }
      }

      // 如果没有找到可滚动的容器，滚动窗口
      if (!scrolled) {
        window.scrollTo({
          top: 0,
          behavior
        });
      }
    } catch (error) {
      // 兜底方案，立即滚动
      try {
        window.scrollTo(0, 0);
      } catch (e) {
        console.warn('滚动到顶部失败:', e);
      }
    }
  };

  // 如果有延迟，使用 setTimeout
  if (delay > 0) {
    setTimeout(executeScroll, delay);
  } else {
    executeScroll();
  }
};

/**
 * 滚动到指定元素
 * @param element 目标元素或选择器
 * @param options 滚动选项
 */
export const scrollToElement = (
  element: HTMLElement | string,
  options: ScrollIntoViewOptions = { behavior: 'smooth', block: 'start' }
) => {
  try {
    const targetElement = typeof element === 'string' 
      ? document.querySelector(element) as HTMLElement
      : element;

    if (targetElement) {
      targetElement.scrollIntoView(options);
    }
  } catch (error) {
    console.warn('滚动到元素失败:', error);
  }
};

/**
 * 检查元素是否在视口中
 * @param element 目标元素或选择器
 * @returns 是否在视口中
 */
export const isElementInViewport = (element: HTMLElement | string): boolean => {
  try {
    const targetElement = typeof element === 'string'
      ? document.querySelector(element) as HTMLElement
      : element;

    if (!targetElement) return false;

    const rect = targetElement.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  } catch (error) {
    console.warn('检查元素是否在视口中失败:', error);
    return false;
  }
};

/**
 * 平滑滚动到指定位置
 * @param container 滚动容器
 * @param targetY 目标Y坐标
 * @param duration 动画持续时间（毫秒）
 */
export const smoothScrollTo = (
  container: HTMLElement | Window,
  targetY: number,
  duration: number = 300
) => {
  const startY = container === window ? window.pageYOffset : (container as HTMLElement).scrollTop;
  const distance = targetY - startY;
  const startTime = performance.now();

  const animateScroll = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // 使用缓动函数
    const easeInOutCubic = (t: number) => 
      t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    
    const currentY = startY + distance * easeInOutCubic(progress);
    
    if (container === window) {
      window.scrollTo(0, currentY);
    } else {
      (container as HTMLElement).scrollTop = currentY;
    }

    if (progress < 1) {
      requestAnimationFrame(animateScroll);
    }
  };

  requestAnimationFrame(animateScroll);
};
