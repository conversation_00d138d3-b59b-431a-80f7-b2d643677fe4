/**
 * 促销活动统一配置文件
 * 合并了 constants、configs、types 三个目录的内容
 */

// ==================== 资源导入 ====================
import casinoPageBg from "@/assets/images/promos/casino_bg.jpg";
import jiliPageBg from "@/assets/images/promos/jili_bg.jpg";
import labaImg from "@/assets/images/promos/laba.png";
import ended from "@/assets/images/tournament/ended.png";
import waiting from "@/assets/images/tournament/waiting.png";
import ongoing from "@/assets/images/tournament/ongoing.png";
import coinImg from "@/assets/images/tournament/coin.png";
import DefaultImage from "@/assets/images/noData.svg";

// ==================== API 导入 ====================
import { casinoRank, rankSolt } from "@/api/promos";

// ==================== 类型定义 ====================

// 主题配置接口
export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    gradient: string;
    tabBackground: string;
    tabActive: string;
    tabActiveText: string;
    tableBackground: string;
    tableRowBackground: string;
    rankNumber: string;
    rankBonus: string;
    footerBackground: string;
    infoBackground: string;
  };
  backgroundImage?: string;
  backgroundPosition?: string;
}

// 标签页配置接口
export interface TabItem {
  label: string;
  value: string;
}

// 表格列配置接口
export interface TableColumn {
  key: string;
  label: string;
  class?: string;
}

// 促销配置接口
export interface PromoConfig {
  id: string;
  name: string;
  theme: ThemeConfig;
  apiFunction: (params: any) => Promise<any>;
  tabs: TabItem[];
  tableColumns: TableColumn[];
  pageBg?: string;
  infoBannerImage: string;
  helpRoute: string;
  showPodium: boolean;
  showInfoBanner: boolean;
  betLabel: string;
  userLabel: string;
  autoRefresh: boolean;
  refreshInterval: number;
  customSettings?: {
    [key: string]: any;
  };
}

// 锦标赛状态类型
export type TournamentStatusType = "waiting" | "ongoing" | "ended";

// 锦标赛状态数值类型
export type TournamentStatusCode = 0 | 1 | 2;

// 报名模式索引类型
export type EntryModeIndex = 0 | 1;

// 报名规则索引类型
export type EntryRuleIndex = 0 | 1 | 2;

// 前三名排名奖励
export type TopRank = {
  rank: number;
  bonus: number;
};

// 排名列表子项
export type RankItem = {
  rank: number;
  avatar: string;
  nickname: string;
  score: string;
  bonus: string;
  user_id: string;
};

// 锦标赛详情信息
export type DetailInfo = {
  id: string;
  status: number | string;
  statusName?: string;
  matches_rule: number;
  title: string;
  title_image: string;
  detail_image_urls: string[];
  history_detail_image_urls: string[];
  period_type: number;
  start_time: number;
  end_time: number;
  registration_rule: number;
  registration_requirement: string;
  my_registration_value: string;
  game_ids: string[];
  vendors: number[];
  brands: number[];
  game_types: number[];
  total_price: number;
  my_rank: RankItem;
  my_historyRank: RankItem;
  top_rankings: TopRank[];
};

// 锦标赛接口
export interface Tournament {
  id: string | number;
  badgeImage: string;
  status: string | number;
  statusName?: string;
  banner_image: string;
  total_price: number;
  start_time: number;
  end_time: number;
  period_type: number;
  match_user_count: number;
  registration_mode: string | number;
  registration_rule: string | number;
  [key: string]: any;
}

// 处理后的锦标赛数据接口
export interface ProcessedTournament extends Tournament {
  rangeTimes: string;
  status: number | string;
  statusName?: TournamentStatusType;
  entryMode: string;
  entryRule: string;
  people: string | number;
  badgeImage: string;
}

// 玩家雷达数据
export type RadarData = {
  bet_amount: number;
  bet_times: number;
  net_win: number;
  win_rate: number;
  game_numbers: number;
};

export type RadarSeriesItem = {
  name?: string;
  value: Array<number>;
};

// 饼图数据子项
export type PieListItem = {
  game_id: number;
  game_name: string;
  value: string;
};

export type SeriesItem = {
  name: string;
  value: number;
};

// 雷达图参考坐标子项
export type RadarItem = {
  name: string;
  max: number;
};

// ==================== 常量定义 ====================

// 锦标赛状态枚举
export const TournamentStatus = {
  0: "waiting",
  1: "ongoing",
  2: "ended",
} as const;

// 报名模式：0=自动报名，1=手动报名
export const ENTRY_MODES = ["Auto Entry", "Manual Entry"] as const;

// 报名规则：0=不限制，1=充值，2=投注
export const ENTRY_RULES = [
  "No Requirement",
  "Deposit Requirement",
  "Wagering Requirement",
] as const;

// 时间周期类型
export const PERIOD_TYPES = {
  HOURLY: 0,
  DAILY: 1,
  CUSTOM: 2,
} as const;

// ==================== 主题配置 ====================

// Promo 5 主题 (Casino)
export const PROMO5_THEME: ThemeConfig = {
  name: "casino",
  colors: {
    primary: "#3413E3",
    secondary: "#9340fc",
    background: "#3413E3",
    gradient: "linear-gradient(180deg, #3313e2 0%, #9340fc 90%)",
    tabBackground: "#814eff",
    tabActive: "linear-gradient(90deg, #fef9e2 0%, #ffe8a7 100%)",
    tabActiveText: "#f7b500",
    tableBackground: "#ac47ff",
    tableRowBackground: "#fff4e5",
    rankNumber: "#a754ff",
    rankBonus: "#f5b608",
    footerBackground: `url(${casinoPageBg})`,
    infoBackground: "#641fff",
  },
  backgroundImage: `url(${casinoPageBg})`,
  backgroundPosition: "top -37px left 0",
};

// Promo 7 主题 (Jili)
export const PROMO7_THEME: ThemeConfig = {
  name: "jili",
  colors: {
    primary: "#ff9a3c",
    secondary: "#ffa519",
    background: "#ff9a3c",
    gradient: "linear-gradient(180deg, #ff7f01 0%, #ffa519 30%)",
    tabBackground: "#ff7d20",
    tabActive: "linear-gradient(to right, #fff9e1, #ffe7a5)",
    tabActiveText: "#ff6c02",
    tableBackground: "#ff8c01",
    tableRowBackground: "#fff4e5",
    rankNumber: "#ff6a00",
    rankBonus: "#f5b608",
    footerBackground: `url(${jiliPageBg})`,
    infoBackground: "#ff9e2e",
  },
  backgroundImage: `url(${jiliPageBg})`,
  backgroundPosition: "top -37px left 0",
};

// 默认主题
export const DEFAULT_THEME: ThemeConfig = {
  name: "default",
  colors: {
    primary: "#6C5CE7",
    secondary: "#A29BFE",
    background: "#6C5CE7",
    gradient: "linear-gradient(180deg, #6C5CE7 0%, #A29BFE 90%)",
    tabBackground: "#8B7ED8",
    tabActive: "linear-gradient(90deg, #fff9e1, #ffe7a5)",
    tabActiveText: "#6C5CE7",
    tableBackground: "#9B8CE8",
    tableRowBackground: "#fff4e5",
    rankNumber: "#8B7ED8",
    rankBonus: "#f5b608",
    footerBackground: "linear-gradient(90deg, #6C5CE7, #A29BFE)",
    infoBackground: "#8B7ED8",
  },
};

// 主题映射
export const THEME_MAP = {
  promo5: PROMO5_THEME,
  promo7: PROMO7_THEME,
  casino: PROMO5_THEME,
  jili: PROMO7_THEME,
  default: DEFAULT_THEME,
} as const;

// ==================== 资源配置 ====================

// 状态徽章映射
export const statusBadgeMap: Record<TournamentStatusType, string> = {
  waiting: waiting,
  ongoing: ongoing,
  ended: ended,
} as const;

// 锦标赛资源
export const tournamentAssets = {
  badges: {
    ended,
    waiting,
    ongoing,
  },
  icons: {
    coin: coinImg,
    noData: DefaultImage,
  },
} as const;

// ==================== 促销配置 ====================

// Promo 5 配置 (Casino)
export const PROMO5_CONFIG: PromoConfig = {
  id: "promo5",
  name: "Casino Ranking",
  theme: PROMO5_THEME,
  apiFunction: casinoRank,
  tabs: [
    { label: "Today", value: "1" },
    { label: "Yesterday", value: "2" },
  ],
  tableColumns: [
    { key: "rank", label: "Rank" },
    { key: "user", label: "User ID" },
    { key: "bet", label: "Bet" },
    { key: "bonus", label: "Bonus", class: "rank-bonus" },
  ],
  infoBannerImage: labaImg,
  helpRoute: "/promos/promo5_tip",
  showPodium: true,
  showInfoBanner: true,
  betLabel: "Bet",
  userLabel: "Me",
  autoRefresh: true,
  refreshInterval: 30000,
  customSettings: {
    enableRealTimeUpdates: true,
    showUserHighlight: true,
  },
};

// Promo 7 配置 (Jili)
export const PROMO7_CONFIG: PromoConfig = {
  id: "promo7",
  name: "Jili Slot Ranking",
  theme: PROMO7_THEME,
  apiFunction: rankSolt,
  tabs: [
    { label: "Today", value: "1" },
    { label: "Yesterday", value: "2" },
  ],
  tableColumns: [
    { key: "rank", label: "Rank" },
    { key: "user", label: "User ID" },
    { key: "bet", label: "Bet" },
    { key: "bonus", label: "Bonus", class: "rank-bonus" },
  ],
  infoBannerImage: labaImg,
  helpRoute: "/promos/promo7_tip",
  showPodium: true,
  showInfoBanner: true,
  betLabel: "Bet",
  userLabel: "Me",
  autoRefresh: true,
  refreshInterval: 30000,
  customSettings: {
    enableRealTimeUpdates: true,
    showUserHighlight: true,
  },
};

// 配置映射
export const PROMO_CONFIGS = {
  promo5: PROMO5_CONFIG,
  promo7: PROMO7_CONFIG,
  casino: PROMO5_CONFIG,
  jili: PROMO7_CONFIG,
} as const;

// 预定义的配置变体
export const PROMO_VARIANTS = {
  simple: {
    showPodium: false,
    showInfoBanner: false,
  },
  compact: {
    showInfoBanner: false,
    tableColumns: [
      { key: "rank", label: "Rank" },
      { key: "user", label: "User" },
      { key: "bonus", label: "Bonus", class: "rank-bonus" },
    ],
  },
  realtime: {
    autoRefresh: true,
    refreshInterval: 10000,
    customSettings: {
      enableRealTimeUpdates: true,
      showLiveIndicator: true,
    },
  },
} as const;

// ==================== 工具函数 ====================

/**
 * 获取主题配置
 * @param themeName 主题名称
 * @returns 主题配置对象
 */
export const getTheme = (themeName: keyof typeof THEME_MAP = "default"): ThemeConfig => {
  return THEME_MAP[themeName] || DEFAULT_THEME;
};

/**
 * 获取促销配置
 * @param promoId 促销ID
 * @returns 促销配置对象
 */
export function getPromoConfig(promoId: keyof typeof PROMO_CONFIGS): PromoConfig {
  return PROMO_CONFIGS[promoId] || PROMO5_CONFIG;
}

/**
 * 合并自定义配置
 * @param baseConfig 基础配置
 * @param customConfig 自定义配置
 * @returns 合并后的配置
 */
export function mergePromoConfig(
  baseConfig: PromoConfig,
  customConfig: Partial<PromoConfig>
): PromoConfig {
  return {
    ...baseConfig,
    ...customConfig,
    theme: customConfig.theme ? { ...baseConfig.theme, ...customConfig.theme } : baseConfig.theme,
    customSettings: {
      ...baseConfig.customSettings,
      ...customConfig.customSettings,
    },
  };
}

/**
 * 创建自定义促销配置
 * @param config 配置参数
 * @returns 促销配置对象
 */
export function createCustomPromoConfig(
  config: Partial<PromoConfig> & { id: string }
): PromoConfig {
  const baseConfig = PROMO5_CONFIG;
  return mergePromoConfig(baseConfig, config);
}

/**
 * 应用配置变体
 * @param baseConfig 基础配置
 * @param variant 变体名称
 * @returns 应用变体后的配置
 */
export function applyConfigVariant(
  baseConfig: PromoConfig,
  variant: keyof typeof PROMO_VARIANTS
): PromoConfig {
  const variantConfig = PROMO_VARIANTS[variant];
  return mergePromoConfig(baseConfig, variantConfig);
}

/**
 * CSS 变量生成器
 * @param theme 主题配置
 * @returns CSS 变量对象
 */
export const generateCSSVariables = (theme: ThemeConfig): Record<string, string> => {
  const cssVars: Record<string, string> = {};

  Object.entries(theme.colors).forEach(([key, value]) => {
    cssVars[`--promo-${key.replace(/([A-Z])/g, "-$1").toLowerCase()}`] = value;
  });

  if (theme.backgroundImage) {
    cssVars["--promo-background-image"] = theme.backgroundImage;
  }

  if (theme.backgroundPosition) {
    cssVars["--promo-background-position"] = theme.backgroundPosition;
  }

  return cssVars;
};
