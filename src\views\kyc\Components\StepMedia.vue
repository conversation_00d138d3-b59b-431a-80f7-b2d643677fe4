<script setup lang="ts">
import { useKycStore } from "@/stores/kyc";
import { S_MEDIA_TYPE } from "@/views/kyc/CONSTANT";

const kycStore = useKycStore();

const { fullFormData } = storeToRefs(kycStore);

const handleCountryConfirm = (e) => {
  fullFormData.value.account_no = "";
  kycStore.handleSelectConfirm("account_type", e);
};

const updateStore = (field: keyof typeof fullFormData.value, value: any) => {
  kycStore.updateDetailFormData(field, value);
};
</script>
<template>
  <div class="form">
    <div class="form-item">
      <label>Please Select Your Social Media Type</label>
      <ZSelect
        title="Select Account Type"
        :modelValue="fullFormData.account_type"
        :selectList="S_MEDIA_TYPE"
        @confirm="handleCountryConfirm"
        placeholder="Please select your account type
"
      ></ZSelect>
    </div>
    <ZFormField
      v-if="fullFormData.account_type"
      label="Please Enter Your Social Media Account"
      v-model="fullFormData.account_no"
      placeholder="Please enter your account"
      :maxLength="200"
      @input="(value) => updateStore('account_no', value)"
      labelAlias="Account"
    ></ZFormField>
  </div>
</template>

<style lang="scss" scoped>
.form {
  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}
</style>
