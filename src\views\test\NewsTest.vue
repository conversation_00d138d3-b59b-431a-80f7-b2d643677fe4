<template>
  <div class="news-test">
    <h2>新闻测试页面</h2>
    <p>这是一个测试页面，用于验证启动页路由跳转是否正常工作。</p>
    
    <div class="info">
      <h3>当前信息</h3>
      <p><strong>当前路由:</strong> {{ $route.fullPath }}</p>
      <p><strong>页面加载时间:</strong> {{ loadTime }}</p>
    </div>
    
    <div class="actions">
      <h3>测试操作</h3>
      <button @click="refreshPage">刷新页面</button>
      <button @click="goToHome">跳转到首页</button>
      <button @click="checkSessionStorage">检查 SessionStorage</button>
      <button @click="clearStorage">清除存储</button>
    </div>
    
    <div class="logs">
      <h3>日志</h3>
      <div v-for="(log, index) in logs" :key="index" class="log-item">
        {{ log }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const loadTime = ref('');
const logs = ref<string[]>([]);

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.unshift(`[${timestamp}] ${message}`);
};

const refreshPage = () => {
  addLog('用户点击刷新页面');
  window.location.reload();
};

const goToHome = () => {
  addLog('用户点击跳转到首页');
  router.push('/');
};

const checkSessionStorage = () => {
  const targetRoute = sessionStorage.getItem('targetRoute');
  const appInitialized = sessionStorage.getItem('appInitialized');
  
  addLog(`SessionStorage - targetRoute: ${targetRoute}`);
  addLog(`SessionStorage - appInitialized: ${appInitialized}`);
  
  console.log('SessionStorage 检查:', {
    targetRoute,
    appInitialized,
    allKeys: Object.keys(sessionStorage)
  });
};

const clearStorage = () => {
  sessionStorage.clear();
  addLog('已清除所有 SessionStorage');
};

onMounted(() => {
  loadTime.value = new Date().toLocaleTimeString();
  addLog('新闻测试页面加载完成');
  addLog(`当前路由: ${router.currentRoute.value.fullPath}`);
  
  // 自动检查 sessionStorage
  checkSessionStorage();
});
</script>

<style scoped lang="scss">
.news-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

h2, h3 {
  color: #333;
  margin-bottom: 15px;
}

.info, .actions, .logs {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  margin: 5px;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background-color: #0056b3;
  }
}

.logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 14px;
}
</style>
