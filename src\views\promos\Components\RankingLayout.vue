<template>
  <ZPage
    :request="initializeData"
    :backgroundColor="theme.colors.background"
    :narBarStyle="navBarStyle"
  >
    <template #right>
      <van-icon size="24" @click="$emit('help-click')" name="question-o" />
    </template>

    <div class="leaderboard" :style="leaderboardStyle">
      <!-- Header Section -->
      <div class="leaderboard-head">
        <!-- Tabs -->
        <RankingTabs
          v-model="activeTab"
          :tabs="finalConfig.tabs"
          :theme="theme"
          @update:modelValue="switchTab"
        />

        <!-- Info Banner -->
        <div v-if="finalConfig.showInfoBanner" class="info" :style="infoStyle">
          <img :src="finalConfig.infoBannerImage" alt="Info Banner" />
        </div>
      </div>

      <!-- Top 3 Podium -->
      <RankingPodium
        v-if="finalConfig.showPodium"
        :top-three-data="getTopThreeData"
        :bet-label="finalConfig.betLabel"
      />

      <!-- Ranking Table -->
      <RankingTable
        :ranking-list="currentRankingList"
        :columns="finalConfig.tableColumns"
        :theme="theme"
      />

      <!-- Footer -->
      <RankingFooter
        :user-rank="currentUserRank"
        :user-bet-amount="currentUserBetAmount"
        :user-award="currentUserAward"
        :user-label="finalConfig.userLabel"
        :theme="theme"
      />
    </div>
  </ZPage>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRankingData } from "../composables/useRankingData";
import { generateCSSVariables, getPromoConfig, type PromoConfig } from "../configs";
import RankingTabs from "./RankingTabs.vue";
import RankingPodium from "./RankingPodium.vue";
import RankingTable from "./RankingTable.vue";
import RankingFooter from "./RankingFooter.vue";

interface Props {
  // 使用预定义配置ID
  configId: "promo5" | "promo7" | "casino" | "jili";
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "help-click": [];
  "tab-change": [tab: string];
}>();

// 解析最终配置
const finalConfig = computed(() => {
  return getPromoConfig(props.configId);
});

// 使用排行榜数据管理
const {
  loading,
  error,
  activeTab,
  currentRankingList,
  currentUserRank,
  currentUserBetAmount,
  currentUserAward,
  getTopThreeData,
  initializeData,
  switchTab: switchTabInternal,
} = useRankingData(finalConfig.value.apiFunction, {
  autoRefresh: finalConfig.value.autoRefresh,
  refreshInterval: finalConfig.value.refreshInterval,
});

// 主题配置
const theme = computed(() => {
  return finalConfig.value.theme;
});

// 样式计算
const navBarStyle = computed(() => ({
  background: theme.value.colors.background,
  color: "#fff",
}));

const leaderboardStyle = computed(() => {
  const cssVars = generateCSSVariables(theme.value);
  return {
    background: theme.value.colors.gradient,
    backgroundImage: theme.value.backgroundImage,
    backgroundSize: "contain",
    backgroundPosition: theme.value.backgroundPosition || "top -37px left 0",
    ...cssVars,
  };
});

const infoStyle = computed(() => ({
  backgroundColor: theme.value.colors.infoBackground,
}));

// 切换标签页
const switchTab = (tab: string) => {
  switchTabInternal(tab);
  emit("tab-change", tab);
};
</script>

<style lang="scss" scoped>
.leaderboard {
  max-height: 90vh;
  font-family: "D-DIN";
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 0;
  overflow: hidden;
  margin-trim: none;

  .leaderboard-head {
    height: 120px;
  }
}

.info {
  margin: 0 20px 12px;
  border-radius: 20px;
  overflow: hidden;

  img {
    height: auto;
    width: 100%;
  }
}
</style>
