/**
 * 字体优先加载工具
 * 确保 iconfont 字体优先加载，减少其他资源的干扰
 */

export class FontPriority {
  private static instance: FontPriority;
  private iconFontLoaded = false;

  static getInstance(): FontPriority {
    if (!FontPriority.instance) {
      FontPriority.instance = new FontPriority();
    }
    return FontPriority.instance;
  }

  /**
   * 在页面头部注入字体预加载
   */
  injectFontPreload(): void {
    // 检查是否已经注入过
    if (document.querySelector('link[data-font-preload="iconfont"]')) {
      return;
    }

    // 动态创建预加载链接
    const preloadLinks = [
      {
        href: new URL("../assets/iconfonts/iconfont.woff2?t=1750902059451", import.meta.url).href,
        type: "font/woff2",
      },
      {
        href: new URL("../assets/iconfonts/iconfont.woff?t=1750902059451", import.meta.url).href,
        type: "font/woff",
      },
    ];

    preloadLinks.forEach((linkInfo, index) => {
      const link = document.createElement("link");
      link.rel = "preload";
      link.href = linkInfo.href;
      link.as = "font";
      link.type = linkInfo.type;
      link.crossOrigin = "anonymous";
      link.setAttribute("data-font-preload", "iconfont");

      // 插入到 head 的最前面，确保最高优先级
      if (document.head.firstChild) {
        document.head.insertBefore(link, document.head.firstChild);
      } else {
        document.head.appendChild(link);
      }

      console.log(`注入字体预加载: ${linkInfo.type}`);
    });
  }

  /**
   * 检查字体是否已加载
   */
  async checkFontLoaded(): Promise<boolean> {
    if (this.iconFontLoaded) {
      return true;
    }

    try {
      // 使用 document.fonts API 检查
      if ("fonts" in document) {
        await (document as any).fonts.load("16px iconfont");
        this.iconFontLoaded = true;
        console.log("iconfont 字体加载完成");
        return true;
      }
    } catch (error) {
      console.warn("字体加载检查失败:", error);
    }

    return false;
  }

  /**
   * 等待字体加载完成（带超时）
   */
  async waitForFont(timeout = 3000): Promise<boolean> {
    if (this.iconFontLoaded) {
      return true;
    }

    return new Promise((resolve) => {
      const startTime = Date.now();

      const checkInterval = setInterval(async () => {
        // 检查超时
        if (Date.now() - startTime > timeout) {
          clearInterval(checkInterval);
          console.warn("字体加载等待超时");
          resolve(false);
          return;
        }

        // 检查字体是否加载
        const loaded = await this.checkFontLoaded();
        if (loaded) {
          clearInterval(checkInterval);
          resolve(true);
        }
      }, 100);
    });
  }

  /**
   * 优化字体加载优先级
   */
  optimizeFontPriority(): void {
    // 1. 注入预加载
    this.injectFontPreload();

    // 2. 设置字体相关的 CSS 优先级
    this.boostFontCSS();

    // 3. 减少其他资源的优先级
    this.reduceLowPriorityResources();
  }

  /**
   * 提升字体 CSS 的优先级
   */
  private boostFontCSS(): void {
    // 查找 iconfont.css 的 link 标签
    const fontLinks = Array.from(document.querySelectorAll('link[rel="stylesheet"]')).filter(
      (link) => (link as HTMLLinkElement).href.includes("iconfont.css")
    );

    fontLinks.forEach((link) => {
      // 移动到 head 的最前面
      if (document.head.firstChild !== link) {
        document.head.insertBefore(link, document.head.firstChild);
      }

      // 设置高优先级
      (link as HTMLLinkElement).setAttribute("data-priority", "high");
      console.log("提升 iconfont.css 优先级");
    });
  }

  /**
   * 降低低优先级资源的加载优先级
   */
  private reduceLowPriorityResources(): void {
    // 延迟加载非关键的预加载资源
    const preloadLinks = Array.from(document.querySelectorAll('link[rel="preload"]')).filter(
      (link) => {
        const href = (link as HTMLLinkElement).href;
        // 保持字体预加载的高优先级
        return !href.includes("iconfont") && !href.includes("font");
      }
    );

    // 将非字体的预加载改为低优先级
    preloadLinks.forEach((link) => {
      const htmlLink = link as HTMLLinkElement;
      // 添加延迟，让字体先加载
      setTimeout(() => {
        if (htmlLink.parentNode) {
          htmlLink.parentNode.appendChild(htmlLink);
        }
      }, 100);
    });
  }

  /**
   * 完整的字体优先加载流程
   */
  async prioritizeIconFont(): Promise<boolean> {
    console.log("开始优化 iconfont 字体加载优先级...");

    // 1. 优化加载优先级
    this.optimizeFontPriority();

    // 2. 等待字体加载
    const loaded = await this.waitForFont(3000);

    if (loaded) {
      console.log("iconfont 字体优先加载成功");
    } else {
      console.warn("iconfont 字体加载超时，但继续执行");
    }

    return loaded;
  }

  /**
   * 检查字体是否已准备就绪
   */
  isFontReady(): boolean {
    return this.iconFontLoaded;
  }
}

// 导出单例实例
export const fontPriority = FontPriority.getInstance();
