import { h, render, AppContext, VNode } from "vue";
import BaseActionSheet from "./index.vue";

export type ActionSheetOptions = {
  title?: string;
  content?: string | VNode;
  showClose?: boolean;
  closeOnClickOverlay?: boolean;
  overlay?: boolean;
  showConfirmButton?: boolean;
  showCancelButton?: boolean;
  cancelText?: string;
  confirmText?: string;
  cancelDisabled?: boolean;
  confirmDisabled?: boolean;
  onConfirm?: (() => void) | (() => Promise<void>);
  onCancel?: (() => void) | (() => Promise<void>);
  onClose?: (() => void) | (() => Promise<void>);
  animationDuration?: number; // 动画时长，单位 ms
};

export function useActionSheet(appContext?: AppContext) {
  // 组件式调用
  const ActionSheetComponent = (options: ActionSheetOptions) => {
    const container = document.createElement("div");
    let isUnmounted = false;

    const onConfirm = async () => {
      try {
        if (options.onConfirm) {
          await options.onConfirm();
        }
        unmount();
      } catch (error) {
        console.error("ActionSheet confirm error:", error);
      }
    };

    const onCancel = async () => {
      try {
        if (options.onCancel) {
          await options.onCancel();
        }
        unmount();
      } catch (error) {
        console.error("ActionSheet cancel error:", error);
      }
    };

    const onClose = async () => {
      try {
        if (options.onClose) {
          await options.onClose();
        }
        unmount();
      } catch (error) {
        console.error("ActionSheet close error:", error);
      }
    };

    const unmount = () => {
      if (isUnmounted) return;
      isUnmounted = true;
      
      setTimeout(() => {
        render(null, container);
        if (container.parentNode) {
          container.parentNode.removeChild(container);
        }
      }, options.animationDuration || 300); // 默认动画时长 300ms
    };

    // 创建内容插槽
    const createContentSlot = () => {
      if (typeof options.content === 'string') {
        return h('div', { innerHTML: options.content });
      } else if (options.content) {
        return options.content;
      }
      return null;
    };

    const vnode = h(BaseActionSheet, {
      ...options,
      modelValue: true,
      onConfirm,
      onCancel,
      onClose,
      'onUpdate:modelValue': (value: boolean) => {
        if (!value) {
          unmount();
        }
      }
    }, {
      default: createContentSlot
    });

    if (appContext) {
      vnode.appContext = appContext;
    }
    
    render(vnode, container);
    document.body.appendChild(container);
    
    return {
      unmount,
    };
  };

  return ActionSheetComponent;
}

// 全局单例实例
let globalActionSheet: ReturnType<typeof useActionSheet> | null = null;

// 创建全局实例
export function createActionSheet(appContext?: AppContext) {
  if (!globalActionSheet) {
    globalActionSheet = useActionSheet(appContext);
  }
  return globalActionSheet;
}

// 便捷方法
export const actionSheet = {
  show: (options: ActionSheetOptions) => {
    const ActionSheetComponent = createActionSheet();
    return ActionSheetComponent(options);
  },
  
  // 确认对话框
  confirm: (options: Omit<ActionSheetOptions, 'showCancelButton'>) => {
    return actionSheet.show({
      ...options,
      showCancelButton: true,
      showConfirmButton: true,
    });
  },
  
  // 仅确认按钮
  alert: (options: Omit<ActionSheetOptions, 'showCancelButton'>) => {
    return actionSheet.show({
      ...options,
      showCancelButton: false,
      showConfirmButton: true,
    });
  },
  
  // 自定义内容
  custom: (options: ActionSheetOptions) => {
    return actionSheet.show({
      showCancelButton: false,
      showConfirmButton: false,
      ...options,
    });
  }
};

// 默认导出
export default actionSheet;
