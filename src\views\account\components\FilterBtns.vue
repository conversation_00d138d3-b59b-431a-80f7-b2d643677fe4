<template>
  <div class="filter-bg">
    <div class="filter-btns">
      <div
        class="filter-btn"
        v-for="item in btns"
        @click="click(item)"
        :class="{ active: activeItem === item || activeItem === item.value }"
        :key="item.value || item"
      >
        {{ item.text || item }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  btns: {
    type: Array,
    required: true,
  },
});
const activeItem = ref(props.btns[0] || "");
const emits = defineEmits(["click"]);

const click = (tar) => {
  emits("click", tar);
  activeItem.value = tar;
};
</script>

<style lang="scss" scoped>
.filter-bg {
  background-color: #fff;
  padding-top: 16px;
  width: 100vw;
  height: 69px;
}
.filter-btns {
  display: flex;
  margin: 0 12px;
  padding: 2px;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
  border-radius: 100px;
  overflow: hidden;
  background: #f5f5f5;
  .filter-btn {
    height: 33px;
    flex-grow: 1;
    text-align: center;
    padding: 0 10px;
    line-height: 33px;
    border-radius: 100px;
    &.active {
      background: #ac1140;
      color: #fff;
    }
  }
}
</style>
