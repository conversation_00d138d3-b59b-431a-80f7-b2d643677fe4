// useConfigurableCountdown.js
import { ref, onUnmounted } from "vue";

export const TimeUnit = {
  HOURS: "hours",
  MINUTES: "minutes",
  SECONDS: "seconds",
};
// 可配置的 expire_time 时间单位（小时、分钟或秒），并显示格式为 '00:00:00' 的倒计时  默认expire_time字段 单位为小时
export function useConfigurableCountdown(
  items,
  expireField = "expire_time",
  timeUnit = TimeUnit.HOURS
) {
  const timer = ref<NodeJS.Timeout | null>(null);

  // 将不同单位的时间转换为秒
  const convertToSeconds = (value) => {
    switch (timeUnit) {
      case TimeUnit.HOURS:
        return value * 3600;
      case TimeUnit.MINUTES:
        return value * 60;
      case TimeUnit.SECONDS:
      default:
        return value;
    }
  };

  const formatTime = (seconds) => {
    if (seconds <= 0) return "00:00:00";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      secs.toString().padStart(2, "0"),
    ].join(":");
  };

  const updateCountdown = () => {
    items.value.forEach((item) => {
      if (item[expireField] > 0) {
        // 转换为秒数处理
        const seconds = convertToSeconds(item[expireField]) - 1;
        item[expireField] = seconds >= 0 ? seconds / convertToSeconds(1) : 0;
        item.countdown = formatTime(convertToSeconds(item[expireField]));
      }
    });
  };

  const startCountdown = () => {
    // 初始化显示
    items.value.forEach((item) => {
      item.countdown = formatTime(convertToSeconds(item[expireField]));
    });

    timer.value = setInterval(updateCountdown, 1000);
  };

  const stopCountdown = () => {
    if (timer.value) {
      clearInterval(timer.value);
    }
  };

  onUnmounted(() => {
    stopCountdown();
  });

  return {
    startCountdown,
    stopCountdown,
  };
}
