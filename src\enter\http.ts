import http from "@/utils/http";
import { baseURL, getHeaders, handleHttpError } from "@/utils/base";

export function configureHttp() {
  http.setConfig({
    baseURL: baseURL(),
    headers: getHeaders,
    transformResult(res) {
      const {
        data: { code, data, message, msg },
      } = res ?? {};
      if (code === 200 || code === 0) {
        return data;
      }
      handleHttpError(res?.data);
      return Promise.reject(msg || message);
    },
    error: async (e) => {
      const { data } = e.response || {};
      handleHttpError(data);
    },
  });
}
