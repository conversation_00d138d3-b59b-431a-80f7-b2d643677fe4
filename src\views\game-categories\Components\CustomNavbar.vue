<script setup lang="ts">
import { ref, computed, watch, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import { debounce } from "lodash-es";
import type { CustomNavbarProps, CustomNavbarEmits } from "../types";
// 移除全局状态依赖，使用 props/emit 通信
import ProviderFilter from "@/components/ProviderFilter.vue";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";

// Props 和 Emits
const props = defineProps<CustomNavbarProps>();
const emit = defineEmits<CustomNavbarEmits>();

// 响应式状态
const visible = defineModel("visible", {
  type: Boolean,
  default: false,
});

const router = useRouter();
const route = useRoute();

// 使用现有的 Props 和 Emits 定义

// 本地状态（用于双向绑定）
const searchValue = ref("");
const checkedProviders = ref(["all"]);

// 计算属性
const hasSearchValue = computed(() => searchValue.value.trim().length > 0);

// 从 props 同步到本地状态
const syncFromProps = () => {
  searchValue.value = props.searchValue || "";
  checkedProviders.value = [...(props.selectedProviders || ["all"])];
};

// 监听 props 变化，同步到本地状态
watch(
  () => [props.searchValue, props.selectedProviders],
  () => {
    syncFromProps();
  },
  { deep: true, immediate: true }
);

// 监听本地搜索值变化，当清空时立即触发搜索
watch(
  () => searchValue.value,
  (newValue) => {
    // 当搜索值变为空时，立即触发搜索
    if (!newValue || !newValue.trim()) {
      emit("search", "");
    }
  }
);

// 实时搜索 - 修复：无论是否有值都要触发搜索
const handleSearchInput = () => {
  handleSearch();
};

const handleSearch = debounce(() => {
  const trimmedValue = searchValue.value.trim();
  emit("search", trimmedValue);
  // 搜索后的处理由父组件负责
}, 300);

const handleClearSearch = () => {
  searchValue.value = "";
  emit("search", "");
  // 清空搜索后的处理由父组件负责
};

const handleGoBack = () => {
  if (window.history.length > 1) {
    router.back();
  } else {
    router.push("/");
  }
};

const handleConfirmFilters = (selectedProviders: string[], providerDetails: any[]) => {
  // 立即更新本地状态（弹窗已经关闭了）
  checkedProviders.value = selectedProviders;
  visible.value = false;

  // 异步处理回调，避免阻塞 UI
  setTimeout(() => {
    props.confirm(selectedProviders, providerDetails);
    emit("confirm-filters", selectedProviders, providerDetails);
  }, 0);
};

const hasFilters = computed(() => {
  return checkedProviders.value.some((v) => v !== "all");
});

// 初始化时从 props 同步
onMounted(() => {
  syncFromProps();
});

// keep-alive 环境下，每次激活都同步状态
onActivated(() => {
  // 立即同步
  syncFromProps();
});

// 强制刷新状态的方法
const forceRefresh = () => {
  syncFromProps();
};

defineExpose({
  setSearchValue: (val: string) => {
    searchValue.value = val || "";
  },
  setCheckedProviders: (val: Array<string>) => {
    checkedProviders.value = val || ["all"];
  },
  syncFromProps,
  forceRefresh,
});
</script>

<template>
  <div class="nav-bar">
    <!-- 返回按钮 -->
    <ZIcon type="icon-fanhui1" @click="handleGoBack" color="" :size="28"></ZIcon>
    <!-- 搜索框 -->
    <van-search
      left-icon=""
      shape="round"
      background="#f4f7fd"
      v-model="searchValue"
      placeholder="Search Games"
      @clear="handleClearSearch"
      @input="handleSearchInput"
    >
      <template #right-icon v-if="!hasSearchValue">
        <ZIcon type="icon-search" @click="handleSearch" color=""></ZIcon>
      </template>
    </van-search>
    <!-- 筛选按钮 -->
    <div class="categories-btn">
      <ZIcon
        type="icon-shaixuan"
        @click.stop="() => (visible = true)"
        :color="hasFilters ? 'red' : ''"
      ></ZIcon>

      <ProviderFilter
        v-model:visible="visible"
        :confirm="handleConfirmFilters"
        :checkedProviders="checkedProviders"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #fff;
  .icon-fanhui1,
  .icon-shaixuan {
    cursor: pointer;
  }

  &:deep(.van-search) {
    flex: 1;
    height: 44px;
    margin: 0px 12px;
    border-radius: 999px;

    .van-search__content {
      background-color: #f4f7fd;
      transition: all 0.3s ease;
      border: 1px solid transparent;

      &:focus-within {
        // border-color: var(--primary-color, #007bff);
        // box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
      }
    }

    .van-field__control {
      font-size: 14px;
      color: #333;
    }

    .van-field__control::placeholder {
      color: #999;
    }
  }
}
</style>
