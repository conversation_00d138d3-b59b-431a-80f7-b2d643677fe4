// import { PN_VERIFY_TYPE, PN_TITLE } from './types'

// 更改手机号
// <ZVerifyDialog v-model:showDialog="showDialog" :verifyType="PN_VERIFY_TYPE.ChangePhoneNumber"/>

// 设置手机号
// <SetChangePhoneDialog v-model:showDialog="showDialog" :verifyType="PN_VERIFY_TYPE.SetPhoneNumber" />

// 首次设置支付密码
// <ZVerifyDialog v-model:showDialog="showDialog" :verifyType="PN_VERIFY_TYPE.SetPaymentPassword"></ZVerifyDialog>

// 更新支付密码
// <SetPaymentPasswordDialog v-model:showNextDialog="showDialog" :verifyType="PN_VERIFY_TYPE.SetPaymentPassword"></SetPaymentPasswordDialog>

// 添加账户弹窗
// <VerifyDialogWithdrawAccount v-model:showDialog="showDialog" :verifyType="PN_VERIFY_TYPE.AddWithdrawAccount"/>

// 编辑账户弹窗
// <VerifyDialogWithdrawAccount v-model:showDialog="showDialog" :verifyType="PN_VERIFY_TYPE.ChangeWithdrawAccount"/>
