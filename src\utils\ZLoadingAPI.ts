/**
 * ZLoading API 调用工具
 * 提供类似 vant showLoadingToast 的 API 方式使用 ZLoading 组件
 */

import { createApp, h } from "vue";
import ZLoading from "@/components/ZLoading/index.vue";
import ZIcon from "@/components/ZIcon/index.vue";

interface ZLoadingOptions {
  /** 是否禁止点击 */
  forbidClick?: boolean;
  /** 持续时间，0 表示不自动关闭 */
  duration?: number;
  /** 背景色 */
  backgroundColor?: string;
  /** z-index 层级 */
  zIndex?: number;
  /** 是否显示关闭按钮，默认显示 */
  showCloseButton?: boolean;
}

class ZLoadingManager {
  private container: HTMLElement | null = null;
  private timer: number | null = null;
  private app: any = null;
  private closeApp: any = null;

  /**
   * 显示 ZLoading
   */
  show(options: ZLoadingOptions = {}) {
    this.close();

    const {
      forbidClick = true,
      duration = 0,
      backgroundColor = "rgba(0, 0, 0, 0.7)",
      zIndex = 3000,
      showCloseButton = false,
    } = options;

    this.container = document.createElement("div");
    this.container.className = "z-loading-overlay";
    Object.assign(this.container.style, {
      position: "fixed",
      top: "0",
      left: "0",
      width: "100%",
      height: "100%",
      backgroundColor,
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      zIndex: zIndex.toString(),
      pointerEvents: forbidClick ? "auto" : "none",
    });

    // 创建 ZLoading 组件
    this.app = createApp(ZLoading);
    this.app.mount(this.container);

    // 如果需要显示关闭按钮，添加关闭按钮
    if (showCloseButton) {
      const closeButtonContainer = document.createElement("div");
      closeButtonContainer.style.cssText = `
        position: absolute;
        bottom: 25%;
        left: 50%;
        transform: translateX(-50%);
        cursor: pointer;
        padding: 10px;
        z-index: ${zIndex + 1};
      `;

      // 创建关闭按钮的 Vue 应用
      const self = this;
      const CloseButtonApp = {
        setup() {
          const handleClose = () => {
            self.close();
          };

          return () =>
            h(ZIcon, {
              type: "icon-guanbi2",
              color: "#fff",
              size: 28,
              onClick: handleClose,
            });
        },
      };

      const closeApp = createApp(CloseButtonApp);
      closeApp.mount(closeButtonContainer);
      this.container.appendChild(closeButtonContainer);

      // 保存关闭按钮应用的引用，用于清理
      this.closeApp = closeApp;
    }

    document.body.appendChild(this.container);

    if (duration > 0) {
      this.timer = window.setTimeout(() => {
        this.close();
      }, duration);
    }

    return this;
  }

  /**
   * 关闭 ZLoading
   */
  close() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    if (this.closeApp) {
      this.closeApp.unmount();
      this.closeApp = null;
    }

    if (this.app) {
      this.app.unmount();
      this.app = null;
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
    }

    // 清理样式（可选，因为样式可以复用）
    const existingStyles = document.querySelectorAll("style");
    existingStyles.forEach((style) => {
      if (style.textContent?.includes(".z-loading-content")) {
        style.remove();
      }
    });
  }
}

// 创建全局实例
const zLoadingManager = new ZLoadingManager();

/**
 * 显示 ZLoading
 * @param options 配置选项
 */
export const showZLoading = (options?: ZLoadingOptions) => {
  return zLoadingManager.show(options);
};

/**
 * 关闭 ZLoading
 */
export const closeZLoading = () => {
  zLoadingManager.close();
};

// 默认导出
export default {
  show: showZLoading,
  close: closeZLoading,
};
